import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:url_launcher/url_launcher.dart';
import '../services/google_search_service.dart';

class WebSearchResults extends StatelessWidget {
  final GoogleSearchResult searchResult;
  final String searchQuery;
  final bool isUserMessage;

  const WebSearchResults({
    super.key,
    required this.searchResult,
    required this.searchQuery,
    this.isUserMessage = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Search header
        _buildSearchHeader(),
        const SizedBox(height: 12),

        // Search results
        if (searchResult.items.isNotEmpty) ...[
          ...searchResult.items.map((item) => _buildSearchResultItem(item)),
        ] else ...[
          _buildNoResultsMessage(),
        ],

        const SizedBox(height: 8),
      ],
    );
  }

  Widget _buildSearchHeader() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isUserMessage
            ? Colors.white.withOpacity(0.1)
            : const Color(0xFF0D76FF).withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isUserMessage
              ? Colors.white.withOpacity(0.3)
              : const Color(0xFF0D76FF).withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.search,
            size: 16,
            color: isUserMessage
                ? Colors.white.withOpacity(0.8)
                : const Color(0xFF0D76FF),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'Web search results for "$searchQuery"',
              style: GoogleFonts.instrumentSans(
                fontSize: 13,
                fontWeight: FontWeight.w600,
                color: isUserMessage
                    ? Colors.white.withOpacity(0.9)
                    : const Color(0xFF4A5568),
              ),
            ),
          ),
          if (searchResult.totalResults > 0) ...[
            Text(
              '${searchResult.totalResults} results',
              style: GoogleFonts.instrumentSans(
                fontSize: 11,
                color: isUserMessage
                    ? Colors.white.withOpacity(0.7)
                    : const Color(0xFF718096),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSearchResultItem(GoogleSearchItem item) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isUserMessage
            ? Colors.white.withOpacity(0.05)
            : Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isUserMessage
              ? Colors.white.withOpacity(0.2)
              : const Color(0xFFE2E8F0),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title (clickable)
          GestureDetector(
            onTap: () => _launchUrl(item.link),
            child: Text(
              item.title,
              style: GoogleFonts.instrumentSans(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: isUserMessage ? Colors.white : const Color(0xFF0D76FF),
                decoration: TextDecoration.underline,
                decorationColor:
                    isUserMessage ? Colors.white : const Color(0xFF0D76FF),
              ),
            ),
          ),
          const SizedBox(height: 4),

          // Display URL
          if (item.displayLink != null) ...[
            Text(
              item.displayLink!,
              style: GoogleFonts.instrumentSans(
                fontSize: 12,
                color: isUserMessage
                    ? Colors.white.withOpacity(0.7)
                    : const Color(0xFF10B981),
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 6),
          ],

          // Snippet
          Text(
            item.snippet,
            style: GoogleFonts.instrumentSans(
              fontSize: 13,
              color: isUserMessage
                  ? Colors.white.withOpacity(0.9)
                  : const Color(0xFF4A5568),
              height: 1.4,
            ),
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),

          const SizedBox(height: 8),

          // Visit link button
          GestureDetector(
            onTap: () => _launchUrl(item.link),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: isUserMessage
                    ? Colors.white.withOpacity(0.2)
                    : const Color(0xFF0D76FF).withOpacity(0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: isUserMessage
                      ? Colors.white.withOpacity(0.4)
                      : const Color(0xFF0D76FF).withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.open_in_new,
                    size: 12,
                    color: isUserMessage
                        ? Colors.white.withOpacity(0.8)
                        : const Color(0xFF0D76FF),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Visit',
                    style: GoogleFonts.instrumentSans(
                      fontSize: 11,
                      fontWeight: FontWeight.w500,
                      color: isUserMessage
                          ? Colors.white.withOpacity(0.8)
                          : const Color(0xFF0D76FF),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNoResultsMessage() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isUserMessage
            ? Colors.white.withOpacity(0.05)
            : Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isUserMessage
              ? Colors.white.withOpacity(0.2)
              : const Color(0xFFE2E8F0),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            size: 16,
            color: isUserMessage
                ? Colors.white.withOpacity(0.7)
                : const Color(0xFF718096),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'No results found for "$searchQuery". Try different search terms.',
              style: GoogleFonts.instrumentSans(
                fontSize: 13,
                color: isUserMessage
                    ? Colors.white.withOpacity(0.8)
                    : const Color(0xFF718096),
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _launchUrl(String url) async {
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );
      } else {
        throw Exception('Could not launch $url');
      }
    } catch (error) {
      debugPrint('Error launching URL: $error');
      // Could show a snackbar or toast here if needed
    }
  }
}
