import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'optimized_network_image.dart';

class SwipeableCard extends StatefulWidget {
  final Map<String, dynamic> destination;
  final VoidCallback? onSwipeLeft;
  final VoidCallback? onSwipeRight;
  final bool isTopCard;

  const SwipeableCard({
    super.key,
    required this.destination,
    this.onSwipeLeft,
    this.onSwipeRight,
    this.isTopCard = false,
  });

  @override
  State<SwipeableCard> createState() => _SwipeableCardState();
}

class _SwipeableCardState extends State<SwipeableCard>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AnimationController _scaleController;
  late Animation<double> _rotationAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<Offset> _positionAnimation;

  Offset _dragOffset = Offset.zero;
  bool _isDragging = false;
  double _dragProgress = 0.0;

  static const double _swipeThreshold = 100.0;
  static const double _maxRotation = 0.3;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 1.0, // Start at full scale instead of 0
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeOut, // Use simpler curve
    ));

    _positionAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    // Always start the scale animation
    _scaleController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  void _onPanStart(DragStartDetails details) {
    setState(() {
      _isDragging = true;
    });
  }

  void _onPanUpdate(DragUpdateDetails details) {
    setState(() {
      _dragOffset += details.delta;
      _dragProgress = (_dragOffset.dx.abs() / _swipeThreshold).clamp(0.0, 1.0);
    });
  }

  void _onPanEnd(DragEndDetails details) {
    setState(() {
      _isDragging = false;
    });

    final velocity = details.velocity.pixelsPerSecond;
    final isSwipeRight = _dragOffset.dx > _swipeThreshold || velocity.dx > 500;
    final isSwipeLeft = _dragOffset.dx < -_swipeThreshold || velocity.dx < -500;

    if (isSwipeRight) {
      _swipeRight();
    } else if (isSwipeLeft) {
      _swipeLeft();
    } else {
      _resetPosition();
    }
  }

  void _swipeRight() {
    HapticFeedback.lightImpact();

    _rotationAnimation = Tween<double>(
      begin: _getRotation(),
      end: _maxRotation,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _positionAnimation = Tween<Offset>(
      begin: _dragOffset,
      end: Offset(MediaQuery.of(context).size.width + 100, _dragOffset.dy),
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _animationController.forward().then((_) {
      widget.onSwipeRight?.call();
    });
  }

  void _swipeLeft() {
    HapticFeedback.lightImpact();

    _rotationAnimation = Tween<double>(
      begin: _getRotation(),
      end: -_maxRotation,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _positionAnimation = Tween<Offset>(
      begin: _dragOffset,
      end: Offset(-MediaQuery.of(context).size.width - 100, _dragOffset.dy),
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _animationController.forward().then((_) {
      widget.onSwipeLeft?.call();
    });
  }

  void _resetPosition() {
    _rotationAnimation = Tween<double>(
      begin: _getRotation(),
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _positionAnimation = Tween<Offset>(
      begin: _dragOffset,
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _animationController.forward().then((_) {
      setState(() {
        _dragOffset = Offset.zero;
        _dragProgress = 0.0;
      });
      _animationController.reset();
    });
  }

  double _getRotation() {
    return (_dragOffset.dx / MediaQuery.of(context).size.width) * _maxRotation;
  }

  Color _getOverlayColor() {
    if (_dragOffset.dx > 0) {
      return Colors.green.withOpacity(_dragProgress * 0.3);
    } else if (_dragOffset.dx < 0) {
      return Colors.red.withOpacity(_dragProgress * 0.3);
    }
    return Colors.transparent;
  }

  String _getOverlayText() {
    if (_dragOffset.dx > _swipeThreshold * 0.5) {
      return 'YES';
    } else if (_dragOffset.dx < -_swipeThreshold * 0.5) {
      return 'MEH';
    }
    return '';
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_animationController, _scaleController]),
      builder: (context, child) {
        final currentOffset =
            _isDragging ? _dragOffset : _positionAnimation.value;
        final currentRotation =
            _isDragging ? _getRotation() : _rotationAnimation.value;

        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Transform.translate(
            offset: currentOffset,
            child: Transform.rotate(
              angle: currentRotation,
              child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onPanStart: widget.isTopCard ? _onPanStart : null,
                onPanUpdate: widget.isTopCard ? _onPanUpdate : null,
                onPanEnd: widget.isTopCard ? _onPanEnd : null,
                child: Container(
                  width: double.infinity,
                  height: double.infinity,
                  margin: const EdgeInsets.symmetric(horizontal: 20),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 20,
                        offset: const Offset(0, 10),
                      ),
                    ],
                  ),
                  child: Stack(
                    children: [
                      // Main card content
                      ClipRRect(
                        borderRadius: BorderRadius.circular(20),
                        child: Container(
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Image section
                              Expanded(
                                flex: 3,
                                child: Container(
                                  width: double.infinity,
                                  decoration: const BoxDecoration(
                                    borderRadius: BorderRadius.vertical(
                                      top: Radius.circular(20),
                                    ),
                                  ),
                                  child: ClipRRect(
                                    borderRadius: const BorderRadius.vertical(
                                      top: Radius.circular(20),
                                    ),
                                    child: OptimizedNetworkImage(
                                      imageUrl:
                                          widget.destination['image'] ?? '',
                                      fit: BoxFit.cover,
                                      width: double.infinity,
                                      height: double.infinity,
                                      borderRadius: const BorderRadius.vertical(
                                        top: Radius.circular(20),
                                      ),
                                      enableFadeIn: true,
                                      fadeInDuration:
                                          const Duration(milliseconds: 300),
                                      cacheWidth: 600,
                                      cacheHeight: 400,
                                    ),
                                  ),
                                ),
                              ),
                              // Content section
                              Container(
                                height: 100, // Fixed height to prevent overflow
                                padding: const EdgeInsets.all(16),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      widget.destination['name'],
                                      style: GoogleFonts.instrumentSans(
                                        fontSize: 20,
                                        fontWeight: FontWeight.bold,
                                        color: const Color(0xFF2D3748),
                                      ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      widget.destination['location'],
                                      style: GoogleFonts.instrumentSans(
                                        fontSize: 14,
                                        color: const Color(0xFF718096),
                                      ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    const SizedBox(height: 8),
                                    Row(
                                      children: [
                                        const Icon(
                                          Icons.star,
                                          color: Colors.amber,
                                          size: 14,
                                        ),
                                        const SizedBox(width: 4),
                                        Text(
                                          widget.destination['rating']
                                              .toString(),
                                          style: GoogleFonts.instrumentSans(
                                            fontSize: 12,
                                            fontWeight: FontWeight.w600,
                                            color: const Color(0xFF2D3748),
                                          ),
                                        ),
                                        const SizedBox(width: 12),
                                        const Icon(
                                          Icons.location_on,
                                          color: Color(0xFF0D76FF),
                                          size: 14,
                                        ),
                                        const SizedBox(width: 4),
                                        Text(
                                          '${widget.destination['distance']} km',
                                          style: GoogleFonts.instrumentSans(
                                            fontSize: 12,
                                            color: const Color(0xFF718096),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      // Overlay for swipe feedback
                      if (_isDragging && _getOverlayText().isNotEmpty)
                        Container(
                          decoration: BoxDecoration(
                            color: _getOverlayColor(),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Center(
                            child: Text(
                              _getOverlayText(),
                              style: GoogleFonts.instrumentSans(
                                fontSize: 48,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      // Invisible overlay to ensure gesture detection
                      if (widget.isTopCard)
                        Positioned.fill(
                          child: Container(
                            color: Colors.transparent,
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
