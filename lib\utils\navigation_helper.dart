import 'package:flutter/material.dart';
import '../screens/onboarding_screen.dart';
import '../widgets/auth_wrapper.dart';

class NavigationHelper {
  /// Navigate to onboarding screen with slide-in animation (Figma-style)
  /// Clears the entire navigation stack to prevent back navigation
  static void navigateToOnboarding(BuildContext context) {
    Navigator.of(context).pushAndRemoveUntil(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const OnboardingScreen(),
        transitionDuration:
            const Duration(milliseconds: 250), // Reduced from 300ms
        reverseTransitionDuration:
            const Duration(milliseconds: 250), // Reduced from 300ms
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          // Slide-in from right (Figma-style)
          const begin = Offset(1.0, 0.0);
          const end = Offset.zero;
          const curve = Curves.easeInOut;

          var tween = Tween(begin: begin, end: end).chain(
            CurveTween(curve: curve),
          );

          return SlideTransition(
            position: animation.drive(tween),
            child: child,
          );
        },
      ),
      (route) => false, // Remove all previous routes
    );
  }

  /// Navigate to AuthWrapper with slide-in animation
  /// This allows AuthWrapper to re-evaluate the authentication state
  static void navigateToAuthWrapper(BuildContext context) {
    Navigator.of(context).pushAndRemoveUntil(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const AuthWrapper(),
        transitionDuration:
            const Duration(milliseconds: 250), // Reduced from 300ms
        reverseTransitionDuration:
            const Duration(milliseconds: 250), // Reduced from 300ms
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          // Slide-in from right (Figma-style)
          const begin = Offset(1.0, 0.0);
          const end = Offset.zero;
          const curve = Curves.easeInOut;

          var tween = Tween(begin: begin, end: end).chain(
            CurveTween(curve: curve),
          );

          return SlideTransition(
            position: animation.drive(tween),
            child: child,
          );
        },
      ),
      (route) => false, // Remove all previous routes
    );
  }

  /// Handle logout navigation based on user type
  /// For guest users: Navigate directly to onboarding
  /// For regular users: Navigate to AuthWrapper for re-evaluation
  static void handleLogoutNavigation(BuildContext context,
      {bool isGuest = false}) {
    if (isGuest) {
      // Guest users go directly to onboarding
      navigateToOnboarding(context);
    } else {
      // Regular users go to AuthWrapper for re-evaluation
      navigateToAuthWrapper(context);
    }
  }

  /// Create simplified slide-in page route (Figma-style) for better performance
  static PageRouteBuilder<T> createSlideRoute<T extends Object?>(
    Widget page, {
    Duration duration = const Duration(milliseconds: 250), // Reduced from 300ms
    Offset begin = const Offset(1.0, 0.0),
    Offset end = Offset.zero,
    Curve curve = Curves.easeInOut,
  }) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration,
      reverseTransitionDuration: duration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        var tween = Tween(begin: begin, end: end).chain(
          CurveTween(curve: curve),
        );

        return SlideTransition(
          position: animation.drive(tween),
          child: child,
        );
      },
    );
  }

  /// Create simplified fade-up page route (bottom to top) for better performance
  static PageRouteBuilder<T> createFadeUpRoute<T extends Object?>(
    Widget page, {
    Duration duration = const Duration(milliseconds: 200), // Reduced from 300ms
  }) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration,
      reverseTransitionDuration: duration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(0.0, 0.3); // Reduced from 1.0 for lighter effect
        const end = Offset.zero;
        const curve = Curves.easeInOut;

        var tween = Tween(begin: begin, end: end).chain(
          CurveTween(curve: curve),
        );

        return SlideTransition(
          position: animation.drive(tween),
          child: FadeTransition(
            opacity: animation,
            child: child,
          ),
        );
      },
    );
  }
}
