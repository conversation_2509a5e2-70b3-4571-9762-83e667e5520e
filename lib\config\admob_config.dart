import 'dart:io';
import 'package:flutter/foundation.dart';

/// Configuration class for Google AdMob ad unit IDs
class AdMobConfig {
  // Test ad unit IDs (provided by Google for testing)
  static const String _testInterstitialAdUnitId = 'ca-app-pub-9901827412904595/6724142639';
  
  // Production ad unit IDs - REPLACE THESE WITH YOUR ACTUAL AD UNIT IDs
  // Get these from your Google AdMob console after setting up your app
  static const String _androidInterstitialAdUnitId = 'ca-app-pub-9901827412904595/6724142639';
  static const String _iosInterstitialAdUnitId = 'ca-app-pub-XXXXXXXXXXXXXXXX/XXXXXXXXXX';

  /// Get the appropriate interstitial ad unit ID based on platform and build mode
  static String get interstitialAdUnitId {
    // Always use test ads in debug mode
    if (kDebugMode) {
      return _testInterstitialAdUnitId;
    }
    
    // Use production ads in release mode
    if (Platform.isAndroid) {
      return _androidInterstitialAdUnitId;
    } else if (Platform.isIOS) {
      return _iosInterstitialAdUnitId;
    } else {
      throw UnsupportedError('Platform not supported for ads');
    }
  }

  /// Test device IDs for development
  /// Add your device IDs here to see test ads during development
  /// You can find your device ID in the console logs when running the app
  static const List<String> testDeviceIds = [
    // Add your test device IDs here
    // Example: '33BE2250B43518CCDA7DE426D04EE231'
  ];

  /// Ad frequency configuration
  static const int adFrequency = 7; // Show ad every 7 swipes

  /// Ad loading timeout in seconds
  static const int adLoadTimeoutSeconds = 30;

  /// Whether to show ads in debug mode (useful for testing)
  static const bool showAdsInDebug = true;

  /// Validate configuration
  static bool get isConfigurationValid {
    if (kDebugMode) {
      // In debug mode, test ads are always valid
      return true;
    }
    
    // In release mode, check if production ad unit IDs are configured
    if (Platform.isAndroid) {
      return _androidInterstitialAdUnitId != 'ca-app-pub-9901827412904595/6724142639';
    } else if (Platform.isIOS) {
      return _iosInterstitialAdUnitId != 'ca-app-pub-XXXXXXXXXXXXXXXX/XXXXXXXXXX';
    }
    
    return false;
  }

  /// Get configuration summary for debugging
  static Map<String, dynamic> get configSummary {
    return {
      'platform': Platform.operatingSystem,
      'isDebugMode': kDebugMode,
      'adUnitId': interstitialAdUnitId,
      'adFrequency': adFrequency,
      'isConfigurationValid': isConfigurationValid,
      'testDeviceIds': testDeviceIds,
      'showAdsInDebug': showAdsInDebug,
    };
  }
}
