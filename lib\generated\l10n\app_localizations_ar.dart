/// Generated file. Do not edit.
///
/// This file contains the localized strings for the TripWiseGo app.
/// To add a new localized string, add it to the appropriate .arb file
/// in the lib/l10n directory and run `flutter gen-l10n`.


import 'app_localizations.dart';

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get appTitle => 'TripWiseGo';

  @override
  String get welcome => 'مرحباً';

  @override
  String get guestUser => 'مستخدم ضيف';

  @override
  String get readyForAdventure => 'جاهز لمغامرتك القادمة';

  @override
  String get exploringAsGuest => 'استكشاف العالم كضيف';

  @override
  String get editProfile => 'تعديل الملف الشخصي';

  @override
  String get saveChanges => 'حفظ التغييرات';

  @override
  String get cancel => 'إلغاء';

  @override
  String get username => 'اسم المستخدم';

  @override
  String get email => 'البريد الإلكتروني';

  @override
  String get profileUpdatedSuccessfully => 'تم تحديث الملف الشخصي بنجاح!';

  @override
  String failedToUpdateProfile(String error) {
    return 'فشل في تحديث الملف الشخصي: $error';
  }

  @override
  String get profilePictureUpdatedSuccessfully => 'تم تحديث صورة الملف الشخصي بنجاح!';

  @override
  String failedToUploadImage(String error) {
    return 'فشل في رفع الصورة: $error';
  }

  @override
  String get profileEditingNotAvailableForGuests => 'تعديل الملف الشخصي غير متاح للمستخدمين الضيوف';

  @override
  String get profilePictureEditingNotAvailableForGuests => 'تعديل صورة الملف الشخصي غير متاح للمستخدمين الضيوف';

  @override
  String get usernameCannotBeEmpty => 'لا يمكن أن يكون اسم المستخدم فارغاً';

  @override
  String get usernameMustBeBetween2And30Characters => 'يجب أن يكون اسم المستخدم بين 2 و 30 حرفاً';

  @override
  String get plan => 'خطة';

  @override
  String get termsOfService => 'شروط الخدمة';

  @override
  String get language => 'اللغة';

  @override
  String get privacyPolicy => 'سياسة الخصوصية';

  @override
  String get support => 'الدعم';

  @override
  String get helpCenter => 'مركز المساعدة';

  @override
  String get contactUs => 'اتصل بنا';

  @override
  String get helpSupport => 'Help & Support';

  @override
  String get howCanWeHelpYou => 'How can we help you?';

  @override
  String get helpSupportGreeting => 'Hi! I\'m here to help you with TripwiseGO. You can ask me about app features, troubleshooting, or report any issues you\'re experiencing.';

  @override
  String get helpSupportWelcome => 'Welcome to TripwiseGO Support! Here are some things I can help you with:';

  @override
  String get helpSupportFeatures => '• App features and how to use them\n• Navigation and account help\n• Troubleshooting common issues\n• Reporting bugs or problems';

  @override
  String get helpSupportAskQuestion => 'Feel free to ask me anything or describe any issues you\'re having!';

  @override
  String get reportIssue => 'Report Issue';

  @override
  String get reportBug => 'Report Bug';

  @override
  String get reportProblem => 'Report Problem';

  @override
  String get issueCategory => 'Issue Category';

  @override
  String get bugReport => 'Bug Report';

  @override
  String get featureRequest => 'Feature Request';

  @override
  String get generalFeedback => 'General Feedback';

  @override
  String get accountIssue => 'Account Issue';

  @override
  String get technicalProblem => 'Technical Problem';

  @override
  String get yourName => 'Your Name';

  @override
  String get yourEmail => 'Your Email';

  @override
  String get issueDescription => 'Issue Description';

  @override
  String get describeIssueDetail => 'Please describe the issue in detail';

  @override
  String get optionalScreenshot => 'Screenshot (Optional)';

  @override
  String get submitReport => 'Submit Report';

  @override
  String get reportSubmitted => 'Report Submitted';

  @override
  String get reportSubmittedSuccess => 'Thank you! Your report has been submitted successfully. We\'ll look into it and get back to you if needed.';

  @override
  String get reportSubmissionFailed => 'Failed to submit report. Please try again later.';

  @override
  String get nameRequired => 'Name is required';

  @override
  String get emailRequired => 'Email is required';

  @override
  String get validEmailRequired => 'Please enter a valid email address';

  @override
  String get descriptionRequired => 'Issue description is required';

  @override
  String get selectCategory => 'Please select a category';

  @override
  String get subscription => 'Subscription';

  @override
  String get subscriptionActive => 'Subscription Active!';

  @override
  String get welcomeToPremium => 'Welcome to TripwiseGO Premium! Enjoy unlimited access to all features.';

  @override
  String get currentPlan => 'Current Plan';

  @override
  String get trial => 'Trial';

  @override
  String get active => 'Active';

  @override
  String trialEndsIn(int days) {
    return 'Trial ends in $days days';
  }

  @override
  String renewsIn(int days) {
    return 'Renews in $days days';
  }

  @override
  String get yourBenefits => 'Your Benefits';

  @override
  String get startPlanningTrip => 'Start Planning Your Trip';

  @override
  String get manageSubscription => 'Manage Subscription';

  @override
  String get basic => 'Basic';

  @override
  String get premium => 'Premium';

  @override
  String get basicPlaceRecommendations => 'Basic Place Recommendations';

  @override
  String get locationRecommendationSwiping => 'Location Recommendation Swiping (20 swipes/day)';

  @override
  String get aiPoweredTravelPlanner => 'AI-Powered Travel Planner';

  @override
  String get unlimitedQuizzes => 'Unlimited Quizzes & Fun Facts';

  @override
  String get noAds => 'No-Ads';

  @override
  String get oneMonth => '1 Month';

  @override
  String get threeMonths => '3 Months';

  @override
  String get fiveMonths => '5 Months';

  @override
  String dayFreeTrial(int days) {
    return '$days-day free trial';
  }

  @override
  String get billedMonthly => 'Billed monthly';

  @override
  String get billedTwiceAnnually => 'Billed twice annually';

  @override
  String get billedAnnually => 'Billed annually';

  @override
  String get save45Percent => 'SAVE 45%';

  @override
  String get continueButton => 'Continue';

  @override
  String get termsAndConditions => 'Terms and Conditions';

  @override
  String get failedToSubscribe => 'Failed to subscribe. Please try again.';

  @override
  String get signOut => 'تسجيل الخروج';

  @override
  String get selectLanguage => 'اختر اللغة';

  @override
  String get chooseYourPreferredLanguage => 'اختر لغتك المفضلة';

  @override
  String get languageUpdatedSuccessfully => 'تم تحديث اللغة بنجاح!';

  @override
  String get home => 'الرئيسية';

  @override
  String get match => 'مطابقة';

  @override
  String get chat => 'محادثة';

  @override
  String get profile => 'الملف الشخصي';

  @override
  String get loading => 'جاري التحميل...';

  @override
  String get error => 'خطأ';

  @override
  String get retry => 'إعادة المحاولة';

  @override
  String get ok => 'موافق';

  @override
  String get yes => 'نعم';

  @override
  String get no => 'لا';

  @override
  String get save => 'حفظ';

  @override
  String get delete => 'حذف';

  @override
  String get edit => 'تعديل';

  @override
  String get add => 'إضافة';

  @override
  String get remove => 'إزالة';

  @override
  String get close => 'إغلاق';

  @override
  String get back => 'رجوع';

  @override
  String get next => 'التالي';

  @override
  String get previous => 'السابق';

  @override
  String get done => 'تم';

  @override
  String get search => 'بحث';

  @override
  String get noResultsFound => 'لم يتم العثور على نتائج';

  @override
  String get tryAgain => 'حاول مرة أخرى';

  @override
  String get somethingWentWrong => 'حدث خطأ ما';

  @override
  String get networkError => 'خطأ في الشبكة. يرجى التحقق من اتصالك.';

  @override
  String get serverError => 'خطأ في الخادم. يرجى المحاولة لاحقاً.';

  @override
  String get invalidInput => 'إدخال غير صحيح';

  @override
  String get required => 'مطلوب';

  @override
  String get optional => 'اختياري';

  @override
  String get itinerary => 'Itinerary';

  @override
  String get yourItineraries => 'Your Itineraries';

  @override
  String get startPlanningYourTrip => 'Start Planning Your Trip';

  @override
  String get tripPlanningFeaturesWillAppearHere => 'Your trip planning features will appear here. The persistent authentication is now working!';

  @override
  String get forYou => 'For You';

  @override
  String get liked => 'Liked';

  @override
  String get collab => 'Collab';

  @override
  String get collaborativeItinerary => 'Collaborative';

  @override
  String get endSession => 'End Session';

  @override
  String get logout => 'Logout';

  @override
  String logoutFailed(String error) {
    return 'Logout failed: $error';
  }

  @override
  String get noItineraryFound => 'لم يتم العثور على مسار رحلة';

  @override
  String get askAiToCreateTravelPlan => 'اطلب من الذكاء الاصطناعي إنشاء خطة سفر لك!';

  @override
  String get saturday => 'السبت';

  @override
  String get tuesday => 'الثلاثاء';

  @override
  String dayNumber(int number) {
    return 'اليوم $number';
  }

  @override
  String get itineraryOverview => 'نظرة عامة على المسار';

  @override
  String daysAndNights(int days, int nights) {
    return '$daysأ $nightsل';
  }

  @override
  String get hiImWanderlyAi => 'مرحباً، أنا Wanderly AI 🌏';

  @override
  String get yourTravelAiAssistant => 'مساعد السفر الذكي الخاص بك، كيف يمكنني مساعدتك اليوم؟';

  @override
  String get useThisBubbleChat => 'استخدم هذه المحادثة الفقاعية';

  @override
  String get aiAssistant => 'المساعد الذكي';

  @override
  String get chatHistory => 'تاريخ المحادثة';

  @override
  String get newChat => 'محادثة جديدة';

  @override
  String get addImage => 'إضافة صورة';

  @override
  String get camera => 'الكاميرا';

  @override
  String get gallery => 'المعرض';

  @override
  String get microphonePermissionRequired => 'مطلوب إذن الميكروفون للإدخال الصوتي';

  @override
  String get speechRecognitionNotAvailable => 'التعرف على الكلام غير متاح على هذا الجهاز';

  @override
  String get listening => 'يستمع...';

  @override
  String get deleteChat => 'حذف المحادثة';

  @override
  String get deleteChatConfirmation => 'هل أنت متأكد من أنك تريد حذف هذه المحادثة؟ لا يمكن التراجع عن هذا الإجراء.';

  @override
  String get chatDeletedSuccessfully => 'تم حذف المحادثة بنجاح';

  @override
  String get pleaseEnterSearchQuery => 'يرجى إدخال استعلام بحث';

  @override
  String get dailySearchLimitReached => 'تم الوصول إلى حد البحث اليومي. يمكنك إجراء 5 عمليات بحث يومياً.';

  @override
  String get searchingTheWeb => 'البحث في الويب...';

  @override
  String get webSearchModeActive => 'وضع البحث في الويب نشط';

  @override
  String get pleaseWaitWhileSearching => 'يرجى الانتظار بينما أبحث عن المعلومات';

  @override
  String get yourNextMessageWillSearch => 'رسالتك التالية ستبحث في الويب';

  @override
  String get disableWebSearch => 'تعطيل البحث في الويب';

  @override
  String get enableWebSearch => 'تمكين البحث في الويب';

  @override
  String get switchBackToAiChatMode => 'العودة إلى وضع المحادثة الذكية';

  @override
  String get searchWebForCurrentInfo => 'البحث في الويب عن المعلومات الحالية';

  @override
  String get pickImageFromGallery => 'اختيار صورة من المعرض';

  @override
  String get uploadImageForAiAnalysis => 'رفع صورة لتحليل الذكاء الاصطناعي';

  @override
  String get yourMessage => 'رسالتك';

  @override
  String get wanderlyAi => 'Wanderly AI';

  @override
  String get webSearch => 'البحث في الويب:';

  @override
  String get like => 'إعجاب';

  @override
  String get dislike => 'عدم إعجاب';

  @override
  String get copy => 'نسخ';

  @override
  String get regenerate => 'إعادة توليد';

  @override
  String get failedToSubmitFeedback => 'فشل في إرسال التعليقات. يرجى المحاولة مرة أخرى.';

  @override
  String get thankYouForFeedback => 'شكراً لك على تعليقاتك! 🙏';

  @override
  String get feedbackReceivedThanks => 'تم استلام التعليقات. شكراً لمساعدتنا في التحسين! 🚀';

  @override
  String get responseCopiedToClipboard => 'تم نسخ الرد إلى الحافظة';

  @override
  String get wanderlyAiIsTyping => 'Wanderly AI يكتب';

  @override
  String get stopGeneration => 'إيقاف التوليد';

  @override
  String get youHaveChatsLeft => 'لديك 10 محادثات متبقية';

  @override
  String get enterSearchQuery => 'أدخل استعلام البحث...';

  @override
  String get askMeAnythingOrLongPress => 'اسألني أي شيء أو اضغط مطولاً للتحدث...';

  @override
  String failedToPickImage(String error) {
    return 'فشل في اختيار الصورة: $error';
  }

  @override
  String get failedToAnalyzeImage => 'فشل في تحليل الصورة. يرجى المحاولة مرة أخرى.';

  @override
  String get responseGenerationStopped => 'تم إيقاف توليد الرد.';

  @override
  String get unknownDestination => 'وجهة غير معروفة';

  @override
  String get congratulations => 'Congratulations';

  @override
  String get itsAMatch => 'It\'s a match';

  @override
  String get travelVibesAligned => 'Travel vibes aligned pack\nyour bags, you\'ve got a match!';

  @override
  String get matchedPreferences => 'Matched Preferences:';

  @override
  String get addToItinerary => 'Add to Itinerary';

  @override
  String get keepSwiping => 'Keep swiping';

  @override
  String get versionInfo => 'Version Info';

  @override
  String get appVersion => 'App Version';

  @override
  String get buildNumber => 'Build Number';

  @override
  String get packageName => 'Package Name';

  @override
  String get appName => 'App Name';

  @override
  String get buildSignature => 'Build Signature';

  @override
  String get installerStore => 'Installer Store';

  @override
  String get deviceInfo => 'Device Information';

  @override
  String get operatingSystem => 'Operating System';

  @override
  String get flutterVersion => 'Flutter Version';

  @override
  String get dartVersion => 'Dart Version';

  @override
  String get leaveAReview => 'Leave a Review';

  @override
  String get rateOurApp => 'Rate Our App';

  @override
  String get enjoyingTripWiseGo => 'Enjoying TripWiseGo?';

  @override
  String get helpUsImproveByLeavingReview => 'Help us improve by leaving a review on the app store. Your feedback means a lot to us!';

  @override
  String get rateNow => 'Rate Now';

  @override
  String get maybeLater => 'Maybe Later';

  @override
  String get reviewFeatureNotAvailable => 'Review feature is only available in production builds';

  @override
  String get unableToOpenStore => 'Unable to open app store. Please try again later.';

  @override
  String get thankYouForReview => 'Thank you for taking the time to review our app!';
}
