class ErrorReport {
  final String id;
  final String userId;
  final String category;
  final String name;
  final String email;
  final String description;
  final String? screenshot;
  final DateTime timestamp;
  final String deviceInfo;
  final String appVersion;
  final bool isSynced;

  ErrorReport({
    required this.id,
    required this.userId,
    required this.category,
    required this.name,
    required this.email,
    required this.description,
    this.screenshot,
    required this.timestamp,
    required this.deviceInfo,
    required this.appVersion,
    this.isSynced = false,
  });

  /// Convert to CSV row for Google Sheets
  List<String> toCsvRow() {
    return [
      id,
      userId,
      category,
      name,
      email,
      description,
      screenshot ?? '',
      timestamp.toIso8601String(),
      deviceInfo,
      appVersion,
      isSynced.toString(),
    ];
  }

  /// Create from CSV row
  static ErrorReport fromCsvRow(List<String> row) {
    // Ensure row has enough columns
    while (row.length < 11) {
      row.add('');
    }

    return ErrorReport(
      id: row[0],
      userId: row[1],
      category: row[2],
      name: row[3],
      email: row[4],
      description: row[5],
      screenshot: row[6].isEmpty ? null : row[6],
      timestamp: DateTime.tryParse(row[7]) ?? DateTime.now(),
      deviceInfo: row[8],
      appVersion: row[9],
      isSynced: row[10].toLowerCase() == 'true',
    );
  }

  /// Create a copy with updated fields
  ErrorReport copyWith({
    String? id,
    String? userId,
    String? category,
    String? name,
    String? email,
    String? description,
    String? screenshot,
    DateTime? timestamp,
    String? deviceInfo,
    String? appVersion,
    bool? isSynced,
  }) {
    return ErrorReport(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      category: category ?? this.category,
      name: name ?? this.name,
      email: email ?? this.email,
      description: description ?? this.description,
      screenshot: screenshot ?? this.screenshot,
      timestamp: timestamp ?? this.timestamp,
      deviceInfo: deviceInfo ?? this.deviceInfo,
      appVersion: appVersion ?? this.appVersion,
      isSynced: isSynced ?? this.isSynced,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'category': category,
      'name': name,
      'email': email,
      'description': description,
      'screenshot': screenshot,
      'timestamp': timestamp.toIso8601String(),
      'deviceInfo': deviceInfo,
      'appVersion': appVersion,
      'isSynced': isSynced,
    };
  }

  /// Create from JSON
  static ErrorReport fromJson(Map<String, dynamic> json) {
    return ErrorReport(
      id: json['id'] ?? '',
      userId: json['userId'] ?? '',
      category: json['category'] ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      description: json['description'] ?? '',
      screenshot: json['screenshot'],
      timestamp: DateTime.tryParse(json['timestamp'] ?? '') ?? DateTime.now(),
      deviceInfo: json['deviceInfo'] ?? '',
      appVersion: json['appVersion'] ?? '',
      isSynced: json['isSynced'] ?? false,
    );
  }

  @override
  String toString() {
    return 'ErrorReport(id: $id, category: $category, name: $name, email: $email, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ErrorReport && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
