import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../config/supabase_config.dart';
import '../services/auth_service.dart';

class OAuthDebugHelper {
  static final SupabaseClient _supabase = SupabaseConfig.client;

  // Helper method to safely truncate strings
  static String _safeSubstring(String? text, int maxLength) {
    if (text == null || text.isEmpty) return 'null';
    if (text.length <= maxLength) return text;
    return '${text.substring(0, maxLength)}...';
  }

  // Log current authentication state
  static void logAuthState() {
    if (!kDebugMode) return;

    print('=== OAuth Debug Helper: Auth State ===');
    print('Is Signed In: ${AuthService.isSignedIn}');
    print('Has Valid Session: ${AuthService.hasValidSession}');

    final user = AuthService.currentUser;
    if (user != null) {
      print('User ID: ${user.id}');
      print('User Email: ${user.email}');
      print('User Provider: ${user.appMetadata['provider']}');
      print('User Created: ${user.createdAt}');
      print('User Last Sign In: ${user.lastSignInAt}');
    } else {
      print('User: null');
    }

    final session = AuthService.currentSession;
    if (session != null) {
      print('Session Access Token: ${_safeSubstring(session.accessToken, 20)}');
      print(
          'Session Refresh Token: ${_safeSubstring(session.refreshToken, 20)}');
      print(
          'Session Expires At: ${DateTime.fromMillisecondsSinceEpoch(session.expiresAt! * 1000)}');
      print('Session Token Type: ${session.tokenType}');
    } else {
      print('Session: null');
    }
    print('=====================================');
  }

  // Log OAuth callback parameters
  static void logOAuthCallback(Uri uri) {
    if (!kDebugMode) return;

    print('=== OAuth Debug Helper: Callback ===');
    print('Full URI: $uri');
    print('Scheme: ${uri.scheme}');
    print('Host: ${uri.host}');
    print('Path: ${uri.path}');
    print('Query: ${uri.query}');
    print('Fragment: ${uri.fragment}');

    // Parse query parameters
    final queryParams = uri.queryParameters;
    if (queryParams.isNotEmpty) {
      print('Query Parameters:');
      queryParams.forEach((key, value) {
        if (key.toLowerCase().contains('token') ||
            key.toLowerCase().contains('code')) {
          print('  $key: ${_safeSubstring(value, 10)}');
        } else {
          print('  $key: $value');
        }
      });
    }

    // Parse fragment parameters
    if (uri.fragment.isNotEmpty) {
      final fragmentParams = Uri.splitQueryString(uri.fragment);
      if (fragmentParams.isNotEmpty) {
        print('Fragment Parameters:');
        fragmentParams.forEach((key, value) {
          if (key.toLowerCase().contains('token') ||
              key.toLowerCase().contains('code')) {
            print('  $key: ${_safeSubstring(value, 10)}');
          } else {
            print('  $key: $value');
          }
        });
      }
    }
    print('====================================');
  }

  // Test deep link handling
  static void testDeepLink(String testUrl) {
    if (!kDebugMode) return;

    print('=== OAuth Debug Helper: Testing Deep Link ===');
    print('Test URL: $testUrl');

    try {
      final uri = Uri.parse(testUrl);
      logOAuthCallback(uri);

      // Check if this matches our expected scheme
      if (uri.scheme == 'io.supabase.tripwisego') {
        print('✅ URL scheme matches expected: ${uri.scheme}');

        if (uri.host == 'login-callback' ||
            uri.path.contains('login-callback')) {
          print('✅ Callback path matches expected');
        } else {
          print('❌ Callback path does not match expected');
        }
      } else {
        print('❌ URL scheme does not match expected: ${uri.scheme}');
      }
    } catch (error) {
      print('❌ Error parsing test URL: $error');
    }
    print('=============================================');
  }

  // Monitor auth state changes
  static void startAuthStateMonitoring() {
    if (!kDebugMode) return;

    print('=== OAuth Debug Helper: Starting Auth State Monitoring ===');

    AuthService.authStateChanges.listen((AuthState authState) {
      print('--- Auth State Change ---');
      print('Event: ${authState.event}');
      print('Session: ${authState.session != null ? 'Present' : 'Null'}');

      if (authState.session != null) {
        final session = authState.session!;
        print('User ID: ${session.user.id}');
        print('User Email: ${session.user.email}');
        print('Provider: ${session.user.appMetadata['provider']}');
      }
      print('------------------------');
    });
  }

  // Validate OAuth configuration
  static void validateOAuthConfig() {
    if (!kDebugMode) return;

    print('=== OAuth Debug Helper: Validating Configuration ===');

    // Check Supabase configuration
    print('Supabase URL: ${SupabaseConfig.url}');
    print('Supabase Anon Key: ${_safeSubstring(SupabaseConfig.anonKey, 20)}');

    // Check expected redirect URLs
    final expectedRedirectUrls = [
      'https://ktdstluymbpqejmjkpsg.supabase.co/auth/v1/callback',
      'io.supabase.tripwisego://login-callback/',
    ];

    print('Expected Redirect URLs:');
    for (final url in expectedRedirectUrls) {
      print('  - $url');
    }

    // Check current auth state
    logAuthState();

    print('===================================================');
  }

  // Generate test OAuth URLs
  static void generateTestUrls() {
    if (!kDebugMode) return;

    print('=== OAuth Debug Helper: Test URLs ===');

    final testUrls = [
      'io.supabase.tripwisego://login-callback/?code=test_auth_code&state=test_state',
      'io.supabase.tripwisego://login-callback/#access_token=test_token&token_type=bearer&expires_in=3600',
      'io.supabase.tripwisego://login-callback/?error=access_denied&error_description=User%20denied%20access',
      'https://ktdstluymbpqejmjkpsg.supabase.co/auth/v1/callback?code=test_web_code&state=test_state',
    ];

    print('Test URLs for manual testing:');
    for (int i = 0; i < testUrls.length; i++) {
      print('${i + 1}. ${testUrls[i]}');
    }

    print('Use these URLs to test deep link handling manually.');
    print('=====================================');
  }

  // Check session validity
  static Future<void> checkSessionValidity() async {
    if (!kDebugMode) return;

    print('=== OAuth Debug Helper: Session Validity Check ===');

    final session = AuthService.currentSession;
    if (session == null) {
      print('❌ No current session');
      return;
    }

    print('✅ Session exists');

    // Check expiration
    final expiresAt =
        DateTime.fromMillisecondsSinceEpoch(session.expiresAt! * 1000);
    final now = DateTime.now();
    final timeUntilExpiry = expiresAt.difference(now);

    if (timeUntilExpiry.isNegative) {
      print('❌ Session expired ${timeUntilExpiry.abs()} ago');
    } else {
      print('✅ Session valid for ${timeUntilExpiry}');
    }

    // Test session refresh
    try {
      print('Testing session refresh...');
      final refreshResponse = await AuthService.refreshSession();
      if (refreshResponse?.session != null) {
        print('✅ Session refresh successful');
      } else {
        print('❌ Session refresh failed');
      }
    } catch (error) {
      print('❌ Session refresh error: $error');
    }

    print('==============================================');
  }

  // Complete OAuth flow test
  static void runCompleteTest() {
    if (!kDebugMode) return;

    print('=== OAuth Debug Helper: Complete Test ===');
    validateOAuthConfig();
    generateTestUrls();
    startAuthStateMonitoring();
    checkSessionValidity();
    print('=========================================');
  }
}
