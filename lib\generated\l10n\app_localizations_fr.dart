/// Generated file. Do not edit.
///
/// This file contains the localized strings for the TripWiseGo app.
/// To add a new localized string, add it to the appropriate .arb file
/// in the lib/l10n directory and run `flutter gen-l10n`.


import 'app_localizations.dart';

/// The translations for French (`fr`).
class AppLocalizationsFr extends AppLocalizations {
  AppLocalizationsFr([String locale = 'fr']) : super(locale);

  @override
  String get appTitle => 'TripWiseGo';

  @override
  String get welcome => 'Bienvenue';

  @override
  String get guestUser => 'Utilisateur invité';

  @override
  String get readyForAdventure => 'Prêt pour votre prochaine aventure';

  @override
  String get exploringAsGuest => 'Explorer le monde en tant qu\'invité';

  @override
  String get editProfile => 'Modifier le profil';

  @override
  String get saveChanges => 'Enregistrer les modifications';

  @override
  String get cancel => 'Annuler';

  @override
  String get username => 'Nom d\'utilisateur';

  @override
  String get email => 'E-mail';

  @override
  String get profileUpdatedSuccessfully => 'Profil mis à jour avec succès !';

  @override
  String failedToUpdateProfile(String error) {
    return 'Échec de la mise à jour du profil : $error';
  }

  @override
  String get profilePictureUpdatedSuccessfully => 'Photo de profil mise à jour avec succès !';

  @override
  String failedToUploadImage(String error) {
    return 'Échec du téléchargement de l\'image : $error';
  }

  @override
  String get profileEditingNotAvailableForGuests => 'La modification du profil n\'est pas disponible pour les utilisateurs invités';

  @override
  String get profilePictureEditingNotAvailableForGuests => 'La modification de la photo de profil n\'est pas disponible pour les utilisateurs invités';

  @override
  String get usernameCannotBeEmpty => 'Le nom d\'utilisateur ne peut pas être vide';

  @override
  String get usernameMustBeBetween2And30Characters => 'Le nom d\'utilisateur doit contenir entre 2 et 30 caractères';

  @override
  String get plan => 'Planifier';

  @override
  String get termsOfService => 'Conditions d\'utilisation';

  @override
  String get language => 'Langue';

  @override
  String get privacyPolicy => 'Politique de Confidentialité';

  @override
  String get support => 'Support';

  @override
  String get helpCenter => 'Centre d\'aide';

  @override
  String get contactUs => 'Nous contacter';

  @override
  String get helpSupport => 'Aide et Support';

  @override
  String get howCanWeHelpYou => 'Comment pouvons-nous vous aider ?';

  @override
  String get helpSupportGreeting => 'Salut ! Je suis là pour vous aider avec TripwiseGO. Vous pouvez me poser des questions sur les fonctionnalités de l\'application, le dépannage, ou signaler des problèmes que vous rencontrez.';

  @override
  String get helpSupportWelcome => 'Bienvenue au Support TripwiseGO ! Voici quelques choses avec lesquelles je peux vous aider :';

  @override
  String get helpSupportFeatures => '• Fonctionnalités de l\'application et comment les utiliser\n• Navigation et aide au compte\n• Dépannage des problèmes courants\n• Signalement de bugs ou problèmes';

  @override
  String get helpSupportAskQuestion => 'N\'hésitez pas à me poser des questions ou à décrire les problèmes que vous rencontrez !';

  @override
  String get reportIssue => 'Signaler un problème';

  @override
  String get reportBug => 'Signaler un bug';

  @override
  String get reportProblem => 'Signaler un problème';

  @override
  String get issueCategory => 'Catégorie du problème';

  @override
  String get bugReport => 'Rapport de bug';

  @override
  String get featureRequest => 'Demande de fonctionnalité';

  @override
  String get generalFeedback => 'Commentaires généraux';

  @override
  String get accountIssue => 'Problème de compte';

  @override
  String get technicalProblem => 'Problème technique';

  @override
  String get yourName => 'Votre nom';

  @override
  String get yourEmail => 'Votre email';

  @override
  String get issueDescription => 'Description du problème';

  @override
  String get describeIssueDetail => 'Veuillez décrire le problème en détail';

  @override
  String get optionalScreenshot => 'Capture d\'écran (Optionnel)';

  @override
  String get submitReport => 'Soumettre le rapport';

  @override
  String get reportSubmitted => 'Rapport soumis';

  @override
  String get reportSubmittedSuccess => 'Merci ! Votre rapport a été soumis avec succès. Nous allons l\'examiner et vous recontacter si nécessaire.';

  @override
  String get reportSubmissionFailed => 'Échec de la soumission du rapport. Veuillez réessayer plus tard.';

  @override
  String get nameRequired => 'Le nom est requis';

  @override
  String get emailRequired => 'L\'email est requis';

  @override
  String get validEmailRequired => 'Veuillez entrer une adresse email valide';

  @override
  String get descriptionRequired => 'La description du problème est requise';

  @override
  String get selectCategory => 'Veuillez sélectionner une catégorie';

  @override
  String get subscription => 'Abonnement';

  @override
  String get subscriptionActive => 'Abonnement Actif !';

  @override
  String get welcomeToPremium => 'Bienvenue à TripwiseGO Premium ! Profitez d\'un accès illimité à toutes les fonctionnalités.';

  @override
  String get currentPlan => 'Plan Actuel';

  @override
  String get trial => 'Essai';

  @override
  String get active => 'Actif';

  @override
  String trialEndsIn(int days) {
    return 'L\'essai se termine dans $days jours';
  }

  @override
  String renewsIn(int days) {
    return 'Se renouvelle dans $days jours';
  }

  @override
  String get yourBenefits => 'Vos Avantages';

  @override
  String get startPlanningTrip => 'Commencer à Planifier Votre Voyage';

  @override
  String get manageSubscription => 'Gérer l\'Abonnement';

  @override
  String get basic => 'Basique';

  @override
  String get premium => 'Premium';

  @override
  String get basicPlaceRecommendations => 'Recommandations de Lieux Basiques';

  @override
  String get locationRecommendationSwiping => 'Balayage de Recommandations de Lieux (20 balayages/jour)';

  @override
  String get aiPoweredTravelPlanner => 'Planificateur de Voyage IA';

  @override
  String get unlimitedQuizzes => 'Quiz et Faits Amusants Illimités';

  @override
  String get noAds => 'Sans Publicités';

  @override
  String get oneMonth => '1 Mois';

  @override
  String get threeMonths => '3 Mois';

  @override
  String get fiveMonths => '5 Mois';

  @override
  String dayFreeTrial(int days) {
    return 'Essai gratuit de $days jours';
  }

  @override
  String get billedMonthly => 'Facturé mensuellement';

  @override
  String get billedTwiceAnnually => 'Facturé deux fois par an';

  @override
  String get billedAnnually => 'Facturé annuellement';

  @override
  String get save45Percent => 'ÉCONOMISEZ 45%';

  @override
  String get continueButton => 'Continuer';

  @override
  String get termsAndConditions => 'Conditions Générales';

  @override
  String get failedToSubscribe => 'Échec de l\'abonnement. Veuillez réessayer.';

  @override
  String get signOut => 'Se déconnecter';

  @override
  String get selectLanguage => 'Sélectionner la langue';

  @override
  String get chooseYourPreferredLanguage => 'Choisissez votre langue préférée';

  @override
  String get languageUpdatedSuccessfully => 'Langue mise à jour avec succès !';

  @override
  String get home => 'Accueil';

  @override
  String get match => 'Correspondance';

  @override
  String get chat => 'Chat';

  @override
  String get profile => 'Profil';

  @override
  String get loading => 'Chargement...';

  @override
  String get error => 'Erreur';

  @override
  String get retry => 'Réessayer';

  @override
  String get ok => 'OK';

  @override
  String get yes => 'Oui';

  @override
  String get no => 'Non';

  @override
  String get save => 'Enregistrer';

  @override
  String get delete => 'Supprimer';

  @override
  String get edit => 'Modifier';

  @override
  String get add => 'Ajouter';

  @override
  String get remove => 'Supprimer';

  @override
  String get close => 'Fermer';

  @override
  String get back => 'Retour';

  @override
  String get next => 'Suivant';

  @override
  String get previous => 'Précédent';

  @override
  String get done => 'Terminé';

  @override
  String get search => 'Rechercher';

  @override
  String get noResultsFound => 'Aucun résultat trouvé';

  @override
  String get tryAgain => 'Réessayer';

  @override
  String get somethingWentWrong => 'Quelque chose s\'est mal passé';

  @override
  String get networkError => 'Erreur réseau. Veuillez vérifier votre connexion.';

  @override
  String get serverError => 'Erreur serveur. Veuillez réessayer plus tard.';

  @override
  String get invalidInput => 'Entrée invalide';

  @override
  String get required => 'Requis';

  @override
  String get optional => 'Optionnel';

  @override
  String get itinerary => 'Itinerary';

  @override
  String get yourItineraries => 'Your Itineraries';

  @override
  String get startPlanningYourTrip => 'Start Planning Your Trip';

  @override
  String get tripPlanningFeaturesWillAppearHere => 'Your trip planning features will appear here. The persistent authentication is now working!';

  @override
  String get forYou => 'For You';

  @override
  String get liked => 'Liked';

  @override
  String get collab => 'Collab';

  @override
  String get collaborativeItinerary => 'Collaboratif';

  @override
  String get endSession => 'End Session';

  @override
  String get logout => 'Logout';

  @override
  String logoutFailed(String error) {
    return 'Logout failed: $error';
  }

  @override
  String get noItineraryFound => 'Aucun itinéraire trouvé';

  @override
  String get askAiToCreateTravelPlan => 'Demandez à notre IA de créer un plan de voyage pour vous !';

  @override
  String get saturday => 'Samedi';

  @override
  String get tuesday => 'Mardi';

  @override
  String dayNumber(int number) {
    return 'Jour $number';
  }

  @override
  String get itineraryOverview => 'Aperçu de l\'itinéraire';

  @override
  String daysAndNights(int days, int nights) {
    return '${days}j ${nights}n';
  }

  @override
  String get hiImWanderlyAi => 'Salut, je suis Wanderly AI 🌏';

  @override
  String get yourTravelAiAssistant => 'Votre assistant IA de voyage, comment puis-je vous aider aujourd\'hui ?';

  @override
  String get useThisBubbleChat => 'Utilisez ce chat à bulles';

  @override
  String get aiAssistant => 'Assistant IA';

  @override
  String get chatHistory => 'Historique du chat';

  @override
  String get newChat => 'Nouveau chat';

  @override
  String get addImage => 'Ajouter une image';

  @override
  String get camera => 'Caméra';

  @override
  String get gallery => 'Galerie';

  @override
  String get microphonePermissionRequired => 'L\'autorisation du microphone est requise pour la saisie vocale';

  @override
  String get speechRecognitionNotAvailable => 'La reconnaissance vocale n\'est pas disponible sur cet appareil';

  @override
  String get listening => 'Écoute...';

  @override
  String get deleteChat => 'Supprimer le chat';

  @override
  String get deleteChatConfirmation => 'Êtes-vous sûr de vouloir supprimer ce chat ? Cette action ne peut pas être annulée.';

  @override
  String get chatDeletedSuccessfully => 'Chat supprimé avec succès';

  @override
  String get pleaseEnterSearchQuery => 'Veuillez saisir une requête de recherche';

  @override
  String get dailySearchLimitReached => 'Limite de recherche quotidienne atteinte. Vous pouvez effectuer 5 recherches par jour.';

  @override
  String get searchingTheWeb => 'Recherche sur le Web...';

  @override
  String get webSearchModeActive => 'Mode de recherche Web actif';

  @override
  String get pleaseWaitWhileSearching => 'Veuillez patienter pendant que je recherche des informations';

  @override
  String get yourNextMessageWillSearch => 'Votre prochain message recherchera sur le web';

  @override
  String get disableWebSearch => 'Désactiver la recherche Web';

  @override
  String get enableWebSearch => 'Activer la recherche Web';

  @override
  String get switchBackToAiChatMode => 'Revenir au mode chat IA';

  @override
  String get searchWebForCurrentInfo => 'Rechercher sur le web des informations actuelles';

  @override
  String get pickImageFromGallery => 'Choisir une image de la galerie';

  @override
  String get uploadImageForAiAnalysis => 'Télécharger une image pour l\'analyse IA';

  @override
  String get yourMessage => 'Votre message';

  @override
  String get wanderlyAi => 'Wanderly AI';

  @override
  String get webSearch => 'Recherche Web :';

  @override
  String get like => 'J\'aime';

  @override
  String get dislike => 'Je n\'aime pas';

  @override
  String get copy => 'Copier';

  @override
  String get regenerate => 'Régénérer';

  @override
  String get failedToSubmitFeedback => 'Échec de l\'envoi des commentaires. Veuillez réessayer.';

  @override
  String get thankYouForFeedback => 'Merci pour vos commentaires ! 🙏';

  @override
  String get feedbackReceivedThanks => 'Commentaires reçus. Merci de nous aider à nous améliorer ! 🚀';

  @override
  String get responseCopiedToClipboard => 'Réponse copiée dans le presse-papiers';

  @override
  String get wanderlyAiIsTyping => 'Wanderly AI tape';

  @override
  String get stopGeneration => 'Arrêter la génération';

  @override
  String get youHaveChatsLeft => 'Il vous reste 10 chats';

  @override
  String get enterSearchQuery => 'Entrez votre requête de recherche...';

  @override
  String get askMeAnythingOrLongPress => 'Demandez-moi n\'importe quoi ou appuyez longuement pour parler...';

  @override
  String failedToPickImage(String error) {
    return 'Échec de la sélection d\'image : $error';
  }

  @override
  String get failedToAnalyzeImage => 'Échec de l\'analyse d\'image. Veuillez réessayer.';

  @override
  String get responseGenerationStopped => 'La génération de réponse a été arrêtée.';

  @override
  String get unknownDestination => 'Destination inconnue';

  @override
  String get congratulations => 'Félicitations';

  @override
  String get itsAMatch => 'C\'est un match';

  @override
  String get travelVibesAligned => 'Les vibes de voyage sont alignées\nfaites vos bagages, vous avez un match !';

  @override
  String get matchedPreferences => 'Préférences correspondantes :';

  @override
  String get addToItinerary => 'Ajouter à l\'itinéraire';

  @override
  String get keepSwiping => 'Continuer à swiper';

  @override
  String get versionInfo => 'Informations de version';

  @override
  String get appVersion => 'Version de l\'application';

  @override
  String get buildNumber => 'Numéro de build';

  @override
  String get packageName => 'Nom du package';

  @override
  String get appName => 'Nom de l\'application';

  @override
  String get buildSignature => 'Signature de build';

  @override
  String get installerStore => 'Magasin d\'installation';

  @override
  String get deviceInfo => 'Informations de l\'appareil';

  @override
  String get operatingSystem => 'Système d\'exploitation';

  @override
  String get flutterVersion => 'Version Flutter';

  @override
  String get dartVersion => 'Version Dart';

  @override
  String get leaveAReview => 'Laisser un avis';

  @override
  String get rateOurApp => 'Évaluez notre app';

  @override
  String get enjoyingTripWiseGo => 'Vous appréciez TripWiseGo ?';

  @override
  String get helpUsImproveByLeavingReview => 'Aidez-nous à nous améliorer en laissant un avis sur l\'app store. Votre retour compte beaucoup pour nous !';

  @override
  String get rateNow => 'Évaluer maintenant';

  @override
  String get maybeLater => 'Peut-être plus tard';

  @override
  String get reviewFeatureNotAvailable => 'La fonction d\'avis n\'est disponible que dans les versions de production';

  @override
  String get unableToOpenStore => 'Impossible d\'ouvrir l\'app store. Veuillez réessayer plus tard.';

  @override
  String get thankYouForReview => 'Merci d\'avoir pris le temps d\'évaluer notre app !';
}
