import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../models/itinerary.dart';
import '../services/itinerary_service.dart';
import '../services/auth_service.dart';
import '../generated/l10n/app_localizations.dart';

class AddToItineraryDialog extends StatefulWidget {
  final Map<String, dynamic> destination;
  final VoidCallback? onSuccess;

  const AddToItineraryDialog({
    super.key,
    required this.destination,
    this.onSuccess,
  });

  @override
  State<AddToItineraryDialog> createState() => _AddToItineraryDialogState();
}

class _AddToItineraryDialogState extends State<AddToItineraryDialog>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  List<Itinerary> _itineraries = [];
  bool _isLoading = true;
  String? _error;
  int _currentStep = 1;
  Itinerary? _selectedItinerary;
  int? _selectedDay;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadItineraries();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadItineraries() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final itineraries = await ItineraryService.getAllItineraries();

      setState(() {
        _itineraries = itineraries;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Failed to load itineraries: $e';
        _isLoading = false;
      });
    }
  }

  void _selectItinerary(Itinerary itinerary) {
    setState(() {
      _selectedItinerary = itinerary;
      _currentStep = 2;
    });
  }

  void _selectDay(int day) {
    setState(() {
      _selectedDay = day;
    });
  }

  Future<void> _addToItinerary() async {
    if (_selectedItinerary == null || _selectedDay == null) return;

    try {
      if (kDebugMode) {
        print(
            'AddToItineraryDialog: Adding destination ${widget.destination['name']} to itinerary ${_selectedItinerary!.title} on day $_selectedDay');
        print(
            'AddToItineraryDialog: Full destination data: ${widget.destination}');
        print(
            'AddToItineraryDialog: Current itinerary day-specific activities: ${_selectedItinerary!.daySpecificActivities}');
      }

      // Get current day-specific activities - create a deep copy
      final daySpecificActivities = <int, Map<String, List<String>>>{};

      // Copy existing activities
      if (_selectedItinerary!.daySpecificActivities != null) {
        for (final entry
            in _selectedItinerary!.daySpecificActivities!.entries) {
          daySpecificActivities[entry.key] = <String, List<String>>{};
          for (final locationEntry in entry.value.entries) {
            daySpecificActivities[entry.key]![locationEntry.key] =
                List<String>.from(locationEntry.value);
          }
        }
      }

      // Initialize day if it doesn't exist
      if (!daySpecificActivities.containsKey(_selectedDay!)) {
        daySpecificActivities[_selectedDay!] = <String, List<String>>{};
      }

      // Use destination name as the location key
      final destinationName =
          widget.destination['name'] ?? 'Unknown Destination';

      // Initialize location if it doesn't exist
      if (!daySpecificActivities[_selectedDay!]!.containsKey(destinationName)) {
        daySpecificActivities[_selectedDay!]![destinationName] = <String>[];
      }

      // Add the activity (visit this destination)
      final activityDescription =
          'Visit ${widget.destination['name'] ?? 'destination'}';
      if (!daySpecificActivities[_selectedDay!]![destinationName]!
          .contains(activityDescription)) {
        daySpecificActivities[_selectedDay!]![destinationName]!
            .add(activityDescription);
      }

      if (kDebugMode) {
        print(
            'AddToItineraryDialog: Updated activities for day $_selectedDay: ${daySpecificActivities[_selectedDay!]}');
      }

      // Create updated itinerary
      final updatedItinerary = Itinerary(
        id: _selectedItinerary!.id,
        title: _selectedItinerary!.title,
        startDate: _selectedItinerary!.startDate,
        endDate: _selectedItinerary!.endDate,
        destinations: _selectedItinerary!.destinations,
        hasPhoto: _selectedItinerary!.hasPhoto,
        imagePath: _selectedItinerary!.imagePath,
        dailyActivities: _selectedItinerary!.dailyActivities,
        daySpecificActivities: daySpecificActivities,
        activityTimes: _selectedItinerary!.activityTimes,
        activityImages: _selectedItinerary!.activityImages,
        accommodation: _selectedItinerary!.accommodation,
        additionalNotes: _selectedItinerary!.additionalNotes,
        createdAt: _selectedItinerary!.createdAt,
      );

      if (kDebugMode) {
        print(
            'AddToItineraryDialog: Saving updated itinerary with ID: ${updatedItinerary.id}');
        print(
            'AddToItineraryDialog: Day-specific activities: ${updatedItinerary.daySpecificActivities}');
      }

      // Save the updated itinerary
      final success = await ItineraryService.updateItinerary(updatedItinerary);

      if (kDebugMode) {
        print('AddToItineraryDialog: Save result: $success');
      }

      if (success) {
        // Verify the save by reloading the itinerary
        if (kDebugMode) {
          print('AddToItineraryDialog: Save successful, verifying...');
          final reloadedItineraries =
              await ItineraryService.getAllItineraries();
          final reloadedItinerary = reloadedItineraries.firstWhere(
            (i) => i.id == _selectedItinerary!.id,
            orElse: () => throw Exception('Itinerary not found after save'),
          );
          print(
              'AddToItineraryDialog: Verification - reloaded itinerary day-specific activities: ${reloadedItinerary.daySpecificActivities}');

          // Check if our destination was actually saved
          final savedActivities =
              reloadedItinerary.daySpecificActivities?[_selectedDay];
          if (savedActivities != null) {
            print(
                'AddToItineraryDialog: Activities for day $_selectedDay: $savedActivities');
          } else {
            print(
                'AddToItineraryDialog: WARNING - No activities found for day $_selectedDay after save!');
          }
        }

        if (mounted) {
          Navigator.of(context).pop();
          widget.onSuccess?.call();

          // Show success message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Added "${widget.destination['name']}" to ${_selectedItinerary!.title} (Day $_selectedDay)',
                style: GoogleFonts.instrumentSans(color: Colors.white),
              ),
              backgroundColor: const Color(0xFF10B981),
              behavior: SnackBarBehavior.floating,
              duration: const Duration(seconds: 3),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          );
        }
      } else {
        throw Exception(
            'Failed to update itinerary - ItineraryService.updateItinerary returned false');
      }
    } catch (e) {
      if (kDebugMode) {
        print('AddToItineraryDialog: Error during save: $e');
      }

      if (mounted) {
        setState(() {
          _error = 'Failed to add to itinerary: $e';
        });

        // Show error message to user
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Failed to add destination to itinerary. Please try again.',
              style: GoogleFonts.instrumentSans(color: Colors.white),
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 3),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: ScaleTransition(
            scale: _scaleAnimation,
            child: Dialog(
              backgroundColor: Colors.transparent,
              child: Container(
                constraints:
                    const BoxConstraints(maxWidth: 400, maxHeight: 600),
                decoration: BoxDecoration(
                  color: const Color(0xFFF7F9FC),
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildHeader(),
                    Flexible(
                      child: _buildContent(),
                    ),
                    _buildFooter(),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        color: Color(0xFF0D76FF),
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _currentStep == 1 ? 'Select Itinerary' : 'Select Day',
                  style: GoogleFonts.instrumentSans(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  widget.destination['name'] ?? 'Unknown Destination',
                  style: GoogleFonts.instrumentSans(
                    fontSize: 14,
                    color: Colors.white.withOpacity(0.8),
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.close, color: Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return const Padding(
        padding: EdgeInsets.all(40),
        child: Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF0D76FF)),
          ),
        ),
      );
    }

    if (_error != null) {
      return Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: Colors.red.withOpacity(0.7),
            ),
            const SizedBox(height: 16),
            Text(
              _error!,
              style: GoogleFonts.instrumentSans(
                fontSize: 16,
                color: Colors.red,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadItineraries,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF0D76FF),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              child: Text(
                'Retry',
                style: GoogleFonts.instrumentSans(fontWeight: FontWeight.w600),
              ),
            ),
          ],
        ),
      );
    }

    if (_currentStep == 1) {
      return _buildItinerarySelection();
    } else {
      return _buildDaySelection();
    }
  }

  Widget _buildItinerarySelection() {
    if (_itineraries.isEmpty) {
      return Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.map_outlined,
              size: 48,
              color: const Color(0xFF0D76FF).withOpacity(0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'No Itineraries Found',
              style: GoogleFonts.instrumentSans(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF2D3748),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Create an itinerary first to add destinations to it.',
              style: GoogleFonts.instrumentSans(
                fontSize: 14,
                color: const Color(0xFF718096),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      padding: const EdgeInsets.all(16),
      itemCount: _itineraries.length,
      itemBuilder: (context, index) {
        final itinerary = _itineraries[index];
        return _buildItineraryCard(itinerary);
      },
    );
  }

  Widget _buildItineraryCard(Itinerary itinerary) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        child: InkWell(
          onTap: () => _selectItinerary(itinerary),
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: const Color(0xFF0D76FF).withOpacity(0.1),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  itinerary.title,
                  style: GoogleFonts.instrumentSans(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF2D3748),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${itinerary.startDate} - ${itinerary.endDate}',
                  style: GoogleFonts.instrumentSans(
                    fontSize: 14,
                    color: const Color(0xFF718096),
                  ),
                ),
                if (itinerary.destinations.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Text(
                    itinerary.destinations.join(', '),
                    style: GoogleFonts.instrumentSans(
                      fontSize: 12,
                      color: const Color(0xFF0D76FF),
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDaySelection() {
    if (_selectedItinerary == null) return const SizedBox.shrink();

    // Calculate number of days in the itinerary
    int numberOfDays = 1;

    try {
      // Try different date formats that might be used in itineraries
      DateTime? startDate;
      DateTime? endDate;

      // Try parsing as ISO format first (YYYY-MM-DD)
      startDate = DateTime.tryParse(_selectedItinerary!.startDate);
      endDate = DateTime.tryParse(_selectedItinerary!.endDate);

      // If that fails, try parsing as DD/MM/YYYY format
      if (startDate == null || endDate == null) {
        final startParts = _selectedItinerary!.startDate.split('/');
        final endParts = _selectedItinerary!.endDate.split('/');

        if (startParts.length == 3 && endParts.length == 3) {
          startDate = DateTime(
            int.parse(startParts[2]), // year
            int.parse(startParts[1]), // month
            int.parse(startParts[0]), // day
          );
          endDate = DateTime(
            int.parse(endParts[2]), // year
            int.parse(endParts[1]), // month
            int.parse(endParts[0]), // day
          );
        }
      }

      // If that also fails, try parsing as MM/DD/YYYY format
      if (startDate == null || endDate == null) {
        final startParts = _selectedItinerary!.startDate.split('/');
        final endParts = _selectedItinerary!.endDate.split('/');

        if (startParts.length == 3 && endParts.length == 3) {
          startDate = DateTime(
            int.parse(startParts[2]), // year
            int.parse(startParts[0]), // month
            int.parse(startParts[1]), // day
          );
          endDate = DateTime(
            int.parse(endParts[2]), // year
            int.parse(endParts[0]), // month
            int.parse(endParts[1]), // day
          );
        }
      }

      if (startDate != null && endDate != null) {
        numberOfDays = endDate.difference(startDate).inDays + 1;
        // Ensure we have at least 1 day
        if (numberOfDays < 1) {
          numberOfDays = 1;
        }
      }
    } catch (e) {
      // If all parsing fails, default to 1 day
      numberOfDays = 1;
      if (kDebugMode) {
        print(
            'AddToItineraryDialog: Failed to parse dates - ${_selectedItinerary!.startDate} to ${_selectedItinerary!.endDate}');
      }
    }

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Selected Itinerary:',
            style: GoogleFonts.instrumentSans(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF718096),
            ),
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: const Color(0xFF0D76FF).withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              _selectedItinerary!.title,
              style: GoogleFonts.instrumentSans(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF0D76FF),
              ),
            ),
          ),
          const SizedBox(height: 20),
          Text(
            'Select Day:',
            style: GoogleFonts.instrumentSans(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF718096),
            ),
          ),
          const SizedBox(height: 12),
          Expanded(
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
                childAspectRatio: 1.5,
              ),
              itemCount: numberOfDays,
              itemBuilder: (context, index) {
                final day = index + 1;
                final isSelected = _selectedDay == day;

                return Material(
                  color: isSelected ? const Color(0xFF0D76FF) : Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  child: InkWell(
                    onTap: () => _selectDay(day),
                    borderRadius: BorderRadius.circular(12),
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: isSelected
                              ? const Color(0xFF0D76FF)
                              : const Color(0xFF0D76FF).withOpacity(0.2),
                          width: 2,
                        ),
                      ),
                      child: Center(
                        child: Text(
                          'Day $day',
                          style: GoogleFonts.instrumentSans(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: isSelected
                                ? Colors.white
                                : const Color(0xFF0D76FF),
                          ),
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFooter() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          if (_currentStep == 2) ...[
            Expanded(
              child: OutlinedButton(
                onPressed: () {
                  setState(() {
                    _currentStep = 1;
                    _selectedDay = null;
                  });
                },
                style: OutlinedButton.styleFrom(
                  foregroundColor: const Color(0xFF0D76FF),
                  side: const BorderSide(color: Color(0xFF0D76FF)),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
                child: Text(
                  'Back',
                  style:
                      GoogleFonts.instrumentSans(fontWeight: FontWeight.w600),
                ),
              ),
            ),
            const SizedBox(width: 12),
          ],
          Expanded(
            child: ElevatedButton(
              onPressed: _currentStep == 1
                  ? null
                  : (_selectedDay != null ? _addToItinerary : null),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF0D76FF),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              child: Text(
                _currentStep == 1 ? 'Select Itinerary' : 'Add to Itinerary',
                style: GoogleFonts.instrumentSans(fontWeight: FontWeight.w600),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
