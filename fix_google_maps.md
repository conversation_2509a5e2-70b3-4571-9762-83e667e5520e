# Fix Google Maps MissingPluginException

## ✅ APPLIED: Updated Dependencies
I've already updated your `pubspec.yaml` with the latest Google Maps plugin versions:
- `google_maps_flutter: ^2.6.1`
- `google_maps_flutter_android: ^2.8.0`
- `google_maps_flutter_ios: ^2.5.0`

## ✅ APPLIED: Enhanced Error Handling
I've added better error handling to the plan page to gracefully handle polyline creation errors.

## Solution 1: Clean and Rebuild (Try this first)

```bash
# Stop the app if running
flutter clean
flutter pub get
flutter pub deps
cd android && ./gradlew clean && cd ..
flutter run
```

**Important**: After running these commands, use **Hot Restart** (Ctrl+Shift+F5) instead of Hot Reload when testing Google Maps functionality.

## Solution 3: Add Explicit Plugin Registration (Android)

Create/update `android/app/src/main/kotlin/com/example/tripwisego/MainActivity.kt`:

```kotlin
package com.example.tripwisego

import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugins.googlemaps.GoogleMapsPlugin

class MainActivity: FlutterActivity() {
    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        
        // Explicitly register Google Maps plugin
        flutterEngine.plugins.add(GoogleMapsPlugin())
    }
}
```

## Solution 4: Check Android Gradle Configuration

Ensure `android/app/build.gradle` has:

```gradle
android {
    compileSdkVersion 34
    
    defaultConfig {
        minSdkVersion 21  // Google Maps requires minimum API 21
        targetSdkVersion 34
    }
}
```

## Solution 5: Hot Restart Instead of Hot Reload

When testing Google Maps changes, always use:
- **Hot Restart** (Ctrl+Shift+F5) or 
- **Full Restart** (Stop and run again)

Hot reload doesn't work properly with native plugins like Google Maps.

## Solution 6: Check for Null Safety Issues

The error might be caused by accessing Google Maps before it's fully initialized. 

## Solution 7: Temporary Workaround

If the issue persists, you can temporarily disable polylines in the plan page until the plugin issue is resolved.
