{"@@locale": "en", "@@last_modified": "2024-01-01T00:00:00.000Z", "appTitle": "TripWiseGo", "@appTitle": {"description": "The title of the application"}, "welcome": "Welcome", "@welcome": {"description": "Welcome message"}, "guestUser": "Guest User", "@guestUser": {"description": "Label for guest users"}, "readyForAdventure": "Ready for your next adventure", "@readyForAdventure": {"description": "Subtitle for authenticated users"}, "exploringAsGuest": "Exploring the world as a guest", "@exploringAsGuest": {"description": "Subtitle for guest users"}, "editProfile": "Edit Profile", "@editProfile": {"description": "<PERSON><PERSON> to edit user profile"}, "saveChanges": "Save Changes", "@saveChanges": {"description": "Button to save profile changes"}, "cancel": "Cancel", "@cancel": {"description": "Cancel button"}, "username": "Username", "@username": {"description": "Username field label"}, "email": "Email", "@email": {"description": "Email field label"}, "profileUpdatedSuccessfully": "Profile updated successfully!", "@profileUpdatedSuccessfully": {"description": "Success message when profile is updated"}, "failedToUpdateProfile": "Failed to update profile: {error}", "@failedToUpdateProfile": {"description": "Error message when profile update fails", "placeholders": {"error": {"type": "String", "description": "The error message"}}}, "profilePictureUpdatedSuccessfully": "Profile picture updated successfully!", "@profilePictureUpdatedSuccessfully": {"description": "Success message when profile picture is updated"}, "failedToUploadImage": "Failed to upload image: {error}", "@failedToUploadImage": {"description": "Error message when image upload fails", "placeholders": {"error": {"type": "String", "description": "The error message"}}}, "profileEditingNotAvailableForGuests": "Profile editing is not available for guest users", "@profileEditingNotAvailableForGuests": {"description": "Message shown when guest users try to edit profile"}, "profilePictureEditingNotAvailableForGuests": "Profile picture editing is not available for guest users", "@profilePictureEditingNotAvailableForGuests": {"description": "Message shown when guest users try to edit profile picture"}, "usernameCannotBeEmpty": "Username cannot be empty", "@usernameCannotBeEmpty": {"description": "Validation message for empty username"}, "usernameMustBeBetween2And30Characters": "Username must be between 2 and 30 characters", "@usernameMustBeBetween2And30Characters": {"description": "Validation message for username length"}, "plan": "Plan", "@plan": {"description": "Plan menu item"}, "termsOfService": "Terms of Service", "@termsOfService": {"description": "Terms of Service page title"}, "language": "Language", "@language": {"description": "Language menu item"}, "privacyPolicy": "Privacy Policy", "@privacyPolicy": {"description": "Privacy Policy page title"}, "support": "Support", "@support": {"description": "Support section title"}, "helpCenter": "Help Center", "@helpCenter": {"description": "Help Center menu item"}, "contactUs": "Contact Us", "@contactUs": {"description": "Contact Us menu item"}, "helpSupport": "Help & Support", "@helpSupport": {"description": "Help & Support screen title"}, "howCanWeHelpYou": "How can we help you?", "@howCanWeHelpYou": {"description": "Help support screen header"}, "helpSupportGreeting": "Hi! I'm here to help you with TripwiseGO. You can ask me about app features, troubleshooting, or report any issues you're experiencing.", "@helpSupportGreeting": {"description": "Initial greeting message in help support chatbot"}, "helpSupportWelcome": "Welcome to TripwiseGO Support! Here are some things I can help you with:", "@helpSupportWelcome": {"description": "Welcome message in help support chatbot"}, "helpSupportFeatures": "• App features and how to use them\n• Navigation and account help\n• Troubleshooting common issues\n• Reporting bugs or problems", "@helpSupportFeatures": {"description": "List of help support features"}, "helpSupportAskQuestion": "Feel free to ask me anything or describe any issues you're having!", "@helpSupportAskQuestion": {"description": "Prompt to ask questions in help support"}, "reportIssue": "Report Issue", "@reportIssue": {"description": "Report issue button text"}, "reportBug": "Report Bug", "@reportBug": {"description": "Report bug option"}, "reportProblem": "Report Problem", "@reportProblem": {"description": "Report problem option"}, "issueCategory": "Issue Category", "@issueCategory": {"description": "Issue category field label"}, "bugReport": "Bug Report", "@bugReport": {"description": "Bug report category"}, "featureRequest": "Feature Request", "@featureRequest": {"description": "Feature request category"}, "generalFeedback": "General <PERSON>", "@generalFeedback": {"description": "General feedback category"}, "accountIssue": "Account Issue", "@accountIssue": {"description": "Account issue category"}, "technicalProblem": "Technical Problem", "@technicalProblem": {"description": "Technical problem category"}, "yourName": "Your Name", "@yourName": {"description": "Name field label"}, "yourEmail": "Your Email", "@yourEmail": {"description": "Email field label"}, "issueDescription": "Issue Description", "@issueDescription": {"description": "Issue description field label"}, "describeIssueDetail": "Please describe the issue in detail", "@describeIssueDetail": {"description": "Issue description placeholder text"}, "optionalScreenshot": "Screenshot (Optional)", "@optionalScreenshot": {"description": "Screenshot upload field label"}, "submitReport": "Submit Report", "@submitReport": {"description": "Submit report button text"}, "reportSubmitted": "Report Submitted", "@reportSubmitted": {"description": "Report submitted dialog title"}, "reportSubmittedSuccess": "Thank you! Your report has been submitted successfully. We'll look into it and get back to you if needed.", "@reportSubmittedSuccess": {"description": "Report submitted success message"}, "reportSubmissionFailed": "Failed to submit report. Please try again later.", "@reportSubmissionFailed": {"description": "Report submission failed message"}, "nameRequired": "Name is required", "@nameRequired": {"description": "Name field validation error"}, "emailRequired": "Email is required", "@emailRequired": {"description": "Email field validation error"}, "validEmailRequired": "Please enter a valid email address", "@validEmailRequired": {"description": "Valid email validation error"}, "descriptionRequired": "Issue description is required", "@descriptionRequired": {"description": "Description field validation error"}, "selectCategory": "Please select a category", "@selectCategory": {"description": "Category selection validation error"}, "subscription": "Subscription", "@subscription": {"description": "Subscription screen title"}, "subscriptionActive": "Subscription Active!", "@subscriptionActive": {"description": "Subscription active screen title"}, "welcomeToPremium": "Welcome to TripwiseGO Premium! Enjoy unlimited access to all features.", "@welcomeToPremium": {"description": "Welcome message for premium users"}, "currentPlan": "Current Plan", "@currentPlan": {"description": "Current subscription plan label"}, "trial": "Trial", "@trial": {"description": "Trial subscription status"}, "active": "Active", "@active": {"description": "Active subscription status"}, "trialEndsIn": "Trial ends in {days} days", "@trialEndsIn": {"description": "Trial expiration message", "placeholders": {"days": {"type": "int"}}}, "renewsIn": "Renews in {days} days", "@renewsIn": {"description": "Subscription renewal message", "placeholders": {"days": {"type": "int"}}}, "yourBenefits": "Your Benefits", "@yourBenefits": {"description": "Benefits section title"}, "startPlanningTrip": "Start Planning Your Trip", "@startPlanningTrip": {"description": "Button to start planning trip"}, "manageSubscription": "Manage Subscription", "@manageSubscription": {"description": "Button to manage subscription"}, "basic": "Basic", "@basic": {"description": "Basic subscription plan"}, "premium": "Premium", "@premium": {"description": "Premium subscription plan"}, "basicPlaceRecommendations": "Basic Place Recommendations", "@basicPlaceRecommendations": {"description": "Basic plan feature"}, "locationRecommendationSwiping": "Location Recommendation Swiping (20 swipes/day)", "@locationRecommendationSwiping": {"description": "Basic plan feature"}, "aiPoweredTravelPlanner": "AI-Powered Travel Planner", "@aiPoweredTravelPlanner": {"description": "Basic plan feature"}, "unlimitedQuizzes": "Unlimited Quizzes & Fun Facts", "@unlimitedQuizzes": {"description": "Basic plan feature"}, "noAds": "No-Ads", "@noAds": {"description": "Basic plan feature"}, "oneMonth": "1 Month", "@oneMonth": {"description": "One month subscription duration"}, "threeMonths": "3 Months", "@threeMonths": {"description": "Three months subscription duration"}, "fiveMonths": "5 Months", "@fiveMonths": {"description": "Five months subscription duration"}, "dayFreeTrial": "{days}-day free trial", "@dayFreeTrial": {"description": "Free trial duration", "placeholders": {"days": {"type": "int"}}}, "billedMonthly": "Billed monthly", "@billedMonthly": {"description": "Monthly billing cycle"}, "billedTwiceAnnually": "Billed twice annually", "@billedTwiceAnnually": {"description": "Twice annually billing cycle"}, "billedAnnually": "Billed annually", "@billedAnnually": {"description": "Annual billing cycle"}, "save45Percent": "SAVE 45%", "@save45Percent": {"description": "Discount text for popular plan"}, "continueButton": "Continue", "@continueButton": {"description": "Continue button text"}, "termsAndConditions": "Terms and Conditions", "@termsAndConditions": {"description": "Terms and conditions link"}, "failedToSubscribe": "Failed to subscribe. Please try again.", "@failedToSubscribe": {"description": "Subscription failure message"}, "signOut": "Sign Out", "@signOut": {"description": "Sign out button"}, "selectLanguage": "Select Language", "@selectLanguage": {"description": "Title for language selection screen"}, "chooseYourPreferredLanguage": "Choose your preferred language", "@chooseYourPreferredLanguage": {"description": "Subtitle for language selection"}, "languageUpdatedSuccessfully": "Language updated successfully!", "@languageUpdatedSuccessfully": {"description": "Success message when language is changed"}, "home": "Home", "@home": {"description": "Home navigation tab"}, "match": "Match", "@match": {"description": "Match navigation tab"}, "chat": "Cha<PERSON>", "@chat": {"description": "Chat navigation tab"}, "profile": "Profile", "@profile": {"description": "Profile navigation tab"}, "loading": "Loading...", "@loading": {"description": "Loading indicator text"}, "error": "Error", "@error": {"description": "Generic error title"}, "retry": "Retry", "@retry": {"description": "Retry button"}, "ok": "OK", "@ok": {"description": "OK button"}, "yes": "Yes", "@yes": {"description": "Yes button"}, "no": "No", "@no": {"description": "No button"}, "save": "Save", "@save": {"description": "Save button"}, "delete": "Delete", "@delete": {"description": "Delete button"}, "edit": "Edit", "@edit": {"description": "Edit button"}, "add": "Add", "@add": {"description": "Add button"}, "remove": "Remove", "@remove": {"description": "Remove button"}, "close": "Close", "@close": {"description": "Close button"}, "back": "Back", "@back": {"description": "Back button"}, "next": "Next", "@next": {"description": "Next button"}, "previous": "Previous", "@previous": {"description": "Previous button"}, "done": "Done", "@done": {"description": "Done button"}, "search": "Search", "@search": {"description": "Search placeholder"}, "noResultsFound": "No results found", "@noResultsFound": {"description": "Message when search returns no results"}, "tryAgain": "Try again", "@tryAgain": {"description": "Try again message"}, "somethingWentWrong": "Something went wrong", "@somethingWentWrong": {"description": "Generic error message"}, "networkError": "Network error. Please check your connection.", "@networkError": {"description": "Network error message"}, "serverError": "Server error. Please try again later.", "@serverError": {"description": "Server error message"}, "invalidInput": "Invalid input", "@invalidInput": {"description": "Invalid input error message"}, "required": "Required", "@required": {"description": "Required field indicator"}, "optional": "Optional", "@optional": {"description": "Optional field indicator"}, "itinerary": "Itinerary", "@itinerary": {"description": "Itinerary navigation tab"}, "yourItineraries": "Your Itineraries", "@yourItineraries": {"description": "Title for itinerary list"}, "startPlanningYourTrip": "Start Planning Your Trip", "@startPlanningYourTrip": {"description": "Call to action for trip planning"}, "tripPlanningFeaturesWillAppearHere": "Your trip planning features will appear here. The persistent authentication is now working!", "@tripPlanningFeaturesWillAppearHere": {"description": "Placeholder text for trip planning features"}, "forYou": "For You", "@forYou": {"description": "For You tab in match screen"}, "liked": "Liked", "@liked": {"description": "Liked tab in match screen"}, "collab": "Collab", "@collab": {"description": "Collaboration tab"}, "collaborativeItinerary": "Collaborative", "@collaborativeItinerary": {"description": "Collaborative Itinerary navigation tab"}, "endSession": "End Session", "@endSession": {"description": "End session button for guest users"}, "logout": "Logout", "@logout": {"description": "Logout button for authenticated users"}, "logoutFailed": "<PERSON><PERSON><PERSON> failed: {error}", "@logoutFailed": {"description": "Error message when logout fails", "placeholders": {"error": {"type": "String", "description": "The error message"}}}, "noItineraryFound": "No Itinerary Found", "@noItineraryFound": {"description": "Message when no itinerary is available"}, "askAiToCreateTravelPlan": "Ask our AI to create a travel plan for you!", "@askAiToCreateTravelPlan": {"description": "Suggestion to use AI for travel planning"}, "saturday": "Saturday", "@saturday": {"description": "Saturday day name"}, "tuesday": "Tuesday", "@tuesday": {"description": "Tuesday day name"}, "dayNumber": "Day {number}", "@dayNumber": {"description": "Day number label", "placeholders": {"number": {"type": "int", "description": "The day number"}}}, "itineraryOverview": "Itinerary Overview", "@itineraryOverview": {"description": "Header for itinerary overview section"}, "daysAndNights": "{days}d {nights}n", "@daysAndNights": {"description": "Days and nights format", "placeholders": {"days": {"type": "int", "description": "Number of days"}, "nights": {"type": "int", "description": "Number of nights"}}}, "hiImWanderlyAi": "<PERSON>, I'm <PERSON><PERSON><PERSON> AI 🌏", "@hiImWanderlyAi": {"description": "AI greeting message"}, "yourTravelAiAssistant": "Your Travel AI Assistant, how can I help you today?", "@yourTravelAiAssistant": {"description": "AI assistant introduction"}, "useThisBubbleChat": "Use this bubble chat", "@useThisBubbleChat": {"description": "Instruction for using chat interface"}, "aiAssistant": "AI Assistant", "@aiAssistant": {"description": "AI Assistant label"}, "chatHistory": "Chat History", "@chatHistory": {"description": "Chat history button tooltip"}, "newChat": "New Chat", "@newChat": {"description": "New chat button tooltip"}, "addImage": "Add Image", "@addImage": {"description": "Add image dialog title"}, "camera": "Camera", "@camera": {"description": "Camera option in image picker"}, "gallery": "Gallery", "@gallery": {"description": "Gallery option in image picker"}, "microphonePermissionRequired": "Microphone permission is required for voice input", "@microphonePermissionRequired": {"description": "Error message for microphone permission"}, "speechRecognitionNotAvailable": "Speech recognition is not available on this device", "@speechRecognitionNotAvailable": {"description": "Error message when speech recognition is unavailable"}, "listening": "Listening...", "@listening": {"description": "Status when listening for voice input"}, "deleteChat": "Delete Chat", "@deleteChat": {"description": "Delete chat dialog title"}, "deleteChatConfirmation": "Are you sure you want to delete this chat? This action cannot be undone.", "@deleteChatConfirmation": {"description": "Delete chat confirmation message"}, "chatDeletedSuccessfully": "<PERSON><PERSON> deleted successfully", "@chatDeletedSuccessfully": {"description": "Success message when chat is deleted"}, "pleaseEnterSearchQuery": "Please enter a search query", "@pleaseEnterSearchQuery": {"description": "Error when search query is empty"}, "dailySearchLimitReached": "Daily search limit reached. You can perform 5 searches per day.", "@dailySearchLimitReached": {"description": "Error when daily search limit is reached"}, "searchingTheWeb": "Searching the Web...", "@searchingTheWeb": {"description": "Status when searching the web"}, "webSearchModeActive": "Web Search Mode Active", "@webSearchModeActive": {"description": "Status when web search mode is active"}, "pleaseWaitWhileSearching": "Please wait while I search for information", "@pleaseWaitWhileSearching": {"description": "Message while searching"}, "yourNextMessageWillSearch": "Your next message will search the web", "@yourNextMessageWillSearch": {"description": "Info about next message searching"}, "disableWebSearch": "Disable Web Search", "@disableWebSearch": {"description": "Option to disable web search"}, "enableWebSearch": "Enable Web Search", "@enableWebSearch": {"description": "Option to enable web search"}, "switchBackToAiChatMode": "Switch back to AI chat mode", "@switchBackToAiChatMode": {"description": "Subtitle for disabling web search"}, "searchWebForCurrentInfo": "Search the web for current information", "@searchWebForCurrentInfo": {"description": "Subtitle for enabling web search"}, "pickImageFromGallery": "Pick Image from Gallery", "@pickImageFromGallery": {"description": "Menu option to pick image from gallery"}, "uploadImageForAiAnalysis": "Upload an image for AI analysis", "@uploadImageForAiAnalysis": {"description": "Subtitle for image upload option"}, "yourMessage": "Your Message", "@yourMessage": {"description": "Label for user messages"}, "wanderlyAi": "Wanderly AI", "@wanderlyAi": {"description": "Label for AI messages"}, "webSearch": "Web Search:", "@webSearch": {"description": "Label for web search messages"}, "like": "Like", "@like": {"description": "Like button label"}, "dislike": "Dislike", "@dislike": {"description": "Dislike button label"}, "copy": "Copy", "@copy": {"description": "Copy button label"}, "regenerate": "Regenerate", "@regenerate": {"description": "Regenerate button label"}, "failedToSubmitFeedback": "Failed to submit feedback. Please try again.", "@failedToSubmitFeedback": {"description": "Error message when feedback submission fails"}, "thankYouForFeedback": "Thank you for your feedback! 🙏", "@thankYouForFeedback": {"description": "Thank you message for positive feedback"}, "feedbackReceivedThanks": "Fe<PERSON><PERSON> received. Thank you for helping us improve! 🚀", "@feedbackReceivedThanks": {"description": "Thank you message for negative feedback"}, "responseCopiedToClipboard": "Response copied to clipboard", "@responseCopiedToClipboard": {"description": "Message when response is copied"}, "wanderlyAiIsTyping": "Wanderly AI is typing", "@wanderlyAiIsTyping": {"description": "Typing indicator message"}, "stopGeneration": "Stop Generation", "@stopGeneration": {"description": "Button to stop AI response generation"}, "youHaveChatsLeft": "You have 10 chats left", "@youHaveChatsLeft": {"description": "Chat limit indicator"}, "enterSearchQuery": "Enter your search query...", "@enterSearchQuery": {"description": "Placeholder for search input"}, "askMeAnythingOrLongPress": "Ask me anything or long press to speak...", "@askMeAnythingOrLongPress": {"description": "Placeholder for chat input"}, "failedToPickImage": "Failed to pick image: {error}", "@failedToPickImage": {"description": "Error message when image picking fails", "placeholders": {"error": {"type": "String", "description": "The error message"}}}, "failedToAnalyzeImage": "Failed to analyze image. Please try again.", "@failedToAnalyzeImage": {"description": "Error message when image analysis fails"}, "responseGenerationStopped": "Response generation was stopped.", "@responseGenerationStopped": {"description": "Message when response generation is stopped"}, "unknownDestination": "Unknown Destination", "@unknownDestination": {"description": "Fallback text for unknown destinations"}, "congratulations": "Congratulations", "@congratulations": {"description": "Congratulations message for matches"}, "itsAMatch": "It's a match", "@itsAMatch": {"description": "Match confirmation message"}, "travelVibesAligned": "Travel vibes aligned pack\nyour bags, you've got a match!", "@travelVibesAligned": {"description": "Match subtitle message"}, "matchedPreferences": "Matched Preferences:", "@matchedPreferences": {"description": "Header for matched preferences section"}, "addToItinerary": "Add to Itinerary", "@addToItinerary": {"description": "Button to add destination to itinerary"}, "keepSwiping": "Keep swiping", "@keepSwiping": {"description": "Button to continue swiping"}, "versionInfo": "Version Info", "@versionInfo": {"description": "Version information menu item"}, "appVersion": "App Version", "@appVersion": {"description": "App version label"}, "buildNumber": "Build Number", "@buildNumber": {"description": "Build number label"}, "packageName": "Package Name", "@packageName": {"description": "Package name label"}, "appName": "App Name", "@appName": {"description": "Application name label"}, "buildSignature": "Build Signature", "@buildSignature": {"description": "Build signature label"}, "installerStore": "Installer Store", "@installerStore": {"description": "Installer store label"}, "deviceInfo": "Device Information", "@deviceInfo": {"description": "Device information section title"}, "operatingSystem": "Operating System", "@operatingSystem": {"description": "Operating system label"}, "flutterVersion": "Flutter Version", "@flutterVersion": {"description": "Flutter version label"}, "dartVersion": "Dart Version", "@dartVersion": {"description": "Dart version label"}, "leaveAReview": "Leave a Review", "@leaveAReview": {"description": "Leave a review button text"}, "rateOurApp": "Rate Our App", "@rateOurApp": {"description": "Rate our app dialog title"}, "enjoyingTripWiseGo": "Enjoying TripWiseGo?", "@enjoyingTripWiseGo": {"description": "Review dialog subtitle"}, "helpUsImproveByLeavingReview": "Help us improve by leaving a review on the app store. Your feedback means a lot to us!", "@helpUsImproveByLeavingReview": {"description": "Review dialog description"}, "rateNow": "Rate Now", "@rateNow": {"description": "Rate now button text"}, "maybeLater": "Maybe Later", "@maybeLater": {"description": "Maybe later button text"}, "reviewFeatureNotAvailable": "Review feature is only available in production builds", "@reviewFeatureNotAvailable": {"description": "Message for development builds"}, "unableToOpenStore": "Unable to open app store. Please try again later.", "@unableToOpenStore": {"description": "Error message when store cannot be opened"}, "thankYouForReview": "Thank you for taking the time to review our app!", "@thankYouForReview": {"description": "Thank you message after review"}}