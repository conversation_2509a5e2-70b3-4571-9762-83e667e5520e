import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:tripwisego/widgets/optimized_network_image.dart';
import 'package:tripwisego/services/image_cache_manager.dart';
import 'package:tripwisego/services/image_preloading_service.dart';

void main() {
  group('Optimized Image Loading Tests', () {
    late ImageCacheManager cacheManager;
    late ImagePreloadingService preloadingService;

    setUpAll(() {
      cacheManager = ImageCacheManager();
      cacheManager.initialize();
      preloadingService = ImagePreloadingService();
    });

    testWidgets('OptimizedNetworkImage renders correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: OptimizedNetworkImage(
              imageUrl: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4',
              width: 200,
              height: 200,
              fit: BoxFit.cover,
            ),
          ),
        ),
      );

      // Verify the widget is rendered
      expect(find.byType(OptimizedNetworkImage), findsOneWidget);
      
      // Wait for shimmer placeholder to appear
      await tester.pump();
      
      // The shimmer should be visible initially
      expect(find.byType(Container), findsWidgets);
    });

    testWidgets('OptimizedNetworkImage with error handling', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: OptimizedNetworkImage(
              imageUrl: 'invalid-url',
              width: 200,
              height: 200,
              fit: BoxFit.cover,
              enableRetry: true,
            ),
          ),
        ),
      );

      await tester.pump();
      
      // Should render without throwing errors
      expect(find.byType(OptimizedNetworkImage), findsOneWidget);
    });

    testWidgets('OptimizedNetworkImage with progressive loading', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: OptimizedNetworkImage(
              imageUrl: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4',
              lowResImageUrl: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?q=30&w=200',
              width: 200,
              height: 200,
              fit: BoxFit.cover,
              enableProgressiveLoading: true,
            ),
          ),
        ),
      );

      await tester.pump();
      
      // Should render progressive loading stack
      expect(find.byType(OptimizedNetworkImage), findsOneWidget);
    });

    test('ImageCacheManager URL optimization', () {
      final originalUrl = 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4';
      
      // Test low-res URL generation
      final lowResUrl = cacheManager.generateLowResUrl(originalUrl);
      expect(lowResUrl, contains('q=30'));
      expect(lowResUrl, contains('w=200'));
      
      // Test high-res URL generation
      final highResUrl = cacheManager.generateHighResUrl(originalUrl);
      expect(highResUrl, contains('q=85'));
      expect(highResUrl, contains('w=800'));
    });

    test('ImagePreloadingService connectivity handling', () async {
      await preloadingService.initialize();
      
      // Test URL optimization based on connectivity
      final testUrl = 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4';
      final optimizedUrl = preloadingService.getOptimizedImageUrl(testUrl);
      
      // Should return a valid URL
      expect(optimizedUrl, isNotEmpty);
      expect(optimizedUrl, contains('unsplash.com'));
    });

    test('ImageCacheManager cache stats', () {
      final stats = cacheManager.getCacheStats();
      
      // Should return valid stats
      expect(stats, isA<Map<String, dynamic>>());
      expect(stats.containsKey('memory_cache_count'), isTrue);
      expect(stats.containsKey('memory_cache_max_size'), isTrue);
    });

    test('ImagePreloadingService cache management', () {
      final testUrl = 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4';
      
      // Test cache stats
      final stats = preloadingService.getCacheStats();
      expect(stats, isA<Map<String, dynamic>>());
      expect(stats.containsKey('preloaded_count'), isTrue);
      expect(stats.containsKey('connectivity'), isTrue);
    });
  });

  group('Performance Tests', () {
    testWidgets('Multiple OptimizedNetworkImages render efficiently', (WidgetTester tester) async {
      final imageUrls = [
        'https://images.unsplash.com/photo-1506905925346-21bda4d32df4',
        'https://images.unsplash.com/photo-1703769605297-cc74106244d9',
        'https://images.unsplash.com/photo-1526481280693-3bfa7568e0f3',
      ];

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ListView.builder(
              itemCount: imageUrls.length,
              itemBuilder: (context, index) => OptimizedNetworkImage(
                imageUrl: imageUrls[index],
                width: 200,
                height: 200,
                fit: BoxFit.cover,
                cacheWidth: 200,
                cacheHeight: 200,
              ),
            ),
          ),
        ),
      );

      await tester.pump();
      
      // Should render all images without performance issues
      expect(find.byType(OptimizedNetworkImage), findsNWidgets(imageUrls.length));
    });
  });

  group('Error Handling Tests', () {
    testWidgets('Handles invalid URLs gracefully', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: OptimizedNetworkImage(
              imageUrl: '',
              width: 200,
              height: 200,
              fit: BoxFit.cover,
            ),
          ),
        ),
      );

      await tester.pump();
      
      // Should not throw errors with empty URL
      expect(find.byType(OptimizedNetworkImage), findsOneWidget);
    });

    testWidgets('Custom error widget displays correctly', (WidgetTester tester) async {
      const customErrorWidget = Text('Custom Error');
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: OptimizedNetworkImage(
              imageUrl: 'invalid-url',
              width: 200,
              height: 200,
              fit: BoxFit.cover,
              errorWidget: customErrorWidget,
            ),
          ),
        ),
      );

      await tester.pump();
      
      // Should render without errors
      expect(find.byType(OptimizedNetworkImage), findsOneWidget);
    });
  });
}
