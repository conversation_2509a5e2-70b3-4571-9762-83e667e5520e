# OAuth Testing Guide for TripWiseGo

This document provides a comprehensive testing plan for the OAuth authentication flow.

## Pre-Testing Setup

### 1. Supabase Configuration
Ensure the following are configured in your Supabase project:

#### Authentication Settings
- **Site URL**: Set to your app's domain or `http://localhost:3000` for development
- **Redirect URLs**: 
  - `https://ktdstluymbpqejmjkpsg.supabase.co/auth/v1/callback`
  - `io.supabase.tripwisego://login-callback/`

#### Provider Settings
- **Google**: Enabled with correct Client ID and Secret

### 2. Provider Configuration
Ensure callback URLs are configured in Google Cloud Console:
- **Google Cloud Console**: Add redirect URIs

### 3. App Configuration
- **Android**: Verify `AndroidManifest.xml` has correct intent filter
- **iOS**: Verify `Info.plist` has correct URL scheme
- **Dependencies**: Ensure all required packages are installed

## Testing Scenarios

### Scenario 1: Google OAuth (Native Flow)
**Expected Behavior**: Direct authentication using Google Sign-In SDK

**Test Steps**:
1. Open the app
2. Navigate to login screen
3. Tap Google sign-in button
4. Complete Google authentication in popup/overlay
5. Verify user is redirected to homepage
6. Verify session persists on app restart

**Success Criteria**:
- ✅ Google sign-in popup appears
- ✅ User can complete authentication
- ✅ App navigates to homepage automatically
- ✅ User stays logged in after app restart
- ✅ User data is available in homepage

### Scenario 2: Session Persistence
**Expected Behavior**: User remains logged in across app restarts

**Test Steps**:
1. Complete OAuth login with any provider
2. Verify user is on homepage
3. Close the app completely
4. Reopen the app
5. Verify user is still logged in and on homepage

**Success Criteria**:
- ✅ User remains logged in after app restart
- ✅ No additional authentication required
- ✅ User data is still available

### Scenario 5: Error Handling
**Expected Behavior**: Graceful handling of OAuth errors

**Test Steps**:
1. Initiate OAuth flow
2. Cancel authentication in browser
3. Verify app handles cancellation gracefully
4. Try OAuth with invalid credentials (if possible)
5. Verify appropriate error messages are shown

**Success Criteria**:
- ✅ App doesn't crash on OAuth cancellation
- ✅ Appropriate error messages are displayed
- ✅ User can retry authentication

## Debugging Tools

### Console Logs
Monitor the Flutter debug console for these log messages:
- `OAuth Callback Handler: Processing deep link`
- `Web OAuth Handler: Found OAuth callback`
- `Auth State Manager: Valid session found`
- `Auth Service: OAuth authentication completed`

### Network Monitoring
Use browser developer tools to monitor:
- OAuth redirect URLs
- Callback parameters
- Session establishment

### Deep Link Testing
Test deep links manually:
```bash
# Android
adb shell am start -W -a android.intent.action.VIEW -d "io.supabase.tripwisego://login-callback/?code=test" com.example.tripwisego

# iOS (Simulator)
xcrun simctl openurl booted "io.supabase.tripwisego://login-callback/?code=test"
```

## Common Issues and Solutions

### Issue: Browser doesn't redirect back to app
**Solution**: 
- Verify URL scheme configuration in Android/iOS
- Check Supabase redirect URL configuration
- Ensure provider callback URLs are correct

### Issue: Session not persisting
**Solution**:
- Check auth state manager initialization
- Verify session refresh logic
- Monitor auth state change events

### Issue: OAuth flow times out
**Solution**:
- Increase timeout duration in `waitForOAuthCompletion`
- Check network connectivity
- Verify provider configuration

### Issue: Deep links not working
**Solution**:
- Test deep link handling manually
- Check app link configuration
- Verify intent filter setup

## Performance Considerations

- OAuth flows should complete within 2 minutes
- App should respond to deep links within 3 seconds
- Session initialization should complete within 5 seconds
- No memory leaks during OAuth flows

## Security Checklist

- ✅ Client secrets not exposed in client code
- ✅ PKCE flow used where possible
- ✅ Session tokens properly secured
- ✅ Redirect URLs use HTTPS (except for app schemes)
- ✅ OAuth state parameter validation (handled by Supabase)

## Test Environment Setup

### Development
- Use localhost URLs for testing
- Enable debug logging
- Use test OAuth credentials if available

### Production
- Use production URLs
- Disable debug logging
- Use production OAuth credentials
- Test on real devices

## Automated Testing

Consider implementing automated tests for:
- Deep link handling
- Session persistence
- Auth state management
- Error scenarios

## Reporting Issues

When reporting OAuth issues, include:
- Platform (Android/iOS/Web)
- OAuth provider (Google)
- Error messages from console
- Network logs
- Steps to reproduce
- Expected vs actual behavior
