name: tripwisego
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.3.4 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.6
  intl: ^0.18.1
  google_fonts: ^6.2.1
  path_provider: ^2.1.4

  # Supabase authentication
  supabase_flutter: ^2.8.0

  # Social authentication
  google_sign_in: ^6.2.1

  # Captcha
  flutter_captcha: ^1.0.0

  # HTTP requests
  http: ^1.2.0

  # CSV parsing for feedback data
  csv: ^6.0.0

  # Google Sheets API
  googleapis: ^13.1.0
  googleapis_auth: ^1.6.0

  # Deep link handling for OAuth callbacks
  app_links: ^6.3.2
  flutter_card_swiper: ^7.0.2

  # Local storage for match history
  shared_preferences: ^2.2.3

  # Image picker for photo selection
  image_picker: ^1.0.4

  # Lottie animations
  lottie: ^3.1.2

  # Markdown rendering for AI chat responses
  flutter_markdown: ^0.7.3

  # Cached network images for travel recommendations
  cached_network_image: ^3.3.1

  # Speech to text for voice input
  speech_to_text: 6.6.0

  # Permission handler for microphone access
  permission_handler: ^11.3.1

  # SQLite for local storage (chat history)
  sqflite: ^2.3.3
  path: ^1.9.0
  url_launcher: ^6.3.1
  google_generative_ai: ^0.4.7

  # Google Maps integration
  google_maps_flutter: ^2.6.1
  google_maps_flutter_android: ^2.8.0
  google_maps_flutter_ios: ^2.5.0
  geolocator: ^10.1.0
  geocoding: ^3.0.0
  package_info_plus: ^8.0.2
  device_info_plus: ^10.1.2
  in_app_review: ^2.0.10

  # In-app purchases with RevenueCat
  purchases_flutter: ^6.30.0
  rename: ^3.1.0
  connectivity_plus: ^6.1.4
  shimmer: ^3.0.0
  flutter_cache_manager: ^3.4.1

  # Google AdMob for interstitial ads
  google_mobile_ads: ^5.2.0



dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^3.0.0

  # Build runner for code generation (if needed for other packages)
  build_runner: ^2.4.9

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Internationalization configuration
  generate: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/
    - assets/images/
    - assets/lottie/
    - assets/credentials/
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
