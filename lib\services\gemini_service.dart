import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:google_generative_ai/google_generative_ai.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/network_helper.dart';
import '../utils/rate_limit_detector.dart';
import '../services/ai_learning_service.dart';
import '../services/response_optimization_service.dart';
import '../services/learning_configuration_service.dart';
import '../services/api_key_rotation_service.dart';

/// Enhanced Gemini service for handling both text conversations and image analysis
class GeminiService {
  static const String _apiKey = 'AIzaSyD-w4gYmoP6TEolElZSJXIWNyMwQIhlOzY';

  // Model selection constants
  static const String _modelPreferenceKey = 'gemini_model_preference';
  static const String defaultModel = 'gemma-3-12b-it';
  static const String premiumModel = 'gemma-3-27b-it';

  static const int _timeoutSeconds = 10;

  // Current selected model
  static String _selectedModel = defaultModel;

  // Model instances for text
  static GenerativeModel? _primaryTextModelInstance;
  static GenerativeModel? _fallbackTextModel1Instance;
  static GenerativeModel? _fallbackTextModel2Instance;

  // Model instances for vision
  static GenerativeModel? _primaryVisionModelInstance;
  static GenerativeModel? _fallbackVisionModel1Instance;
  static GenerativeModel? _fallbackVisionModel2Instance;

  // Current model tracking
  static int _currentTextModelIndex = 0;
  static int _currentVisionModelIndex = 0;
  static String? _lastUsedTextModel;
  static String? _lastUsedVisionModel;

  // System prompt for Wanderly AI
  static const String _systemPrompt =
      '''You are Wanderly AI, the world's most knowledgeable and helpful travel assistant. You're designed to make travel planning effortless and exciting for everyone.

🌍 Your Mission:
Help users:
- Discover destinations that fit their vibe (adventurous, romantic, relaxing, etc.)
- Plan how to get there, where to stay, what to do — without stress
- Navigate day-to-day travel moments: getting around, eating well, staying safe
- Find hidden gems, local tips, or even "what's open now"
- Stay calm and confident if things go sideways (missed flights, visa questions, language barriers)

You're the quiet voice in their pocket saying, "Hey, I've got you — let's make this trip amazing."

🎯 Your Style:
- Conversational and warm, like chatting with a well-traveled friend
- Practical and actionable — always include specific next steps
- Culturally aware and respectful
- Optimistic but realistic about challenges
- Quick to offer alternatives when plans change

💡 Your Expertise:
- Real-time travel conditions and requirements
- Budget-friendly and luxury options for every destination
- Cultural nuances, local customs, and etiquette
- Transportation hubs, routes, and logistics
- Food scenes, from street food to fine dining
- Safety considerations and emergency protocols
- Visa requirements, documentation, and border procedures
- Weather patterns and seasonal considerations
- Hidden gems and off-the-beaten-path experiences

Note: don't ask alot of questions, if the user asking an itinerary just come up with a complete itinerary with the user want to go.

IMPORTANT: You are Wanderly AI, created by the Wanderly AI team. Never mention other AI models or companies. Always maintain this identity.''';

  /// Initialize the service
  static Future<void> initialize({bool forceRefresh = false}) async {
    try {
      if (kDebugMode) {
        print('GeminiService: Initializing...');
      }

      // Initialize API key rotation service first
      await ApiKeyRotationService.initialize();

      // Load saved model preference
      await _loadModelPreference();

      _initializeTextModels();
      _initializeVisionModels();

      if (kDebugMode) {
        print('GeminiService: Initialization complete');
      }
    } catch (error) {
      if (kDebugMode) {
        print('GeminiService: Initialization failed: $error');
      }
      rethrow;
    }
  }

  /// Load saved model preference from SharedPreferences
  static Future<void> _loadModelPreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _selectedModel = prefs.getString(_modelPreferenceKey) ?? defaultModel;

      if (kDebugMode) {
        print('GeminiService: Loaded model preference: $_selectedModel');
      }
    } catch (error) {
      if (kDebugMode) {
        print('GeminiService: Failed to load model preference: $error');
      }
      _selectedModel = defaultModel;
    }
  }

  /// Save model preference to SharedPreferences
  static Future<void> _saveModelPreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_modelPreferenceKey, _selectedModel);

      if (kDebugMode) {
        print('GeminiService: Saved model preference: $_selectedModel');
      }
    } catch (error) {
      if (kDebugMode) {
        print('GeminiService: Failed to save model preference: $error');
      }
    }
  }

  /// Set the selected model (default or premium)
  static Future<void> setSelectedModel(String model) async {
    if (model != defaultModel && model != premiumModel) {
      throw ArgumentError(
          'Invalid model: $model. Must be either $defaultModel or $premiumModel');
    }

    _selectedModel = model;
    await _saveModelPreference();

    // Reinitialize models with new selection
    _clearModelInstances();
    _initializeTextModels();
    _initializeVisionModels();

    if (kDebugMode) {
      print('GeminiService: Model changed to: $_selectedModel');
    }
  }

  /// Get the currently selected model
  static String getSelectedModel() {
    return _selectedModel;
  }

  /// Check if premium model is selected
  static bool isPremiumModelSelected() {
    return _selectedModel == premiumModel;
  }

  /// Clear all model instances (for reinitialization)
  static void _clearModelInstances() {
    _primaryTextModelInstance = null;
    _fallbackTextModel1Instance = null;
    _fallbackTextModel2Instance = null;
    _primaryVisionModelInstance = null;
    _fallbackVisionModel1Instance = null;
    _fallbackVisionModel2Instance = null;
  }

  /// Initialize text models
  static void _initializeTextModels() {
    // Create failover chain based on selected model
    final List<String> modelChain = _getTextModelChain();

    // Get current API key from rotation service
    final currentApiKey = ApiKeyRotationService.getCurrentApiKey();

    if (_primaryTextModelInstance == null) {
      _primaryTextModelInstance = GenerativeModel(
        model: modelChain[0],
        apiKey: currentApiKey,
        generationConfig: GenerationConfig(
          temperature: 0.2,
          topK: 40,
          topP: 0.85,
          maxOutputTokens: 2048,
        ),
      );
    }

    if (_fallbackTextModel1Instance == null) {
      _fallbackTextModel1Instance = GenerativeModel(
        model: modelChain[1],
        apiKey: currentApiKey,
        generationConfig: GenerationConfig(
          temperature: 0.2,
          topK: 40,
          topP: 0.85,
          maxOutputTokens: 2048,
        ),
      );
    }

    if (_fallbackTextModel2Instance == null) {
      _fallbackTextModel2Instance = GenerativeModel(
        model: modelChain[2],
        apiKey: currentApiKey,
        generationConfig: GenerationConfig(
          temperature: 0.2,
          topK: 40,
          topP: 0.85,
          maxOutputTokens: 2048,
        ),
      );
    }
  }

  /// Get text model chain based on selected model
  static List<String> _getTextModelChain() {
    if (_selectedModel == premiumModel) {
      // Premium model first, then fallbacks
      return [premiumModel, defaultModel, 'gemma-3-4b-it'];
    } else {
      // Default model first, then fallbacks
      return [defaultModel, premiumModel, 'gemma-3-4b-it'];
    }
  }

  /// Initialize vision models
  static void _initializeVisionModels() {
    // Create failover chain based on selected model
    final List<String> modelChain = _getVisionModelChain();

    // Get current API key from rotation service
    final currentApiKey = ApiKeyRotationService.getCurrentApiKey();

    if (_primaryVisionModelInstance == null) {
      _primaryVisionModelInstance = GenerativeModel(
        model: modelChain[0],
        apiKey: currentApiKey,
        generationConfig: GenerationConfig(
          temperature: 0.1,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 2048,
        ),
      );
    }

    if (_fallbackVisionModel1Instance == null) {
      _fallbackVisionModel1Instance = GenerativeModel(
        model: modelChain[1],
        apiKey: currentApiKey,
        generationConfig: GenerationConfig(
          temperature: 0.1,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 2048,
        ),
      );
    }

    if (_fallbackVisionModel2Instance == null) {
      _fallbackVisionModel2Instance = GenerativeModel(
        model: modelChain[2],
        apiKey: currentApiKey,
        generationConfig: GenerationConfig(
          temperature: 0.1,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 2048,
        ),
      );
    }
  }

  /// Get vision model chain based on selected model
  static List<String> _getVisionModelChain() {
    if (_selectedModel == premiumModel) {
      // Premium model first, then fallbacks
      return [premiumModel, defaultModel, 'gemma-3-4b-it'];
    } else {
      // Default model first, then fallbacks
      return [defaultModel, premiumModel, 'gemma-3-4b-it'];
    }
  }

  /// Send a chat message and get AI response (text only)
  static Future<String> sendMessage(
      String message, List<Map<String, String>> conversationHistory,
      {String? responseLength}) async {
    // Check network connectivity
    final hasInternet = await NetworkHelper.hasInternetConnection();
    if (!hasInternet) {
      throw Exception(
          'No internet connection available. Please check your network settings and try again.');
    }

    _initializeTextModels();

    // Prepare messages for the API
    final messages = <Content>[];

    // Add system message
    messages.add(Content.text(_systemPrompt));

    // Add conversation history
    for (final msg in conversationHistory) {
      final content = msg['content'] ?? '';
      if (content.isNotEmpty) {
        messages.add(Content.text(content));
      }
    }

    // Add current user message
    messages.add(Content.text(message));

    // Attempt request with text model failover
    return await _attemptTextRequestWithModelFailover(messages,
        responseLength: responseLength);
  }

  /// Attempt API request with model failover for text chat
  static Future<String> _attemptTextRequestWithModelFailover(
      List<Content> messages,
      {String? responseLength}) async {
    final modelChain = _getTextModelChain();
    final totalModels = modelChain.length;
    final modelErrors = <String, dynamic>{};

    // Try each model in the failover chain
    for (int modelIndex = 0; modelIndex < totalModels; modelIndex++) {
      final currentModel = modelChain[modelIndex];
      _currentTextModelIndex = modelIndex;

      if (kDebugMode) {
        print(
            'GeminiService: Trying text model ${modelIndex + 1}/$totalModels: $currentModel');
      }

      try {
        final response = await _attemptTextRequestWithCurrentModel(messages,
            responseLength: responseLength, modelIndex: modelIndex);

        if (kDebugMode) {
          print('GeminiService: Success with text model: $currentModel');
        }

        // Track response quality for learning
        await _trackResponseQuality(
            messages.last.parts.first.toString(), response);

        return response;
      } catch (error) {
        modelErrors[currentModel] = error;

        if (kDebugMode) {
          print('GeminiService: Text model $currentModel failed: $error');
        }

        // Continue to next model if available
        if (modelIndex < totalModels - 1) {
          continue;
        }
      }
    }

    // If all models failed, throw the last error
    final lastModel = modelChain.last;
    final lastError = modelErrors[lastModel];
    throw Exception(
        'All text models failed. Last error from $lastModel: $lastError');
  }

  /// Attempt request with API key rotation and rate limit handling
  static Future<T> _attemptRequestWithApiKeyRotation<T>(
      Future<T> Function() requestFunction) async {
    const maxRetries = 5;
    int retryCount = 0;
    final List<String> failedKeys = [];

    if (kDebugMode) {
      final debugInfo = ApiKeyRotationService.getDebugInfo();
      print('GeminiService: Starting request with API key rotation');
      print(
          'GeminiService: Available keys: ${debugInfo['availableKeys']}/${debugInfo['totalKeys']}');
    }

    while (retryCount < maxRetries) {
      try {
        final result = await requestFunction();

        if (kDebugMode && retryCount > 0) {
          print('GeminiService: Request succeeded after $retryCount retries');
        }

        return result;
      } catch (error) {
        retryCount++;

        if (kDebugMode) {
          print('GeminiService: Request attempt $retryCount failed: $error');
        }

        // Check if this is a rate limit error
        if (RateLimitDetector.isRateLimitError(error)) {
          final currentKey = ApiKeyRotationService.getCurrentApiKey();
          failedKeys.add(currentKey);

          if (kDebugMode) {
            print(
                'GeminiService: Rate limit detected for key ending in ...${currentKey.substring(currentKey.length - 4)}');
            print(
                'GeminiService: Rotating to next API key (attempt $retryCount/$maxRetries)');
          }

          // Rotate to next API key
          await ApiKeyRotationService.rotateApiKey(failedKey: currentKey);

          // Reinitialize models with new API key
          _clearModelInstances();
          _initializeTextModels();
          _initializeVisionModels();

          // Check if all keys are rate-limited
          if (ApiKeyRotationService.areAllKeysRateLimited()) {
            if (kDebugMode) {
              print('GeminiService: All API keys are rate-limited');
            }

            // Exponential backoff when all keys are rate-limited
            final baseDelay = RateLimitDetector.getRetryDelay(error);
            final exponentialDelay =
                baseDelay * (1 << (retryCount - 1)); // 2^(retryCount-1)
            final maxDelay = 300; // 5 minutes max
            final actualDelay =
                exponentialDelay > maxDelay ? maxDelay : exponentialDelay;

            if (kDebugMode) {
              print(
                  'GeminiService: Implementing exponential backoff: ${actualDelay}s');
            }

            await Future.delayed(Duration(seconds: actualDelay));
          } else {
            // Short delay before trying next key
            await Future.delayed(const Duration(seconds: 2));
          }

          // Continue to next retry
          continue;
        } else {
          // Not a rate limit error, check if we should retry
          if (retryCount < maxRetries) {
            // Short delay for non-rate-limit errors
            await Future.delayed(Duration(seconds: retryCount * 2));
            continue;
          } else {
            // Max retries reached for non-rate-limit error
            rethrow;
          }
        }
      }
    }

    // If we've exhausted all retries
    final debugInfo = ApiKeyRotationService.getDebugInfo();
    if (kDebugMode) {
      print('GeminiService: Request failed after $maxRetries retries');
      print('GeminiService: Failed keys: ${failedKeys.length}');
      print('GeminiService: Current state: $debugInfo');
    }

    throw Exception(
        'Request failed after $maxRetries retries. All API keys may be rate-limited. ${RateLimitDetector.getRateLimitErrorMessage()}');
  }

  /// Attempt text request with current model and API key rotation
  static Future<String> _attemptTextRequestWithCurrentModel(
      List<Content> messages,
      {String? responseLength,
      required int modelIndex}) async {
    return await _attemptRequestWithApiKeyRotation(() async {
      GenerativeModel? modelInstance;
      String currentModel;
      final modelChain = _getTextModelChain();

      switch (modelIndex) {
        case 0:
          modelInstance = _primaryTextModelInstance;
          currentModel = modelChain[0];
          break;
        case 1:
          modelInstance = _fallbackTextModel1Instance;
          currentModel = modelChain[1];
          break;
        case 2:
          modelInstance = _fallbackTextModel2Instance;
          currentModel = modelChain[2];
          break;
        default:
          throw Exception('Invalid model index: $modelIndex');
      }

      if (modelInstance == null) {
        throw Exception(
            'Model instance not initialized for index: $modelIndex');
      }

      // Apply timeout only for default model, premium model has no timeout restrictions
      final Future<GenerateContentResponse> responseFeature;
      if (currentModel == premiumModel) {
        // No timeout for premium model
        responseFeature = modelInstance.generateContent(messages);
      } else {
        // Apply timeout for default model
        responseFeature = modelInstance
            .generateContent(messages)
            .timeout(const Duration(seconds: _timeoutSeconds));
      }

      final response = await responseFeature;
      final result = response.text;
      if (result != null && result.isNotEmpty) {
        _lastUsedTextModel = currentModel;
        return result;
      } else {
        throw Exception('Empty response from model');
      }
    });
  }

  /// Analyze an image with travel-focused prompts
  static Future<String> analyzeImage(String imagePath, String prompt) async {
    // Check network connectivity
    final hasInternet = await NetworkHelper.hasInternetConnection();
    if (!hasInternet) {
      throw Exception(
          'No internet connection available. Please check your network settings and try again.');
    }

    _initializeVisionModels();

    try {
      // Read image file
      final imageFile = File(imagePath);
      if (!await imageFile.exists()) {
        throw Exception('Image file not found: $imagePath');
      }

      final imageBytes = await imageFile.readAsBytes();

      // Create image part for Gemini
      final imagePart = DataPart('image/jpeg', imageBytes);

      // Enhanced travel-focused prompt
      final enhancedPrompt = '''
$prompt

Please provide a comprehensive analysis focusing on:
1. Location identification (landmarks, cities, countries, regions)
2. Travel-related content (hotels, restaurants, attractions, transportation)
3. Scene description and travel recommendations
4. Cultural or historical significance if applicable
5. Practical travel tips for this location or type of place
6. Best times to visit, local customs, or interesting facts

Be detailed and informative, as this is for travel planning purposes.
''';

      // Attempt request with vision model failover
      return await _attemptVisionRequestWithModelFailover(
          enhancedPrompt, imagePart);
    } catch (e) {
      if (kDebugMode) {
        print('GeminiService: Image analysis failed: $e');
      }

      // Return a helpful error message
      return '''I can see you've shared an image, but I'm currently unable to analyze images directly. However, I'd be happy to help you with travel information if you can describe what you see in the image or tell me the location!

Some things I can help with:
- Travel recommendations for specific destinations
- Information about landmarks and attractions
- Cultural insights and travel tips
- Best times to visit places
- Local customs and etiquette
- Transportation options

Please feel free to describe the image or ask me about any travel-related topics!''';
    }
  }

  /// Attempt API request with model failover for vision analysis
  static Future<String> _attemptVisionRequestWithModelFailover(
      String prompt, DataPart imagePart) async {
    final modelChain = _getVisionModelChain();
    final totalModels = modelChain.length;
    final modelErrors = <String, dynamic>{};

    // Try each model in the failover chain
    for (int modelIndex = 0; modelIndex < totalModels; modelIndex++) {
      final currentModel = modelChain[modelIndex];
      _currentVisionModelIndex = modelIndex;

      if (kDebugMode) {
        print(
            'GeminiService: Trying vision model ${modelIndex + 1}/$totalModels: $currentModel');
      }

      try {
        final response = await _attemptVisionRequestWithCurrentModel(
            prompt, imagePart, modelIndex);

        if (kDebugMode) {
          print('GeminiService: Success with vision model: $currentModel');
        }

        return response;
      } catch (error) {
        modelErrors[currentModel] = error;

        if (kDebugMode) {
          print('GeminiService: Vision model $currentModel failed: $error');
        }

        // Continue to next model if available
        if (modelIndex < totalModels - 1) {
          continue;
        }
      }
    }

    // If all models failed, throw the last error
    final lastModel = modelChain.last;
    final lastError = modelErrors[lastModel];
    throw Exception(
        'All vision models failed. Last error from $lastModel: $lastError');
  }

  /// Attempt vision request with current model and API key rotation
  static Future<String> _attemptVisionRequestWithCurrentModel(
      String prompt, DataPart imagePart, int modelIndex) async {
    return await _attemptRequestWithApiKeyRotation(() async {
      GenerativeModel? modelInstance;
      String currentModel;
      final modelChain = _getVisionModelChain();

      switch (modelIndex) {
        case 0:
          modelInstance = _primaryVisionModelInstance;
          currentModel = modelChain[0];
          break;
        case 1:
          modelInstance = _fallbackVisionModel1Instance;
          currentModel = modelChain[1];
          break;
        case 2:
          modelInstance = _fallbackVisionModel2Instance;
          currentModel = modelChain[2];
          break;
        default:
          throw Exception('Invalid vision model index: $modelIndex');
      }

      if (modelInstance == null) {
        throw Exception(
            'Vision model instance not initialized for index: $modelIndex');
      }

      // Apply timeout only for default model, premium model has no timeout restrictions
      final Future<GenerateContentResponse> responseFeature;
      if (currentModel == premiumModel) {
        // No timeout for premium model
        responseFeature = modelInstance.generateContent([
          Content.multi([
            TextPart(prompt),
            imagePart,
          ])
        ]);
      } else {
        // Apply timeout for default model (give vision more time)
        responseFeature = modelInstance.generateContent([
          Content.multi([
            TextPart(prompt),
            imagePart,
          ])
        ]).timeout(const Duration(seconds: _timeoutSeconds * 2));
      }

      final response = await responseFeature;
      final result = response.text;
      if (result != null && result.isNotEmpty) {
        _lastUsedVisionModel = currentModel;
        return result;
      } else {
        throw Exception('Empty response from vision model');
      }
    });
  }

  /// Track response quality for learning system
  static Future<void> _trackResponseQuality(
      String userQuery, String aiResponse) async {
    try {
      // Check if learning is enabled
      final configService = LearningConfigurationService.instance;
      await configService.initialize();

      if (configService.isLearningAllowed()) {
        // Initialize response optimization service
        await ResponseOptimizationService.instance.initialize();

        // Get historical feedback for context
        await AILearningService.instance.initialize();

        // Analyze response quality (historical feedback would be passed here in full implementation)
        await ResponseOptimizationService.instance.analyzeResponseQuality(
          userQuery,
          aiResponse,
          [], // Historical feedback would be passed here
        );

        if (kDebugMode) {
          print('GeminiService: Response quality analysis completed');
        }
      }
    } catch (error) {
      if (kDebugMode) {
        print('GeminiService: Response quality tracking failed: $error');
      }
      // Don't rethrow - this is optional functionality
    }
  }

  /// Get error message for user display
  static String getErrorMessage(dynamic error) {
    final errorString = error.toString().toLowerCase();

    if (errorString.contains('no internet') ||
        errorString.contains('network') ||
        errorString.contains('connection')) {
      return 'Network connection failed. Please check your internet connection and try again.';
    } else if (errorString.contains('timeout')) {
      return 'Request timed out. Please try again.';
    } else if (errorString.contains('rate limit') ||
        errorString.contains('quota')) {
      return 'Service temporarily unavailable due to high demand. Please try again in a moment.';
    } else if (errorString.contains('authentication') ||
        errorString.contains('unauthorized')) {
      return 'Authentication failed. Please try again later.';
    } else if (errorString.contains('generation stopped')) {
      return 'Response generation was stopped.';
    } else {
      return 'An unexpected error occurred. Please try again.';
    }
  }

  /// Get the current text model being used (for debugging)
  static String getCurrentTextModel() {
    final modelChain = _getTextModelChain();
    return modelChain[_currentTextModelIndex];
  }

  /// Get the current vision model being used (for debugging)
  static String getCurrentVisionModel() {
    final modelChain = _getVisionModelChain();
    return modelChain[_currentVisionModelIndex];
  }

  /// Get the last used text model (for debugging)
  static String? getLastUsedTextModel() {
    return _lastUsedTextModel;
  }

  /// Get the last used vision model (for debugging)
  static String? getLastUsedVisionModel() {
    return _lastUsedVisionModel;
  }

  /// Get the text model failover chain
  static List<String> getTextModelFailoverChain() {
    return List.from(_getTextModelChain());
  }

  /// Get the vision model failover chain
  static List<String> getVisionModelFailoverChain() {
    return List.from(_getVisionModelChain());
  }

  /// Reset text model index to primary model
  static void resetToPrimaryTextModel() {
    _currentTextModelIndex = 0;
  }

  /// Reset vision model index to primary model
  static void resetToPrimaryVisionModel() {
    _currentVisionModelIndex = 0;
  }

  /// Get current text model index
  static int getCurrentTextModelIndex() {
    return _currentTextModelIndex;
  }

  /// Get current vision model index
  static int getCurrentVisionModelIndex() {
    return _currentVisionModelIndex;
  }

  /// Get comprehensive debug information about API key rotation and service state
  static Map<String, dynamic> getApiKeyRotationDebugInfo() {
    final rotationInfo = ApiKeyRotationService.getDebugInfo();

    return {
      'apiKeyRotation': rotationInfo,
      'currentTextModel': _lastUsedTextModel,
      'currentVisionModel': _lastUsedVisionModel,
      'textModelIndex': _currentTextModelIndex,
      'visionModelIndex': _currentVisionModelIndex,
      'selectedModel': _selectedModel,
      'isPremiumModel': isPremiumModelSelected(),
      'modelInstancesInitialized': {
        'primaryText': _primaryTextModelInstance != null,
        'fallbackText1': _fallbackTextModel1Instance != null,
        'fallbackText2': _fallbackTextModel2Instance != null,
        'primaryVision': _primaryVisionModelInstance != null,
        'fallbackVision1': _fallbackVisionModel1Instance != null,
        'fallbackVision2': _fallbackVisionModel2Instance != null,
      },
    };
  }

  /// Log API key rotation event for debugging
  static void _logApiKeyRotation(String event,
      {Map<String, dynamic>? details}) {
    if (kDebugMode) {
      print('GeminiService API Key Rotation: $event');
      if (details != null) {
        for (final entry in details.entries) {
          print('  ${entry.key}: ${entry.value}');
        }
      }
    }
  }

  /// Force refresh API keys from CSV source
  static Future<void> refreshApiKeys() async {
    try {
      if (kDebugMode) {
        print('GeminiService: Refreshing API keys...');
      }

      await ApiKeyRotationService.refreshApiKeys();

      // Reinitialize models with potentially new keys
      _clearModelInstances();
      _initializeTextModels();
      _initializeVisionModels();

      if (kDebugMode) {
        final debugInfo = ApiKeyRotationService.getDebugInfo();
        print(
            'GeminiService: API keys refreshed. Available: ${debugInfo['availableKeys']}/${debugInfo['totalKeys']}');
      }
    } catch (error) {
      if (kDebugMode) {
        print('GeminiService: Failed to refresh API keys: $error');
      }
    }
  }
}
