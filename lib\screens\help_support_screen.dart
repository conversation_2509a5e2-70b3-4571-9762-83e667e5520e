import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import '../generated/l10n/app_localizations.dart';
import '../widgets/help_support_chatbot.dart';
import '../widgets/error_report_dialog.dart';
import '../services/error_report_service.dart';

// Simple message model for help support chat
class HelpSupportMessage {
  final String text;
  final bool isUser;
  final DateTime timestamp;

  HelpSupportMessage({
    required this.text,
    required this.isUser,
    required this.timestamp,
  });
}

class HelpSupportScreen extends StatefulWidget {
  const HelpSupportScreen({super.key});

  @override
  State<HelpSupportScreen> createState() => _HelpSupportScreenState();
}

class _HelpSupportScreenState extends State<HelpSupportScreen>
    with TickerProviderStateMixin {
  final List<HelpSupportMessage> _messages = [];
  final TextEditingController _textController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  late AnimationController _fadeAnimationController;
  late Animation<double> _fadeAnimation;

  late AnimationController _bubbleAnimationController;
  late Animation<double> _bubbleFadeAnimation;
  late Animation<double> _bubbleScaleAnimation;

  bool _isTyping = false;
  bool _hasInitializedMessages = false;

  @override
  void initState() {
    super.initState();

    // Initialize animations
    _fadeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
          parent: _fadeAnimationController, curve: Curves.easeInOut),
    );

    _bubbleAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _bubbleFadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
          parent: _bubbleAnimationController, curve: Curves.easeInOut),
    );
    _bubbleScaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(
          parent: _bubbleAnimationController, curve: Curves.easeInOut),
    );

    // Start fade animation
    _fadeAnimationController.forward();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Add initial greeting messages once localization is available
    if (!_hasInitializedMessages) {
      _addInitialMessages();
      _hasInitializedMessages = true;
    }
  }

  @override
  void dispose() {
    _textController.dispose();
    _scrollController.dispose();
    _fadeAnimationController.dispose();
    _bubbleAnimationController.dispose();
    super.dispose();
  }

  void _addInitialMessages() {
    final greeting = HelpSupportMessage(
      text: AppLocalizations.of(context).helpSupportGreeting,
      isUser: false,
      timestamp: DateTime.now(),
    );

    final welcome = HelpSupportMessage(
      text: AppLocalizations.of(context).helpSupportWelcome,
      isUser: false,
      timestamp: DateTime.now().add(const Duration(milliseconds: 500)),
    );

    final features = HelpSupportMessage(
      text: AppLocalizations.of(context).helpSupportFeatures,
      isUser: false,
      timestamp: DateTime.now().add(const Duration(milliseconds: 1000)),
    );

    final askQuestion = HelpSupportMessage(
      text: AppLocalizations.of(context).helpSupportAskQuestion,
      isUser: false,
      timestamp: DateTime.now().add(const Duration(milliseconds: 1500)),
    );

    setState(() {
      _messages.addAll([greeting, welcome, features, askQuestion]);
    });

    // Animate initial messages
    _bubbleAnimationController.forward();
    _scrollToBottom();
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  void _sendMessage(String text) async {
    if (text.trim().isEmpty) return;

    // Create user message
    final userMessage = HelpSupportMessage(
      text: text,
      isUser: true,
      timestamp: DateTime.now(),
    );

    setState(() {
      _messages.add(userMessage);
      _isTyping = true;
    });

    _textController.clear();
    _scrollToBottom();

    // Check if user wants to report an issue
    if (_isReportRequest(text)) {
      setState(() {
        _isTyping = false;
      });
      _showErrorReportDialog();
      return;
    }

    // Simulate typing delay
    await Future.delayed(const Duration(milliseconds: 1000));

    if (!mounted) return;

    // Generate response
    final response = HelpSupportChatbot.generateResponse(text, context);
    final aiMessage = HelpSupportMessage(
      text: response,
      isUser: false,
      timestamp: DateTime.now(),
    );

    setState(() {
      _messages.add(aiMessage);
      _isTyping = false;
    });

    _bubbleAnimationController.reset();
    _bubbleAnimationController.forward();
    _scrollToBottom();
  }

  bool _isReportRequest(String text) {
    final lowerText = text.toLowerCase();
    final reportKeywords = [
      'report',
      'bug',
      'error',
      'problem',
      'issue',
      'not working',
      'broken',
      'crash',
      'freeze',
      'glitch',
      'submit report'
    ];

    return reportKeywords.any((keyword) => lowerText.contains(keyword));
  }

  void _showErrorReportDialog() {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return ErrorReportDialog(
          onSubmit: (category, name, email, description, screenshot) async {
            Navigator.of(context).pop();
            // Handle report submission
            await _handleReportSubmission(
                category, name, email, description, screenshot);
          },
          onCancel: () {
            Navigator.of(context).pop();
          },
        );
      },
    );
  }

  Future<void> _handleReportSubmission(
    String category,
    String name,
    String email,
    String description,
    String? screenshot,
  ) async {
    // Submit the error report
    final success = await ErrorReportService.submitErrorReport(
      category: category,
      name: name,
      email: email,
      description: description,
      screenshot: screenshot,
    );

    if (!mounted) return;

    // Add confirmation message
    final confirmationMessage = HelpSupportMessage(
      text: success
          ? AppLocalizations.of(context).reportSubmittedSuccess
          : AppLocalizations.of(context).reportSubmissionFailed,
      isUser: false,
      timestamp: DateTime.now(),
    );

    setState(() {
      _messages.add(confirmationMessage);
    });

    _bubbleAnimationController.reset();
    _bubbleAnimationController.forward();
    _scrollToBottom();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F9FC),
      appBar: AppBar(
        backgroundColor: const Color(0xFFF7F9FC),
        surfaceTintColor: const Color(0xFFF7F9FC),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Color(0xFF2D3748)),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          AppLocalizations.of(context).helpSupport,
          style: GoogleFonts.instrumentSans(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF2D3748),
          ),
        ),
        centerTitle: true,
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          children: [
            // Chat messages area
            Expanded(
              child: _buildChatContent(),
            ),
            // Chat input area
            _buildChatInput(),
          ],
        ),
      ),
    );
  }

  Widget _buildChatContent() {
    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      itemCount: _messages.length + (_isTyping ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == _messages.length && _isTyping) {
          return _buildTypingIndicator();
        }

        final message = _messages[index];
        return _buildMessageBubble(message);
      },
    );
  }

  Widget _buildMessageBubble(HelpSupportMessage message) {
    return AnimatedBuilder(
      animation: _bubbleAnimationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _bubbleFadeAnimation,
          child: ScaleTransition(
            scale: _bubbleScaleAnimation,
            child: Container(
              margin: const EdgeInsets.symmetric(vertical: 4),
              child: Row(
                mainAxisAlignment: message.isUser
                    ? MainAxisAlignment.end
                    : MainAxisAlignment.start,
                children: [
                  if (!message.isUser) ...[
                    Container(
                      width: 32,
                      height: 32,
                      decoration: const BoxDecoration(
                        color: Color(0xFF0D76FF),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.support_agent,
                        color: Colors.white,
                        size: 18,
                      ),
                    ),
                    const SizedBox(width: 8),
                  ],
                  Flexible(
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                      decoration: BoxDecoration(
                        color: message.isUser
                            ? const Color(0xFF0D76FF)
                            : Colors.white,
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.05),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: message.isUser
                          ? Text(
                              message.text,
                              style: GoogleFonts.instrumentSans(
                                fontSize: 14,
                                color: Colors.white,
                                height: 1.4,
                              ),
                            )
                          : MarkdownBody(
                              data: message.text,
                              styleSheet: MarkdownStyleSheet(
                                p: GoogleFonts.instrumentSans(
                                  fontSize: 14,
                                  color: const Color(0xFF2D3748),
                                  height: 1.4,
                                ),
                                strong: GoogleFonts.instrumentSans(
                                  fontSize: 14,
                                  color: const Color(0xFF2D3748),
                                  fontWeight: FontWeight.w600,
                                  height: 1.4,
                                ),
                                em: GoogleFonts.instrumentSans(
                                  fontSize: 14,
                                  color: const Color(0xFF718096),
                                  fontStyle: FontStyle.italic,
                                  height: 1.4,
                                ),
                                listBullet: GoogleFonts.instrumentSans(
                                  fontSize: 14,
                                  color: const Color(0xFF2D3748),
                                  height: 1.4,
                                ),
                                h1: GoogleFonts.instrumentSans(
                                  fontSize: 16,
                                  color: const Color(0xFF2D3748),
                                  fontWeight: FontWeight.w600,
                                  height: 1.3,
                                ),
                                h2: GoogleFonts.instrumentSans(
                                  fontSize: 15,
                                  color: const Color(0xFF2D3748),
                                  fontWeight: FontWeight.w600,
                                  height: 1.3,
                                ),
                                blockSpacing: 8.0,
                              ),
                              selectable: false,
                            ),
                    ),
                  ),
                  if (message.isUser) ...[
                    const SizedBox(width: 8),
                    Container(
                      width: 32,
                      height: 32,
                      decoration: const BoxDecoration(
                        color: Color(0xFF718096),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.person,
                        color: Colors.white,
                        size: 18,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildTypingIndicator() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: const BoxDecoration(
              color: Color(0xFF0D76FF),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.support_agent,
              color: Colors.white,
              size: 18,
            ),
          ),
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: const SizedBox(
              width: 40,
              height: 20,
              child: Center(
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF0D76FF)),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChatInput() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(color: Color(0xFFE2E8F0), width: 1),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: const Color(0xFFF7F9FC),
                borderRadius: BorderRadius.circular(24),
                border: Border.all(color: const Color(0xFFE2E8F0)),
              ),
              child: TextField(
                controller: _textController,
                decoration: InputDecoration(
                  hintText: AppLocalizations.of(context).howCanWeHelpYou,
                  hintStyle: GoogleFonts.instrumentSans(
                    color: const Color(0xFF718096),
                    fontSize: 14,
                  ),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
                style: GoogleFonts.instrumentSans(
                  fontSize: 14,
                  color: const Color(0xFF2D3748),
                ),
                onSubmitted: _sendMessage,
                textInputAction: TextInputAction.send,
              ),
            ),
          ),
          const SizedBox(width: 8),
          Container(
            decoration: const BoxDecoration(
              color: Color(0xFF0D76FF),
              shape: BoxShape.circle,
            ),
            child: IconButton(
              onPressed: () => _sendMessage(_textController.text),
              icon: const Icon(
                Icons.send,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
