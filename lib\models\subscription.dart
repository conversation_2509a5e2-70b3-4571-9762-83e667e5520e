enum SubscriptionPlan {
  none,
  basic,
  premium,
}

enum SubscriptionDuration {
  weekly,
  monthly,
  quarterly,
  biannual,
}

class SubscriptionTier {
  final String id;
  final String name;
  final SubscriptionPlan plan;
  final SubscriptionDuration duration;
  final double price;
  final String billingCycle;
  final List<String> features;
  final bool isPopular;
  final int freeTrialDays;
  final String? discountText;
  final String revenueCatProductId;

  const SubscriptionTier({
    required this.id,
    required this.name,
    required this.plan,
    required this.duration,
    required this.price,
    required this.billingCycle,
    required this.features,
    required this.revenueCatProductId,
    this.isPopular = false,
    this.freeTrialDays = 7,
    this.discountText,
  });

  static const List<SubscriptionTier> availableTiers = [
    SubscriptionTier(
      id: 'weekly_subscription',
      name: '1 Week',
      plan: SubscriptionPlan.basic,
      duration: SubscriptionDuration.weekly,
      price: 1.00,
      billingCycle: 'Billed weekly',
      revenueCatProductId: 'tripwisego_weekly_1_00',
      features: [
        'Premium Place Recommendations',
        'Location Recommendation Swiping (100 swipes/day)',
        'AI-Powered Travel Planner',
        'Unlimited Quizzes & Fun Facts',
        'No-Ads',
      ],
      freeTrialDays: 0,
    ),
    SubscriptionTier(
      id: 'monthly_subscription',
      name: '1 Month',
      plan: SubscriptionPlan.basic,
      duration: SubscriptionDuration.monthly,
      price: 3.00,
      billingCycle: 'Billed monthly',
      revenueCatProductId: 'tripwisego_monthly_3_00',
      features: [
        'Premium Place Recommendations',
        'Location Recommendation Swiping (100 swipes/day)',
        'AI-Powered Travel Planner',
        'Unlimited Quizzes & Fun Facts',
        'No-Ads',
      ],
      freeTrialDays: 0,
    ),
    SubscriptionTier(
      id: 'quarterly_subscription',
      name: '3 Months',
      plan: SubscriptionPlan.basic,
      duration: SubscriptionDuration.quarterly,
      price: 5.00,
      billingCycle: 'Billed every 3 months',
      revenueCatProductId: 'tripwisego_quarterly_5_00',
      features: [
        'Premium Place Recommendations',
        'Location Recommendation Swiping (100 swipes/day)',
        'AI-Powered Travel Planner',
        'Unlimited Quizzes & Fun Facts',
        'No-Ads',
      ],
      freeTrialDays: 0,
      isPopular: true,
      discountText: 'BEST VALUE',
    ),
  ];

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'plan': plan.name,
      'duration': duration.name,
      'price': price,
      'billingCycle': billingCycle,
      'revenueCatProductId': revenueCatProductId,
      'features': features,
      'isPopular': isPopular,
      'freeTrialDays': freeTrialDays,
      'discountText': discountText,
    };
  }

  static SubscriptionTier fromJson(Map<String, dynamic> json) {
    return SubscriptionTier(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      plan: SubscriptionPlan.values.firstWhere(
        (e) => e.name == json['plan'],
        orElse: () => SubscriptionPlan.none,
      ),
      duration: SubscriptionDuration.values.firstWhere(
        (e) => e.name == json['duration'],
        orElse: () => SubscriptionDuration.monthly,
      ),
      price: (json['price'] ?? 0.0).toDouble(),
      billingCycle: json['billingCycle'] ?? '',
      revenueCatProductId: json['revenueCatProductId'] ?? '',
      features: List<String>.from(json['features'] ?? []),
      isPopular: json['isPopular'] ?? false,
      freeTrialDays: json['freeTrialDays'] ?? 7,
      discountText: json['discountText'],
    );
  }
}

class UserSubscription {
  final String id;
  final String userId;
  final SubscriptionPlan plan;
  final SubscriptionTier tier;
  final DateTime startDate;
  final DateTime endDate;
  final bool isActive;
  final bool isInTrial;
  final DateTime? trialEndDate;
  final String status; // active, expired, cancelled, trial
  final String? paymentMethod;
  final DateTime? nextBillingDate;

  const UserSubscription({
    required this.id,
    required this.userId,
    required this.plan,
    required this.tier,
    required this.startDate,
    required this.endDate,
    required this.isActive,
    required this.isInTrial,
    this.trialEndDate,
    required this.status,
    this.paymentMethod,
    this.nextBillingDate,
  });

  bool get hasActiveSubscription =>
      isActive && (isInTrial || DateTime.now().isBefore(endDate));

  bool get isTrialExpired =>
      isInTrial &&
      trialEndDate != null &&
      DateTime.now().isAfter(trialEndDate!);

  int get daysUntilExpiry {
    if (isInTrial && trialEndDate != null) {
      return trialEndDate!.difference(DateTime.now()).inDays;
    }
    return endDate.difference(DateTime.now()).inDays;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'plan': plan.name,
      'tier': tier.toJson(),
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'isActive': isActive,
      'isInTrial': isInTrial,
      'trialEndDate': trialEndDate?.toIso8601String(),
      'status': status,
      'paymentMethod': paymentMethod,
      'nextBillingDate': nextBillingDate?.toIso8601String(),
    };
  }

  static UserSubscription fromJson(Map<String, dynamic> json) {
    return UserSubscription(
      id: json['id'] ?? '',
      userId: json['userId'] ?? '',
      plan: SubscriptionPlan.values.firstWhere(
        (e) => e.name == json['plan'],
        orElse: () => SubscriptionPlan.none,
      ),
      tier: SubscriptionTier.fromJson(json['tier'] ?? {}),
      startDate: DateTime.tryParse(json['startDate'] ?? '') ?? DateTime.now(),
      endDate: DateTime.tryParse(json['endDate'] ?? '') ?? DateTime.now(),
      isActive: json['isActive'] ?? false,
      isInTrial: json['isInTrial'] ?? false,
      trialEndDate: json['trialEndDate'] != null
          ? DateTime.tryParse(json['trialEndDate'])
          : null,
      status: json['status'] ?? 'none',
      paymentMethod: json['paymentMethod'],
      nextBillingDate: json['nextBillingDate'] != null
          ? DateTime.tryParse(json['nextBillingDate'])
          : null,
    );
  }

  UserSubscription copyWith({
    String? id,
    String? userId,
    SubscriptionPlan? plan,
    SubscriptionTier? tier,
    DateTime? startDate,
    DateTime? endDate,
    bool? isActive,
    bool? isInTrial,
    DateTime? trialEndDate,
    String? status,
    String? paymentMethod,
    DateTime? nextBillingDate,
  }) {
    return UserSubscription(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      plan: plan ?? this.plan,
      tier: tier ?? this.tier,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      isActive: isActive ?? this.isActive,
      isInTrial: isInTrial ?? this.isInTrial,
      trialEndDate: trialEndDate ?? this.trialEndDate,
      status: status ?? this.status,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      nextBillingDate: nextBillingDate ?? this.nextBillingDate,
    );
  }
}
