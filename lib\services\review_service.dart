import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:in_app_review/in_app_review.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:package_info_plus/package_info_plus.dart';
import '../generated/l10n/app_localizations.dart';
import '../config/app_store_config.dart';

class ReviewService {
  static final InAppReview _inAppReview = InAppReview.instance;

  /// Shows a review dialog and handles the review process
  static Future<void> requestReview(BuildContext context) async {
    try {
      // Check if we're in debug mode
      if (kDebugMode) {
        _showDebugReviewDialog(context);
        return;
      }

      // Check if in-app review is available
      if (await _inAppReview.isAvailable()) {
        // Show custom dialog first for better UX
        final shouldProceed = await _showReviewDialog(context);
        if (shouldProceed == true && context.mounted) {
          await _inAppReview.requestReview();
          if (context.mounted) {
            _showThankYouMessage(context);
          }
        }
      } else {
        // Fallback to opening store page
        if (context.mounted) {
          await _openStorePage(context);
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Review request failed: $e');
      }
      if (context.mounted) {
        _showErrorMessage(context);
      }
    }
  }

  /// Shows a custom review dialog with app branding
  static Future<bool?> _showReviewDialog(BuildContext context) {
    return showDialog<bool>(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: const Color(0xFFF7F9FC),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // App Icon
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: const Color(0xFF0D76FF),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: const Icon(
                    Icons.travel_explore,
                    size: 40,
                    color: Colors.white,
                  ),
                ),

                const SizedBox(height: 20),

                // Title
                Text(
                  AppLocalizations.of(context).enjoyingTripWiseGo,
                  style: GoogleFonts.instrumentSans(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF2D3748),
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 12),

                // Description
                Text(
                  AppLocalizations.of(context).helpUsImproveByLeavingReview,
                  style: GoogleFonts.instrumentSans(
                    fontSize: 16,
                    color: const Color(0xFF718096),
                    height: 1.5,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 24),

                // Buttons
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => Navigator.of(context).pop(false),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: const Color(0xFF718096),
                          side: const BorderSide(color: Color(0xFF718096)),
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Text(
                          AppLocalizations.of(context).maybeLater,
                          style: GoogleFonts.instrumentSans(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () => Navigator.of(context).pop(true),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF0D76FF),
                          foregroundColor: Colors.white,
                          elevation: 0,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Text(
                          AppLocalizations.of(context).rateNow,
                          style: GoogleFonts.instrumentSans(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Shows debug dialog for development builds
  static void _showDebugReviewDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFFF7F9FC),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Text(
            AppLocalizations.of(context).rateOurApp,
            style: GoogleFonts.instrumentSans(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF2D3748),
            ),
          ),
          content: Text(
            AppLocalizations.of(context).reviewFeatureNotAvailable,
            style: GoogleFonts.instrumentSans(
              fontSize: 16,
              color: const Color(0xFF718096),
            ),
          ),
          actions: [
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF0D76FF),
                foregroundColor: Colors.white,
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                AppLocalizations.of(context).ok,
                style: GoogleFonts.instrumentSans(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// Opens the app store page as fallback
  static Future<void> _openStorePage(BuildContext context) async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      final packageName = packageInfo.packageName;

      Uri? storeUrl;

      if (Platform.isAndroid) {
        storeUrl = Uri.parse(AppStoreConfig.getAndroidStoreUrl(packageName));
      } else if (Platform.isIOS) {
        storeUrl = Uri.parse(AppStoreConfig.getIosStoreUrl());
      } else if (kIsWeb) {
        // For web, default to Play Store
        storeUrl = Uri.parse(AppStoreConfig.getWebStoreUrl(packageName));
      }

      if (storeUrl != null && await canLaunchUrl(storeUrl)) {
        await launchUrl(storeUrl, mode: LaunchMode.externalApplication);
        if (context.mounted) {
          _showThankYouMessage(context);
        }
      } else {
        if (context.mounted) {
          _showErrorMessage(context);
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Failed to open store page: $e');
      }
      if (context.mounted) {
        _showErrorMessage(context);
      }
    }
  }

  /// Shows thank you message after review
  static void _showThankYouMessage(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          AppLocalizations.of(context).thankYouForReview,
          style: GoogleFonts.instrumentSans(color: Colors.white),
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// Shows error message when review fails
  static void _showErrorMessage(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          AppLocalizations.of(context).unableToOpenStore,
          style: GoogleFonts.instrumentSans(color: Colors.white),
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        duration: const Duration(seconds: 3),
      ),
    );
  }
}
