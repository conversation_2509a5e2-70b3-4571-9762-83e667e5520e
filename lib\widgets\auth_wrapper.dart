import 'package:flutter/material.dart';
import '../services/auth_state_manager.dart';
import '../services/auth_service.dart';
import '../screens/homepage_screen.dart';
import '../screens/onboarding_screen.dart';
import '../screens/survey_screen.dart';

class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  late AuthStateManager _authStateManager;

  @override
  void initState() {
    super.initState();
    _authStateManager = AuthStateManager();
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<AuthenticationStatus>(
      stream: _authStateManager.authStatusStream,
      initialData: _authStateManager.currentStatus,
      builder: (context, snapshot) {
        final authStatus = snapshot.data ?? AuthenticationStatus.unknown;

        switch (authStatus) {
          case AuthenticationStatus.authenticated:
            // Check if user needs to complete survey
            return FutureBuilder<bool>(
              future: AuthService.needsSurveyCompletion(),
              builder: (context, surveySnapshot) {
                if (surveySnapshot.connectionState == ConnectionState.waiting) {
                  // Show loading while checking survey status
                  return const Scaffold(
                    backgroundColor: Color(0xFF0D76FF),
                    body: Center(
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    ),
                  );
                }

                final needsSurvey = surveySnapshot.data ?? false;

                if (needsSurvey) {
                  // User is authenticated but needs to complete survey
                  return const SurveyScreen();
                } else {
                  // User is authenticated and has completed survey
                  return const HomepageScreen();
                }
              },
            );
          case AuthenticationStatus.unauthenticated:
            return const OnboardingScreen();
          case AuthenticationStatus.unknown:
            // Show loading screen while determining auth status
            return const Scaffold(
              backgroundColor: Color(0xFF0D76FF),
              body: Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            );
        }
      },
    );
  }
}
