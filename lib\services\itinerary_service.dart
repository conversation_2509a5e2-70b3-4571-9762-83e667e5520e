import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/itinerary.dart';

class ItineraryService {
  static const String _storageKey = 'user_itineraries';

  // Save an itinerary to local storage
  static Future<bool> saveItinerary(Itinerary itinerary) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final existingItineraries = await getAllItineraries();

      // Add the new itinerary to the list
      existingItineraries.add(itinerary);

      // Convert to JSON and save
      final jsonList = existingItineraries.map((i) => i.toJson()).toList();
      final jsonString = jsonEncode(jsonList);

      return await prefs.setString(_storageKey, jsonString);
    } catch (e) {
      print('Error saving itinerary: $e');
      return false;
    }
  }

  // Get all itineraries from local storage
  static Future<List<Itinerary>> getAllItineraries() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_storageKey);

      if (jsonString == null || jsonString.isEmpty) {
        return [];
      }

      final jsonList = jsonDecode(jsonString) as List;
      return jsonList.map((json) => Itinerary.fromJson(json)).toList();
    } catch (e) {
      print('Error loading itineraries: $e');
      return [];
    }
  }

  // Delete an itinerary by ID
  static Future<bool> deleteItinerary(String id) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final existingItineraries = await getAllItineraries();

      // Remove the itinerary with the specified ID
      existingItineraries.removeWhere((itinerary) => itinerary.id == id);

      // Save the updated list
      final jsonList = existingItineraries.map((i) => i.toJson()).toList();
      final jsonString = jsonEncode(jsonList);

      return await prefs.setString(_storageKey, jsonString);
    } catch (e) {
      print('Error deleting itinerary: $e');
      return false;
    }
  }

  // Update an existing itinerary
  static Future<bool> updateItinerary(Itinerary updatedItinerary) async {
    try {
      if (kDebugMode) {
        print(
            'ItineraryService: Updating itinerary with ID: ${updatedItinerary.id}');
      }

      final prefs = await SharedPreferences.getInstance();
      final existingItineraries = await getAllItineraries();

      if (kDebugMode) {
        print(
            'ItineraryService: Found ${existingItineraries.length} existing itineraries');
      }

      // Find and replace the itinerary with the same ID
      final index =
          existingItineraries.indexWhere((i) => i.id == updatedItinerary.id);

      if (kDebugMode) {
        print('ItineraryService: Found itinerary at index: $index');
      }

      if (index != -1) {
        existingItineraries[index] = updatedItinerary;

        // Save the updated list
        final jsonList = existingItineraries.map((i) => i.toJson()).toList();
        final jsonString = jsonEncode(jsonList);

        final result = await prefs.setString(_storageKey, jsonString);

        if (kDebugMode) {
          print('ItineraryService: Save result: $result');
          print(
              'ItineraryService: Updated itinerary day-specific activities: ${updatedItinerary.daySpecificActivities}');
        }

        return result;
      }

      if (kDebugMode) {
        print(
            'ItineraryService: Itinerary with ID ${updatedItinerary.id} not found');
      }

      return false;
    } catch (e) {
      if (kDebugMode) {
        print('ItineraryService: Error updating itinerary: $e');
      }
      return false;
    }
  }

  // Clear all itineraries (for testing/debugging)
  static Future<bool> clearAllItineraries() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final result = await prefs.remove(_storageKey);
      print('Cleared all itineraries from storage');
      return result;
    } catch (e) {
      print('Error clearing itineraries: $e');
      return false;
    }
  }

  // Get itinerary count
  static Future<int> getItineraryCount() async {
    final itineraries = await getAllItineraries();
    return itineraries.length;
  }

  // Generate a unique ID for new itineraries
  static String generateId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }

  // Remove placeholder/invalid itineraries
  static Future<bool> removePlaceholderItineraries() async {
    try {
      final allItineraries = await getAllItineraries();
      final validItineraries = allItineraries.where((itinerary) {
        // Remove itineraries that appear to be placeholders or have generic titles
        final title = itinerary.title.toLowerCase();
        final isPlaceholder = title.contains('travel dreams') ||
            title.contains('let\'s make') ||
            title.contains('✨') ||
            title.contains('placeholder') ||
            title.contains('sample') ||
            title.contains('demo') ||
            (itinerary.destinations.isEmpty) ||
            (itinerary.dailyActivities.isEmpty &&
                (itinerary.daySpecificActivities == null ||
                    itinerary.daySpecificActivities!.isEmpty));

        return !isPlaceholder;
      }).toList();

      if (validItineraries.length != allItineraries.length) {
        // Save only the valid itineraries
        final prefs = await SharedPreferences.getInstance();
        final jsonList = validItineraries.map((i) => i.toJson()).toList();
        final jsonString = jsonEncode(jsonList);

        final success = await prefs.setString(_storageKey, jsonString);
        if (success) {
          print(
              'Removed ${allItineraries.length - validItineraries.length} placeholder itineraries');
        }
        return success;
      }

      return true; // No placeholders found
    } catch (e) {
      print('Error removing placeholder itineraries: $e');
      return false;
    }
  }
}
