import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:shimmer/shimmer.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../utils/network_helper.dart';
import '../services/image_cache_manager.dart';

/// Optimized network image widget with caching, shimmer loading, and error handling
/// Follows the app's design system with #0D76FF blue styling and fade-in animations
class OptimizedNetworkImage extends StatefulWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final BorderRadius? borderRadius;
  final bool enableFadeIn;
  final Duration fadeInDuration;
  final bool enableProgressiveLoading;
  final String? lowResImageUrl;
  final int? cacheWidth;
  final int? cacheHeight;
  final Widget? errorWidget;
  final Widget? placeholder;
  final bool enableRetry;
  final VoidCallback? onImageLoaded;
  final VoidCallback? onImageError;

  const OptimizedNetworkImage({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.borderRadius,
    this.enableFadeIn = true,
    this.fadeInDuration = const Duration(milliseconds: 300),
    this.enableProgressiveLoading = false,
    this.lowResImageUrl,
    this.cacheWidth,
    this.cacheHeight,
    this.errorWidget,
    this.placeholder,
    this.enableRetry = true,
    this.onImageLoaded,
    this.onImageError,
  });

  @override
  State<OptimizedNetworkImage> createState() => _OptimizedNetworkImageState();
}

class _OptimizedNetworkImageState extends State<OptimizedNetworkImage>
    with SingleTickerProviderStateMixin {
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;
  bool _hasError = false;
  bool _isRetrying = false;
  List<ConnectivityResult> _connectivityResult = [ConnectivityResult.none];
  final ImageCacheManager _cacheManager = ImageCacheManager();
  bool _showingLowRes = false;

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: widget.fadeInDuration,
      vsync: this,
    );
    _fadeAnimation = CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    );
    _checkConnectivity();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  Future<void> _checkConnectivity() async {
    final connectivity = Connectivity();
    final result = await connectivity.checkConnectivity();
    if (mounted) {
      setState(() {
        _connectivityResult = result;
      });
    }
  }

  String _getOptimizedImageUrl() {
    // Return appropriate image quality based on connectivity
    if (_connectivityResult.contains(ConnectivityResult.mobile)) {
      // For mobile data, use lower quality
      return _cacheManager.generateHighResUrl(
        widget.imageUrl,
        targetWidth: 600,
        targetHeight: 800,
      );
    } else if (_connectivityResult.contains(ConnectivityResult.wifi) ||
        _connectivityResult.contains(ConnectivityResult.ethernet)) {
      // For WiFi/Ethernet, use higher quality
      return _cacheManager.generateHighResUrl(
        widget.imageUrl,
        targetWidth: widget.cacheWidth ?? 800,
        targetHeight: widget.cacheHeight ?? 1000,
      );
    }
    // Default quality
    return _cacheManager.generateHighResUrl(widget.imageUrl);
  }

  String _getLowResImageUrl() {
    return widget.lowResImageUrl ??
        _cacheManager.generateLowResUrl(widget.imageUrl);
  }

  Widget _buildShimmerPlaceholder() {
    return Shimmer.fromColors(
      baseColor: const Color(0xFFF7F9FC),
      highlightColor: const Color(0xFF0D76FF).withOpacity(0.1),
      child: Container(
        width: widget.width,
        height: widget.height,
        decoration: BoxDecoration(
          color: const Color(0xFFF7F9FC),
          borderRadius: widget.borderRadius,
        ),
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        color: const Color(0xFFF7F9FC),
        borderRadius: widget.borderRadius,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            color: const Color(0xFF0D76FF).withOpacity(0.7),
            size: 32,
          ),
          const SizedBox(height: 8),
          Text(
            'Image failed to load',
            style: GoogleFonts.instrumentSans(
              fontSize: 12,
              color: const Color(0xFF718096),
            ),
            textAlign: TextAlign.center,
          ),
          if (widget.enableRetry) ...[
            const SizedBox(height: 8),
            if (_isRetrying)
              const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: Color(0xFF0D76FF),
                ),
              )
            else
              GestureDetector(
                onTap: _retryImageLoad,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: const Color(0xFF0D76FF),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'Retry',
                    style: GoogleFonts.instrumentSans(
                      fontSize: 10,
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
          ],
        ],
      ),
    );
  }

  void _retryImageLoad() async {
    if (_isRetrying) return;

    setState(() {
      _isRetrying = true;
      _hasError = false;
    });

    // Check connectivity before retrying
    await _checkConnectivity();

    // Wait a moment before retrying
    await Future.delayed(const Duration(milliseconds: 500));

    if (mounted) {
      setState(() {
        _isRetrying = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_hasError && !_isRetrying) {
      return widget.errorWidget ?? _buildErrorWidget();
    }

    Widget imageWidget;

    if (widget.enableProgressiveLoading && widget.lowResImageUrl != null) {
      // Progressive loading: show low-res first, then high-res
      imageWidget = _buildProgressiveImage();
    } else {
      // Standard loading
      imageWidget = _buildStandardImage();
    }

    if (widget.enableFadeIn) {
      imageWidget = FadeTransition(
        opacity: _fadeAnimation,
        child: imageWidget,
      );
    }

    if (widget.borderRadius != null) {
      imageWidget = ClipRRect(
        borderRadius: widget.borderRadius!,
        child: imageWidget,
      );
    }

    return imageWidget;
  }

  Widget _buildStandardImage() {
    return CachedNetworkImage(
      imageUrl: _getOptimizedImageUrl(),
      width: widget.width,
      height: widget.height,
      fit: widget.fit,
      memCacheWidth: widget.cacheWidth,
      memCacheHeight: widget.cacheHeight,
      cacheManager: _cacheManager.cacheManager,
      placeholder: (context, url) {
        return widget.placeholder ?? _buildShimmerPlaceholder();
      },
      errorWidget: (context, url, error) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            setState(() {
              _hasError = true;
            });
            widget.onImageError?.call();
          }
        });
        return widget.errorWidget ?? _buildErrorWidget();
      },
      imageBuilder: (context, imageProvider) {
        if (widget.enableFadeIn) {
          _fadeController.forward();
        }
        widget.onImageLoaded?.call();

        return Container(
          width: widget.width,
          height: widget.height,
          decoration: BoxDecoration(
            borderRadius: widget.borderRadius,
            image: DecorationImage(
              image: imageProvider,
              fit: widget.fit,
            ),
          ),
        );
      },
    );
  }

  Widget _buildProgressiveImage() {
    return Stack(
      children: [
        // Low-res image (loads first)
        CachedNetworkImage(
          imageUrl: _getLowResImageUrl(),
          width: widget.width,
          height: widget.height,
          fit: widget.fit,
          cacheManager: _cacheManager.cacheManager,
          placeholder: (context, url) {
            return widget.placeholder ?? _buildShimmerPlaceholder();
          },
          imageBuilder: (context, imageProvider) {
            setState(() {
              _showingLowRes = true;
            });
            return Container(
              width: widget.width,
              height: widget.height,
              decoration: BoxDecoration(
                borderRadius: widget.borderRadius,
                image: DecorationImage(
                  image: imageProvider,
                  fit: widget.fit,
                ),
              ),
            );
          },
        ),
        // High-res image (loads on top)
        CachedNetworkImage(
          imageUrl: _getOptimizedImageUrl(),
          width: widget.width,
          height: widget.height,
          fit: widget.fit,
          memCacheWidth: widget.cacheWidth,
          memCacheHeight: widget.cacheHeight,
          cacheManager: _cacheManager.cacheManager,
          placeholder: (context, url) => const SizedBox.shrink(),
          errorWidget: (context, url, error) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (mounted) {
                setState(() {
                  _hasError = true;
                });
                widget.onImageError?.call();
              }
            });
            return const SizedBox.shrink();
          },
          imageBuilder: (context, imageProvider) {
            if (widget.enableFadeIn) {
              _fadeController.forward();
            }
            widget.onImageLoaded?.call();

            return AnimatedOpacity(
              opacity: 1.0,
              duration: const Duration(milliseconds: 300),
              child: Container(
                width: widget.width,
                height: widget.height,
                decoration: BoxDecoration(
                  borderRadius: widget.borderRadius,
                  image: DecorationImage(
                    image: imageProvider,
                    fit: widget.fit,
                  ),
                ),
              ),
            );
          },
        ),
      ],
    );
  }
}
