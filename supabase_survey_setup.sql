-- TripWiseGo Survey System Database Setup
-- Run this SQL script in your Supabase SQL Editor

-- Create the survey_responses table
CREATE TABLE IF NOT EXISTS survey_responses (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  travel_preferences JSONB NOT NULL DEFAULT '{}',
  demographics JSONB NOT NULL DEFAULT '{}',
  travel_experience JSONB NOT NULL DEFAULT '{}',
  interests JSONB NOT NULL DEFAULT '{}',
  completed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_completed BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id)
);

-- Enable Row Level Security (RLS)
ALTER TABLE survey_responses ENABLE ROW LEVEL SECURITY;

-- Create policies for survey_responses table

-- Policy: Users can view their own survey responses
CREATE POLICY "Users can view their own survey responses" ON survey_responses
  FOR SELECT USING (auth.uid() = user_id);

-- Policy: Users can insert their own survey responses
CREATE POLICY "Users can insert their own survey responses" ON survey_responses
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Policy: Users can update their own survey responses
CREATE POLICY "Users can update their own survey responses" ON survey_responses
  FOR UPDATE USING (auth.uid() = user_id);

-- Policy: Users can delete their own survey responses (optional)
CREATE POLICY "Users can delete their own survey responses" ON survey_responses
  FOR DELETE USING (auth.uid() = user_id);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_survey_responses_user_id ON survey_responses(user_id);
CREATE INDEX IF NOT EXISTS idx_survey_responses_completed ON survey_responses(is_completed);
CREATE INDEX IF NOT EXISTS idx_survey_responses_completed_at ON survey_responses(completed_at);

-- Create a function to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at on row updates
CREATE TRIGGER update_survey_responses_updated_at 
    BEFORE UPDATE ON survey_responses 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Optional: Create a view for survey statistics (admin use)
CREATE OR REPLACE VIEW survey_statistics AS
SELECT 
    COUNT(*) as total_responses,
    COUNT(*) FILTER (WHERE is_completed = true) as completed_responses,
    COUNT(*) FILTER (WHERE is_completed = false) as incomplete_responses,
    ROUND(
        (COUNT(*) FILTER (WHERE is_completed = true)::DECIMAL / COUNT(*)) * 100, 
        2
    ) as completion_percentage,
    DATE_TRUNC('day', completed_at) as completion_date,
    COUNT(*) as daily_completions
FROM survey_responses 
WHERE completed_at IS NOT NULL
GROUP BY DATE_TRUNC('day', completed_at)
ORDER BY completion_date DESC;

-- Optional: Create a function to get user survey status
CREATE OR REPLACE FUNCTION get_user_survey_status(user_uuid UUID)
RETURNS TABLE(
    has_survey BOOLEAN,
    is_completed BOOLEAN,
    completed_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        EXISTS(SELECT 1 FROM survey_responses WHERE user_id = user_uuid) as has_survey,
        COALESCE(sr.is_completed, false) as is_completed,
        sr.completed_at
    FROM survey_responses sr
    WHERE sr.user_id = user_uuid
    LIMIT 1;
    
    -- If no survey found, return default values
    IF NOT FOUND THEN
        RETURN QUERY SELECT false, false, NULL::TIMESTAMP WITH TIME ZONE;
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on the function to authenticated users
GRANT EXECUTE ON FUNCTION get_user_survey_status(UUID) TO authenticated;

-- Optional: Create a function to validate survey data structure
CREATE OR REPLACE FUNCTION validate_survey_data(
    travel_prefs JSONB,
    demographics JSONB,
    travel_exp JSONB,
    interests JSONB
)
RETURNS BOOLEAN AS $$
BEGIN
    -- Check if required fields exist in travel_preferences
    IF NOT (travel_prefs ? 'budget_range' AND 
            travel_prefs ? 'travel_style' AND 
            travel_prefs ? 'group_size') THEN
        RETURN FALSE;
    END IF;
    
    -- Check if required fields exist in demographics
    IF NOT (demographics ? 'age_range' AND 
            demographics ? 'gender') THEN
        RETURN FALSE;
    END IF;
    
    -- Check if required fields exist in travel_experience
    IF NOT (travel_exp ? 'experience_level' AND 
            travel_exp ? 'longest_trip_duration') THEN
        RETURN FALSE;
    END IF;
    
    -- Check if required fields exist in interests
    IF NOT (interests ? 'activities') THEN
        RETURN FALSE;
    END IF;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Add a check constraint to ensure survey data is valid (optional)
-- ALTER TABLE survey_responses 
-- ADD CONSTRAINT valid_survey_data 
-- CHECK (validate_survey_data(travel_preferences, demographics, travel_experience, interests));

-- Insert some sample data for testing (optional - remove in production)
-- INSERT INTO survey_responses (
--     user_id,
--     travel_preferences,
--     demographics,
--     travel_experience,
--     interests,
--     is_completed
-- ) VALUES (
--     auth.uid(), -- This will only work if run by an authenticated user
--     '{"budget_range": "Mid-range ($50-150/day)", "travel_style": "Mix of Both", "group_size": "Couple"}',
--     '{"age_range": "25-34", "gender": "Prefer not to say"}',
--     '{"experience_level": "Intermediate (3-10 trips)", "longest_trip_duration": "Medium trip (1-2 weeks)"}',
--     '{"activities": ["Sightseeing", "Photography", "Food Experiences"]}',
--     true
-- );

-- Create a comment on the table for documentation
COMMENT ON TABLE survey_responses IS 'Stores user survey responses for travel preferences and demographics';
COMMENT ON COLUMN survey_responses.user_id IS 'References the authenticated user who completed the survey';
COMMENT ON COLUMN survey_responses.travel_preferences IS 'JSON object containing budget, destinations, travel style, etc.';
COMMENT ON COLUMN survey_responses.demographics IS 'JSON object containing age, gender, location, occupation';
COMMENT ON COLUMN survey_responses.travel_experience IS 'JSON object containing experience level, trips per year, etc.';
COMMENT ON COLUMN survey_responses.interests IS 'JSON object containing activities, cuisine, cultural interests, etc.';
COMMENT ON COLUMN survey_responses.is_completed IS 'Boolean flag indicating if the survey was fully completed';

-- Success message
SELECT 'TripWiseGo Survey System database setup completed successfully!' as message;
