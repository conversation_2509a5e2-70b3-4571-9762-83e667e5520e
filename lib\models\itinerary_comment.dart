class ItineraryComment {
  final String id;
  final String itineraryId;
  final String userId;
  final String content;
  final String? parentCommentId;
  final DateTime createdAt;
  final DateTime updatedAt;

  // Activity-specific fields
  final String? activityId;
  final int? activityDay;
  final String commentType; // 'itinerary' or 'activity'

  // User information (populated from joins)
  final String? userName;
  final String? userEmail;
  final String? userAvatarUrl;

  ItineraryComment({
    required this.id,
    required this.itineraryId,
    required this.userId,
    required this.content,
    this.parentCommentId,
    required this.createdAt,
    required this.updatedAt,
    this.activityId,
    this.activityDay,
    this.commentType = 'itinerary',
    this.userName,
    this.userEmail,
    this.userAvatarUrl,
  });

  // Convert from JSON (Supabase response)
  factory ItineraryComment.fromJson(Map<String, dynamic> json) {
    return ItineraryComment(
      id: json['id']?.toString() ?? '',
      itineraryId: json['itinerary_id'] as String,
      userId: json['user_id'] as String,
      content: json['content'] as String, // Use content from database
      parentCommentId: json['parent_comment_id']?.toString(),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      activityId: json['activity_id'] as String?,
      activityDay: json['activity_day'] as int?,
      commentType: json['comment_type'] as String? ?? 'itinerary',
      userName: json['user_name'] as String?,
      userEmail: json['user_email'] as String?,
      userAvatarUrl: json['user_avatar_url'] as String?,
    );
  }

  // Convert to JSON for Supabase
  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'itinerary_id': itineraryId,
      'user_id': userId,
      'content': content,
      'parent_comment_id': parentCommentId,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'activity_id': activityId,
      'activity_day': activityDay,
      'comment_type': commentType,
    };

    // Only include id if it's not empty (for updates)
    if (id.isNotEmpty) {
      json['id'] = int.tryParse(id) ?? id;
    }

    return json;
  }

  // Create new comment
  factory ItineraryComment.create({
    required String itineraryId,
    required String userId,
    required String content,
    String? parentCommentId,
    String? activityId,
    int? activityDay,
    String commentType = 'itinerary',
  }) {
    final now = DateTime.now();
    return ItineraryComment(
      id: '', // Will be generated by Supabase
      itineraryId: itineraryId,
      userId: userId,
      content: content,
      parentCommentId: parentCommentId,
      createdAt: now,
      updatedAt: now,
      activityId: activityId,
      activityDay: activityDay,
      commentType: commentType,
    );
  }

  // Check if comment belongs to current user
  bool isOwnComment(String? currentUserId) {
    return currentUserId != null && userId == currentUserId;
  }

  // Get display name for the comment author
  String get displayName {
    if (userName != null && userName!.isNotEmpty) {
      return userName!;
    }
    if (userEmail != null && userEmail!.isNotEmpty) {
      return userEmail!.split('@').first;
    }
    return 'Anonymous';
  }

  // Get initials for avatar
  String get initials {
    final name = displayName;
    if (name.isEmpty) return '?';

    final words = name.split(' ');
    if (words.length >= 2) {
      return '${words[0][0]}${words[1][0]}'.toUpperCase();
    }
    return name.substring(0, 1).toUpperCase();
  }

  // Get relative time string
  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  // Helper getters
  bool get isReply => parentCommentId != null;
  bool get isActivityComment => commentType == 'activity';
  bool get isItineraryComment => commentType == 'itinerary';

  String get formattedDate {
    final months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];

    return '${months[createdAt.month - 1]} ${createdAt.day}, ${createdAt.year}';
  }

  // Copy with method for updates
  ItineraryComment copyWith({
    String? id,
    String? itineraryId,
    String? userId,
    String? content,
    String? parentCommentId,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? activityId,
    int? activityDay,
    String? commentType,
    String? userName,
    String? userEmail,
    String? userAvatarUrl,
  }) {
    return ItineraryComment(
      id: id ?? this.id,
      itineraryId: itineraryId ?? this.itineraryId,
      userId: userId ?? this.userId,
      content: content ?? this.content,
      parentCommentId: parentCommentId ?? this.parentCommentId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      activityId: activityId ?? this.activityId,
      activityDay: activityDay ?? this.activityDay,
      commentType: commentType ?? this.commentType,
      userName: userName ?? this.userName,
      userEmail: userEmail ?? this.userEmail,
      userAvatarUrl: userAvatarUrl ?? this.userAvatarUrl,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ItineraryComment && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'ItineraryComment(id: $id, userId: $userId, content: ${content.substring(0, content.length > 50 ? 50 : content.length)}...)';
  }
}

class CollaborationParticipant {
  final String id;
  final String itineraryId;
  final String userId;
  final String role; // 'owner', 'editor', 'viewer'
  final DateTime joinedAt;

  // User information (populated from joins)
  final String? userName;
  final String? userEmail;
  final String? userAvatarUrl;

  CollaborationParticipant({
    required this.id,
    required this.itineraryId,
    required this.userId,
    required this.role,
    required this.joinedAt,
    this.userName,
    this.userEmail,
    this.userAvatarUrl,
  });

  // Convert from JSON (Supabase response)
  factory CollaborationParticipant.fromJson(Map<String, dynamic> json) {
    return CollaborationParticipant(
      id: json['id'] as String,
      itineraryId: json['itinerary_id'] as String,
      userId: json['user_id'] as String,
      role: json['role'] as String,
      joinedAt: DateTime.parse(json['joined_at'] as String),
      userName: json['user_name'] as String?,
      userEmail: json['user_email'] as String?,
      userAvatarUrl: json['user_avatar_url'] as String?,
    );
  }

  // Convert to JSON for Supabase
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'itinerary_id': itineraryId,
      'user_id': userId,
      'role': role,
      'joined_at': joinedAt.toIso8601String(),
    };
  }

  // Helper getters
  bool get isOwner => role == 'owner';
  bool get isEditor => role == 'editor';
  bool get isViewer => role == 'viewer';

  String get displayName {
    if (userName != null && userName!.isNotEmpty) {
      return userName!;
    }
    if (userEmail != null && userEmail!.isNotEmpty) {
      return userEmail!.split('@').first;
    }
    return 'Anonymous User';
  }

  String get initials {
    final name = displayName;
    if (name.isEmpty) return 'A';

    final words = name.split(' ');
    if (words.length >= 2) {
      return '${words[0][0]}${words[1][0]}'.toUpperCase();
    }
    return name[0].toUpperCase();
  }

  String get roleDisplayName {
    switch (role) {
      case 'owner':
        return 'Owner';
      case 'editor':
        return 'Editor';
      case 'viewer':
        return 'Viewer';
      default:
        return 'Unknown';
    }
  }

  // Copy with method for updates
  CollaborationParticipant copyWith({
    String? id,
    String? itineraryId,
    String? userId,
    String? role,
    DateTime? joinedAt,
    String? userName,
    String? userEmail,
    String? userAvatarUrl,
  }) {
    return CollaborationParticipant(
      id: id ?? this.id,
      itineraryId: itineraryId ?? this.itineraryId,
      userId: userId ?? this.userId,
      role: role ?? this.role,
      joinedAt: joinedAt ?? this.joinedAt,
      userName: userName ?? this.userName,
      userEmail: userEmail ?? this.userEmail,
      userAvatarUrl: userAvatarUrl ?? this.userAvatarUrl,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CollaborationParticipant && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'CollaborationParticipant(id: $id, userId: $userId, role: $role)';
  }
}
