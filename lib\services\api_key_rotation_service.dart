import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/network_helper.dart';

/// Service for managing rotational API keys for Gemini AI service
/// Handles fetching keys from Google Sheets CSV and automatic rotation
class ApiKeyRotationService {
  static const String _csvUrl =
      'https://docs.google.com/spreadsheets/d/e/2PACX-1vSkJi1yLjXULd4V59bW3Fg-IpycJQwDS3ng05SoPFbmbqANBkrA9MKAxS7MYcBFAnOy03aAQgptU4t3/pub?gid=0&single=true&output=csv';

  static const String _fallbackApiKey =
      'AIzaSyD-w4gYmoP6TEolElZSJXIWNyMwQIhlOzY';
  static const String _currentKeyIndexKey = 'api_key_current_index';
  static const String _keysCacheKey = 'api_keys_cache';
  static const String _lastFetchTimeKey = 'api_keys_last_fetch';
  static const String _rateLimitedKeysKey = 'rate_limited_keys';

  // Cache duration: 1 hour
  static const Duration _cacheDuration = Duration(hours: 1);
  // Rate limit cooldown: 1 hour
  static const Duration _rateLimitCooldown = Duration(hours: 1);

  static List<String> _apiKeys = [];
  static int _currentKeyIndex = 0;
  static Map<String, DateTime> _rateLimitedKeys = {};
  static bool _isInitialized = false;
  static DateTime? _lastFetchTime;

  /// Initialize the API key rotation service
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      if (kDebugMode) {
        print('ApiKeyRotationService: Initializing...');
      }

      // Load cached data first
      await _loadCachedData();

      // Try to fetch fresh keys if cache is stale or empty
      if (_shouldRefreshKeys()) {
        await _fetchApiKeysFromCsv();
      }

      // Ensure we have at least the fallback key
      if (_apiKeys.isEmpty) {
        _apiKeys = [_fallbackApiKey];
        if (kDebugMode) {
          print('ApiKeyRotationService: Using fallback API key');
        }
      }

      // Clean up expired rate-limited keys
      _cleanupRateLimitedKeys();

      _isInitialized = true;

      if (kDebugMode) {
        print(
            'ApiKeyRotationService: Initialized with ${_apiKeys.length} API keys');
        print('ApiKeyRotationService: Current key index: $_currentKeyIndex');
      }
    } catch (error) {
      if (kDebugMode) {
        print('ApiKeyRotationService: Initialization failed: $error');
      }

      // Fallback to default key
      _apiKeys = [_fallbackApiKey];
      _currentKeyIndex = 0;
      _isInitialized = true;
    }
  }

  /// Get the current active API key
  static String getCurrentApiKey() {
    if (!_isInitialized || _apiKeys.isEmpty) {
      return _fallbackApiKey;
    }

    // Ensure index is within bounds
    if (_currentKeyIndex >= _apiKeys.length) {
      _currentKeyIndex = 0;
    }

    final currentKey = _apiKeys[_currentKeyIndex];

    if (kDebugMode) {
      print('ApiKeyRotationService: Using API key index $_currentKeyIndex');
    }

    return currentKey;
  }

  /// Rotate to the next API key due to rate limiting
  static Future<String> rotateApiKey({String? failedKey}) async {
    if (!_isInitialized) {
      await initialize();
    }

    if (failedKey != null) {
      // Mark the failed key as rate-limited
      _rateLimitedKeys[failedKey] = DateTime.now();
      await _saveRateLimitedKeys();

      if (kDebugMode) {
        print(
            'ApiKeyRotationService: Marked key as rate-limited: ${_maskApiKey(failedKey)}');
      }
    }

    // Find next available key
    final startIndex = _currentKeyIndex;
    int attempts = 0;

    do {
      _currentKeyIndex = (_currentKeyIndex + 1) % _apiKeys.length;
      attempts++;

      final candidateKey = _apiKeys[_currentKeyIndex];

      // Check if this key is not rate-limited
      if (!_isKeyRateLimited(candidateKey)) {
        await _saveCurrentKeyIndex();

        if (kDebugMode) {
          print(
              'ApiKeyRotationService: Rotated to key index $_currentKeyIndex');
        }

        return candidateKey;
      }
    } while (_currentKeyIndex != startIndex && attempts < _apiKeys.length);

    // All keys are rate-limited
    if (kDebugMode) {
      print('ApiKeyRotationService: All API keys are rate-limited');
    }

    // Return the current key anyway (will need retry logic)
    return _apiKeys[_currentKeyIndex];
  }

  /// Check if all API keys are currently rate-limited
  static bool areAllKeysRateLimited() {
    _cleanupRateLimitedKeys();

    for (final key in _apiKeys) {
      if (!_isKeyRateLimited(key)) {
        return false;
      }
    }

    return true;
  }

  /// Get the number of available (non-rate-limited) keys
  static int getAvailableKeyCount() {
    _cleanupRateLimitedKeys();

    int availableCount = 0;
    for (final key in _apiKeys) {
      if (!_isKeyRateLimited(key)) {
        availableCount++;
      }
    }

    return availableCount;
  }

  /// Force refresh API keys from CSV
  static Future<void> refreshApiKeys() async {
    try {
      await _fetchApiKeysFromCsv();

      if (kDebugMode) {
        print('ApiKeyRotationService: Refreshed ${_apiKeys.length} API keys');
      }
    } catch (error) {
      if (kDebugMode) {
        print('ApiKeyRotationService: Failed to refresh API keys: $error');
      }
    }
  }

  /// Get debug information about the current state
  static Map<String, dynamic> getDebugInfo() {
    return {
      'totalKeys': _apiKeys.length,
      'currentIndex': _currentKeyIndex,
      'availableKeys': getAvailableKeyCount(),
      'rateLimitedKeys': _rateLimitedKeys.length,
      'lastFetchTime': _lastFetchTime?.toIso8601String(),
      'isInitialized': _isInitialized,
    };
  }

  /// Check if we should refresh the API keys cache
  static bool _shouldRefreshKeys() {
    if (_apiKeys.isEmpty) return true;
    if (_lastFetchTime == null) return true;

    final now = DateTime.now();
    return now.difference(_lastFetchTime!) > _cacheDuration;
  }

  /// Fetch API keys from Google Sheets CSV
  static Future<void> _fetchApiKeysFromCsv() async {
    try {
      // Check network connectivity
      final hasInternet = await NetworkHelper.hasInternetConnection();
      if (!hasInternet) {
        throw Exception('No internet connection available');
      }

      if (kDebugMode) {
        print('ApiKeyRotationService: Fetching API keys from CSV...');
      }

      final response = await http.get(Uri.parse(_csvUrl)).timeout(
            const Duration(seconds: 10),
          );

      if (response.statusCode != 200) {
        throw Exception('Failed to fetch CSV: HTTP ${response.statusCode}');
      }

      final csvData = response.body;
      final keys = _parseCsvForApiKeys(csvData);

      if (keys.isNotEmpty) {
        _apiKeys = keys;
        _lastFetchTime = DateTime.now();

        // Reset current index if it's out of bounds
        if (_currentKeyIndex >= _apiKeys.length) {
          _currentKeyIndex = 0;
        }

        // Save to cache
        await _saveCachedData();

        if (kDebugMode) {
          print(
              'ApiKeyRotationService: Successfully fetched ${keys.length} API keys');
        }
      } else {
        throw Exception('No valid API keys found in CSV');
      }
    } catch (error) {
      if (kDebugMode) {
        print('ApiKeyRotationService: Failed to fetch API keys: $error');
      }
      rethrow;
    }
  }

  /// Parse CSV data to extract API keys from the first column
  static List<String> _parseCsvForApiKeys(String csvData) {
    final keys = <String>[];

    try {
      final lines = csvData.split('\n');

      for (final line in lines) {
        final trimmedLine = line.trim();
        if (trimmedLine.isEmpty) continue;

        // Split by comma and take the first column
        final columns = trimmedLine.split(',');
        if (columns.isNotEmpty) {
          final potentialKey = columns[0].trim().replaceAll('"', '');

          // Basic validation: Gemini API keys typically start with 'AIza'
          if (potentialKey.startsWith('AIza') && potentialKey.length > 20) {
            keys.add(potentialKey);
          }
        }
      }
    } catch (error) {
      if (kDebugMode) {
        print('ApiKeyRotationService: Error parsing CSV: $error');
      }
    }

    return keys;
  }

  /// Check if a specific API key is currently rate-limited
  static bool _isKeyRateLimited(String apiKey) {
    final rateLimitTime = _rateLimitedKeys[apiKey];
    if (rateLimitTime == null) return false;

    final now = DateTime.now();
    return now.difference(rateLimitTime) < _rateLimitCooldown;
  }

  /// Clean up expired rate-limited keys
  static void _cleanupRateLimitedKeys() {
    final now = DateTime.now();
    final expiredKeys = <String>[];

    for (final entry in _rateLimitedKeys.entries) {
      if (now.difference(entry.value) >= _rateLimitCooldown) {
        expiredKeys.add(entry.key);
      }
    }

    for (final key in expiredKeys) {
      _rateLimitedKeys.remove(key);

      if (kDebugMode) {
        print(
            'ApiKeyRotationService: Removed expired rate limit for key: ${_maskApiKey(key)}');
      }
    }

    if (expiredKeys.isNotEmpty) {
      _saveRateLimitedKeys();
    }
  }

  /// Mask API key for logging (show only first 8 and last 4 characters)
  static String _maskApiKey(String apiKey) {
    if (apiKey.length <= 12) return '***';
    return '${apiKey.substring(0, 8)}...${apiKey.substring(apiKey.length - 4)}';
  }

  /// Load cached data from SharedPreferences
  static Future<void> _loadCachedData() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Load API keys
      final cachedKeys = prefs.getStringList(_keysCacheKey);
      if (cachedKeys != null && cachedKeys.isNotEmpty) {
        _apiKeys = cachedKeys;
      }

      // Load current key index
      _currentKeyIndex = prefs.getInt(_currentKeyIndexKey) ?? 0;

      // Load last fetch time
      final lastFetchTimeString = prefs.getString(_lastFetchTimeKey);
      if (lastFetchTimeString != null) {
        _lastFetchTime = DateTime.tryParse(lastFetchTimeString);
      }

      // Load rate-limited keys
      final rateLimitedKeysJson = prefs.getString(_rateLimitedKeysKey);
      if (rateLimitedKeysJson != null) {
        try {
          final Map<String, dynamic> decoded = jsonDecode(rateLimitedKeysJson);
          _rateLimitedKeys = decoded.map(
            (key, value) => MapEntry(key, DateTime.parse(value)),
          );
        } catch (e) {
          if (kDebugMode) {
            print('ApiKeyRotationService: Error loading rate-limited keys: $e');
          }
          _rateLimitedKeys = {};
        }
      }

      if (kDebugMode) {
        print(
            'ApiKeyRotationService: Loaded cached data - ${_apiKeys.length} keys, index $_currentKeyIndex');
      }
    } catch (error) {
      if (kDebugMode) {
        print('ApiKeyRotationService: Error loading cached data: $error');
      }
    }
  }

  /// Save cached data to SharedPreferences
  static Future<void> _saveCachedData() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Save API keys
      await prefs.setStringList(_keysCacheKey, _apiKeys);

      // Save current key index
      await prefs.setInt(_currentKeyIndexKey, _currentKeyIndex);

      // Save last fetch time
      if (_lastFetchTime != null) {
        await prefs.setString(
            _lastFetchTimeKey, _lastFetchTime!.toIso8601String());
      }

      if (kDebugMode) {
        print('ApiKeyRotationService: Saved cached data');
      }
    } catch (error) {
      if (kDebugMode) {
        print('ApiKeyRotationService: Error saving cached data: $error');
      }
    }
  }

  /// Save current key index to SharedPreferences
  static Future<void> _saveCurrentKeyIndex() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_currentKeyIndexKey, _currentKeyIndex);
    } catch (error) {
      if (kDebugMode) {
        print('ApiKeyRotationService: Error saving current key index: $error');
      }
    }
  }

  /// Save rate-limited keys to SharedPreferences
  static Future<void> _saveRateLimitedKeys() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final rateLimitedKeysJson = jsonEncode(
        _rateLimitedKeys.map(
          (key, value) => MapEntry(key, value.toIso8601String()),
        ),
      );
      await prefs.setString(_rateLimitedKeysKey, rateLimitedKeysJson);
    } catch (error) {
      if (kDebugMode) {
        print('ApiKeyRotationService: Error saving rate-limited keys: $error');
      }
    }
  }
}
