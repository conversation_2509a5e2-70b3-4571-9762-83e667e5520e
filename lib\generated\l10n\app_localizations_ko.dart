/// Generated file. Do not edit.
///
/// This file contains the localized strings for the TripWiseGo app.
/// To add a new localized string, add it to the appropriate .arb file
/// in the lib/l10n directory and run `flutter gen-l10n`.


import 'app_localizations.dart';

/// The translations for Korean (`ko`).
class AppLocalizationsKo extends AppLocalizations {
  AppLocalizationsKo([String locale = 'ko']) : super(locale);

  @override
  String get appTitle => 'TripWiseGo';

  @override
  String get welcome => '환영합니다';

  @override
  String get guestUser => '게스트 사용자';

  @override
  String get readyForAdventure => '다음 모험을 위한 준비 완료';

  @override
  String get exploringAsGuest => '게스트로 세계 탐험';

  @override
  String get editProfile => '프로필 편집';

  @override
  String get saveChanges => '변경사항 저장';

  @override
  String get cancel => '취소';

  @override
  String get username => '사용자명';

  @override
  String get email => '이메일';

  @override
  String get profileUpdatedSuccessfully => '프로필이 성공적으로 업데이트되었습니다!';

  @override
  String failedToUpdateProfile(String error) {
    return '프로필 업데이트 실패: $error';
  }

  @override
  String get profilePictureUpdatedSuccessfully => '프로필 사진이 성공적으로 업데이트되었습니다!';

  @override
  String failedToUploadImage(String error) {
    return '이미지 업로드 실패: $error';
  }

  @override
  String get profileEditingNotAvailableForGuests => '게스트 사용자는 프로필을 편집할 수 없습니다';

  @override
  String get profilePictureEditingNotAvailableForGuests => '게스트 사용자는 프로필 사진을 편집할 수 없습니다';

  @override
  String get usernameCannotBeEmpty => '사용자명은 비워둘 수 없습니다';

  @override
  String get usernameMustBeBetween2And30Characters => '사용자명은 2자에서 30자 사이여야 합니다';

  @override
  String get plan => '계획';

  @override
  String get termsOfService => '서비스 약관';

  @override
  String get language => '언어';

  @override
  String get privacyPolicy => '개인정보 보호정책';

  @override
  String get support => '지원';

  @override
  String get helpCenter => '도움말 센터';

  @override
  String get contactUs => '문의하기';

  @override
  String get helpSupport => 'Help & Support';

  @override
  String get howCanWeHelpYou => 'How can we help you?';

  @override
  String get helpSupportGreeting => 'Hi! I\'m here to help you with TripwiseGO. You can ask me about app features, troubleshooting, or report any issues you\'re experiencing.';

  @override
  String get helpSupportWelcome => 'Welcome to TripwiseGO Support! Here are some things I can help you with:';

  @override
  String get helpSupportFeatures => '• App features and how to use them\n• Navigation and account help\n• Troubleshooting common issues\n• Reporting bugs or problems';

  @override
  String get helpSupportAskQuestion => 'Feel free to ask me anything or describe any issues you\'re having!';

  @override
  String get reportIssue => 'Report Issue';

  @override
  String get reportBug => 'Report Bug';

  @override
  String get reportProblem => 'Report Problem';

  @override
  String get issueCategory => 'Issue Category';

  @override
  String get bugReport => 'Bug Report';

  @override
  String get featureRequest => 'Feature Request';

  @override
  String get generalFeedback => 'General Feedback';

  @override
  String get accountIssue => 'Account Issue';

  @override
  String get technicalProblem => 'Technical Problem';

  @override
  String get yourName => 'Your Name';

  @override
  String get yourEmail => 'Your Email';

  @override
  String get issueDescription => 'Issue Description';

  @override
  String get describeIssueDetail => 'Please describe the issue in detail';

  @override
  String get optionalScreenshot => 'Screenshot (Optional)';

  @override
  String get submitReport => 'Submit Report';

  @override
  String get reportSubmitted => 'Report Submitted';

  @override
  String get reportSubmittedSuccess => 'Thank you! Your report has been submitted successfully. We\'ll look into it and get back to you if needed.';

  @override
  String get reportSubmissionFailed => 'Failed to submit report. Please try again later.';

  @override
  String get nameRequired => 'Name is required';

  @override
  String get emailRequired => 'Email is required';

  @override
  String get validEmailRequired => 'Please enter a valid email address';

  @override
  String get descriptionRequired => 'Issue description is required';

  @override
  String get selectCategory => 'Please select a category';

  @override
  String get subscription => 'Subscription';

  @override
  String get subscriptionActive => 'Subscription Active!';

  @override
  String get welcomeToPremium => 'Welcome to TripwiseGO Premium! Enjoy unlimited access to all features.';

  @override
  String get currentPlan => 'Current Plan';

  @override
  String get trial => 'Trial';

  @override
  String get active => 'Active';

  @override
  String trialEndsIn(int days) {
    return 'Trial ends in $days days';
  }

  @override
  String renewsIn(int days) {
    return 'Renews in $days days';
  }

  @override
  String get yourBenefits => 'Your Benefits';

  @override
  String get startPlanningTrip => 'Start Planning Your Trip';

  @override
  String get manageSubscription => 'Manage Subscription';

  @override
  String get basic => 'Basic';

  @override
  String get premium => 'Premium';

  @override
  String get basicPlaceRecommendations => 'Basic Place Recommendations';

  @override
  String get locationRecommendationSwiping => 'Location Recommendation Swiping (20 swipes/day)';

  @override
  String get aiPoweredTravelPlanner => 'AI-Powered Travel Planner';

  @override
  String get unlimitedQuizzes => 'Unlimited Quizzes & Fun Facts';

  @override
  String get noAds => 'No-Ads';

  @override
  String get oneMonth => '1 Month';

  @override
  String get threeMonths => '3 Months';

  @override
  String get fiveMonths => '5 Months';

  @override
  String dayFreeTrial(int days) {
    return '$days-day free trial';
  }

  @override
  String get billedMonthly => 'Billed monthly';

  @override
  String get billedTwiceAnnually => 'Billed twice annually';

  @override
  String get billedAnnually => 'Billed annually';

  @override
  String get save45Percent => 'SAVE 45%';

  @override
  String get continueButton => 'Continue';

  @override
  String get termsAndConditions => 'Terms and Conditions';

  @override
  String get failedToSubscribe => 'Failed to subscribe. Please try again.';

  @override
  String get signOut => '로그아웃';

  @override
  String get selectLanguage => '언어 선택';

  @override
  String get chooseYourPreferredLanguage => '선호하는 언어를 선택하세요';

  @override
  String get languageUpdatedSuccessfully => '언어가 성공적으로 업데이트되었습니다!';

  @override
  String get home => '홈';

  @override
  String get match => '매치';

  @override
  String get chat => '채팅';

  @override
  String get profile => '프로필';

  @override
  String get loading => '로딩 중...';

  @override
  String get error => '오류';

  @override
  String get retry => '다시 시도';

  @override
  String get ok => '확인';

  @override
  String get yes => '예';

  @override
  String get no => '아니오';

  @override
  String get save => '저장';

  @override
  String get delete => '삭제';

  @override
  String get edit => '편집';

  @override
  String get add => '추가';

  @override
  String get remove => '제거';

  @override
  String get close => '닫기';

  @override
  String get back => '뒤로';

  @override
  String get next => '다음';

  @override
  String get previous => '이전';

  @override
  String get done => '완료';

  @override
  String get search => '검색';

  @override
  String get noResultsFound => '결과를 찾을 수 없습니다';

  @override
  String get tryAgain => '다시 시도';

  @override
  String get somethingWentWrong => '문제가 발생했습니다';

  @override
  String get networkError => '네트워크 오류. 연결을 확인하세요.';

  @override
  String get serverError => '서버 오류. 나중에 다시 시도하세요.';

  @override
  String get invalidInput => '잘못된 입력';

  @override
  String get required => '필수';

  @override
  String get optional => '선택사항';

  @override
  String get itinerary => 'Itinerary';

  @override
  String get yourItineraries => 'Your Itineraries';

  @override
  String get startPlanningYourTrip => 'Start Planning Your Trip';

  @override
  String get tripPlanningFeaturesWillAppearHere => 'Your trip planning features will appear here. The persistent authentication is now working!';

  @override
  String get forYou => 'For You';

  @override
  String get liked => 'Liked';

  @override
  String get collab => 'Collab';

  @override
  String get collaborativeItinerary => 'Collaborative';

  @override
  String get endSession => 'End Session';

  @override
  String get logout => 'Logout';

  @override
  String logoutFailed(String error) {
    return 'Logout failed: $error';
  }

  @override
  String get noItineraryFound => '여행 일정을 찾을 수 없습니다';

  @override
  String get askAiToCreateTravelPlan => 'AI에게 여행 계획을 만들어 달라고 요청하세요!';

  @override
  String get saturday => '토요일';

  @override
  String get tuesday => '화요일';

  @override
  String dayNumber(int number) {
    return '$number일차';
  }

  @override
  String get itineraryOverview => '여행 일정 개요';

  @override
  String daysAndNights(int days, int nights) {
    return '$days일 $nights박';
  }

  @override
  String get hiImWanderlyAi => '안녕하세요, 저는 Wanderly AI 🌏입니다';

  @override
  String get yourTravelAiAssistant => '당신의 여행 AI 어시스턴트입니다. 오늘 어떻게 도와드릴까요?';

  @override
  String get useThisBubbleChat => '이 버블 채팅을 사용하세요';

  @override
  String get aiAssistant => 'AI 어시스턴트';

  @override
  String get chatHistory => '채팅 기록';

  @override
  String get newChat => '새 채팅';

  @override
  String get addImage => '이미지 추가';

  @override
  String get camera => '카메라';

  @override
  String get gallery => '갤러리';

  @override
  String get microphonePermissionRequired => '음성 입력을 위해 마이크 권한이 필요합니다';

  @override
  String get speechRecognitionNotAvailable => '이 기기에서는 음성 인식을 사용할 수 없습니다';

  @override
  String get listening => '듣고 있습니다...';

  @override
  String get deleteChat => '채팅 삭제';

  @override
  String get deleteChatConfirmation => '이 채팅을 삭제하시겠습니까? 이 작업은 되돌릴 수 없습니다.';

  @override
  String get chatDeletedSuccessfully => '채팅이 성공적으로 삭제되었습니다';

  @override
  String get pleaseEnterSearchQuery => '검색어를 입력해주세요';

  @override
  String get dailySearchLimitReached => '일일 검색 한도에 도달했습니다. 하루에 5번 검색할 수 있습니다.';

  @override
  String get searchingTheWeb => '웹 검색 중...';

  @override
  String get webSearchModeActive => '웹 검색 모드 활성화';

  @override
  String get pleaseWaitWhileSearching => '정보를 검색하는 동안 잠시 기다려주세요';

  @override
  String get yourNextMessageWillSearch => '다음 메시지로 웹을 검색합니다';

  @override
  String get disableWebSearch => '웹 검색 비활성화';

  @override
  String get enableWebSearch => '웹 검색 활성화';

  @override
  String get switchBackToAiChatMode => 'AI 채팅 모드로 돌아가기';

  @override
  String get searchWebForCurrentInfo => '최신 정보를 웹에서 검색';

  @override
  String get pickImageFromGallery => '갤러리에서 이미지 선택';

  @override
  String get uploadImageForAiAnalysis => 'AI 분석을 위해 이미지 업로드';

  @override
  String get yourMessage => '당신의 메시지';

  @override
  String get wanderlyAi => 'Wanderly AI';

  @override
  String get webSearch => '웹 검색:';

  @override
  String get like => '좋아요';

  @override
  String get dislike => '싫어요';

  @override
  String get copy => '복사';

  @override
  String get regenerate => '재생성';

  @override
  String get failedToSubmitFeedback => '피드백 제출에 실패했습니다. 다시 시도해주세요.';

  @override
  String get thankYouForFeedback => '피드백 감사합니다! 🙏';

  @override
  String get feedbackReceivedThanks => '피드백을 받았습니다. 개선에 도움을 주셔서 감사합니다! 🚀';

  @override
  String get responseCopiedToClipboard => '응답이 클립보드에 복사되었습니다';

  @override
  String get wanderlyAiIsTyping => 'Wanderly AI가 입력 중입니다';

  @override
  String get stopGeneration => '생성 중지';

  @override
  String get youHaveChatsLeft => '10개의 채팅이 남았습니다';

  @override
  String get enterSearchQuery => '검색어를 입력하세요...';

  @override
  String get askMeAnythingOrLongPress => '무엇이든 물어보시거나 길게 눌러서 말하세요...';

  @override
  String failedToPickImage(String error) {
    return '이미지 선택 실패: $error';
  }

  @override
  String get failedToAnalyzeImage => '이미지 분석에 실패했습니다. 다시 시도해주세요.';

  @override
  String get responseGenerationStopped => '응답 생성이 중지되었습니다.';

  @override
  String get unknownDestination => '알 수 없는 목적지';

  @override
  String get congratulations => 'Congratulations';

  @override
  String get itsAMatch => 'It\'s a match';

  @override
  String get travelVibesAligned => 'Travel vibes aligned pack\nyour bags, you\'ve got a match!';

  @override
  String get matchedPreferences => 'Matched Preferences:';

  @override
  String get addToItinerary => 'Add to Itinerary';

  @override
  String get keepSwiping => 'Keep swiping';

  @override
  String get versionInfo => 'Version Info';

  @override
  String get appVersion => 'App Version';

  @override
  String get buildNumber => 'Build Number';

  @override
  String get packageName => 'Package Name';

  @override
  String get appName => 'App Name';

  @override
  String get buildSignature => 'Build Signature';

  @override
  String get installerStore => 'Installer Store';

  @override
  String get deviceInfo => 'Device Information';

  @override
  String get operatingSystem => 'Operating System';

  @override
  String get flutterVersion => 'Flutter Version';

  @override
  String get dartVersion => 'Dart Version';

  @override
  String get leaveAReview => 'Leave a Review';

  @override
  String get rateOurApp => 'Rate Our App';

  @override
  String get enjoyingTripWiseGo => 'Enjoying TripWiseGo?';

  @override
  String get helpUsImproveByLeavingReview => 'Help us improve by leaving a review on the app store. Your feedback means a lot to us!';

  @override
  String get rateNow => 'Rate Now';

  @override
  String get maybeLater => 'Maybe Later';

  @override
  String get reviewFeatureNotAvailable => 'Review feature is only available in production builds';

  @override
  String get unableToOpenStore => 'Unable to open app store. Please try again later.';

  @override
  String get thankYouForReview => 'Thank you for taking the time to review our app!';
}
