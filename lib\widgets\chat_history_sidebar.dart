import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../models/chat_history_models.dart';
import '../services/chat_history_service.dart';

class ChatHistorySidebar extends StatefulWidget {
  final bool isVisible;
  final VoidCallback onClose;
  final Function(ChatSession) onChatSelected;
  final VoidCallback onNewChat;

  const ChatHistorySidebar({
    super.key,
    required this.isVisible,
    required this.onClose,
    required this.onChatSelected,
    required this.onNewChat,
  });

  @override
  State<ChatHistorySidebar> createState() => _ChatHistorySidebarState();
}

class _ChatHistorySidebarState extends State<ChatHistorySidebar>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;

  List<ChatSession> _chatSessions = [];
  bool _isLoading = true;
  String? _activeChatId;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(-1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 0.5,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _loadChatHistory();
  }

  @override
  void didUpdateWidget(ChatHistorySidebar oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.isVisible != oldWidget.isVisible) {
      if (widget.isVisible) {
        _animationController.forward();
        _loadChatHistory(); // Refresh when opening
        _refreshActiveChat(); // Refresh active chat indicator
      } else {
        _animationController.reverse();
      }
    }
  }

  Future<void> _refreshActiveChat() async {
    try {
      final activeSession = await ChatHistoryService.getActiveSession();
      setState(() {
        _activeChatId = activeSession?.id;
      });
    } catch (e) {
      // Handle error silently
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadChatHistory() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final sessions = await ChatHistoryService.getAllSessions();
      final activeSession = await ChatHistoryService.getActiveSession();

      setState(() {
        _chatSessions = sessions;
        _activeChatId = activeSession?.id;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _deleteChat(ChatSession session) async {
    try {
      await ChatHistoryService.deleteSession(session.id);
      await _loadChatHistory();

      // If this was the active chat, trigger new chat
      if (session.id == _activeChatId) {
        widget.onNewChat();
      }
    } catch (e) {
      // Handle error
    }
  }

  Future<void> _renameChat(ChatSession session) async {
    final TextEditingController controller =
        TextEditingController(text: session.title);

    final result = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Rename Chat',
          style: GoogleFonts.instrumentSans(fontWeight: FontWeight.w600),
        ),
        content: TextField(
          controller: controller,
          autofocus: true,
          maxLength: 50,
          decoration: InputDecoration(
            hintText: 'Enter new chat title',
            hintStyle: GoogleFonts.instrumentSans(color: Colors.grey[500]),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFFE2E8F0)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF0D76FF)),
            ),
          ),
          style: GoogleFonts.instrumentSans(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Cancel',
              style: GoogleFonts.instrumentSans(color: Colors.grey[600]),
            ),
          ),
          TextButton(
            onPressed: () {
              final newTitle = controller.text.trim();
              Navigator.of(context).pop(newTitle.isNotEmpty ? newTitle : null);
            },
            child: Text(
              'Rename',
              style: GoogleFonts.instrumentSans(
                color: const Color(0xFF0D76FF),
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );

    if (result != null) {
      try {
        await ChatHistoryService.renameSession(session.id, result);
        await _loadChatHistory();
      } catch (e) {
        // Handle error
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Failed to rename chat',
                style: GoogleFonts.instrumentSans(),
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isVisible) {
      return const SizedBox.shrink();
    }

    return Stack(
      children: [
        // Background overlay
        AnimatedBuilder(
          animation: _fadeAnimation,
          builder: (context, child) {
            return GestureDetector(
              onTap: widget.onClose,
              child: Container(
                color: Colors.black.withOpacity(_fadeAnimation.value),
                width: double.infinity,
                height: double.infinity,
              ),
            );
          },
        ),

        // Sidebar
        AnimatedBuilder(
          animation: _slideAnimation,
          builder: (context, child) {
            return SlideTransition(
              position: _slideAnimation,
              child: Container(
                width: MediaQuery.of(context).size.width * 0.8,
                height: double.infinity,
                decoration: const BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black26,
                      blurRadius: 10,
                      offset: Offset(2, 0),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    _buildHeader(),
                    Expanded(
                      child: _isLoading
                          ? _buildLoadingState()
                          : _chatSessions.isEmpty
                              ? _buildEmptyState()
                              : _buildChatList(),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.fromLTRB(20, 60, 20, 20),
      decoration: const BoxDecoration(
        color: Color(0xFF0D76FF),
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Chat History',
                style: GoogleFonts.instrumentSans(
                  fontSize: 24,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
              IconButton(
                onPressed: widget.onClose,
                icon: const Icon(
                  Icons.close,
                  color: Colors.white,
                  size: 24,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () {
                widget.onClose();
                widget.onNewChat();
              },
              icon: const Icon(Icons.add, size: 18),
              label: Text(
                'New Chat',
                style: GoogleFonts.instrumentSans(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFF7F9FC),
                foregroundColor: const Color(0xFF0D76FF),
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF0D76FF)),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.chat_bubble_outline,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No chat history yet',
              style: GoogleFonts.instrumentSans(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Start a conversation to see your chat history here',
              style: GoogleFonts.instrumentSans(
                fontSize: 14,
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChatList() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: 8),
      itemCount: _chatSessions.length,
      itemBuilder: (context, index) {
        final session = _chatSessions[index];
        final isActive = session.id == _activeChatId;

        return _buildChatItem(session, isActive);
      },
    );
  }

  Widget _buildChatItem(ChatSession session, bool isActive) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        color: isActive ? const Color(0xFF0D76FF).withOpacity(0.1) : null,
        borderRadius: BorderRadius.circular(12),
        border: isActive
            ? Border.all(color: const Color(0xFF0D76FF), width: 1)
            : null,
      ),
      child: ListTile(
        onTap: () {
          widget.onClose();
          widget.onChatSelected(session);
        },
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        title: Text(
          session.title,
          style: GoogleFonts.instrumentSans(
            fontSize: 16,
            fontWeight: isActive ? FontWeight.w600 : FontWeight.w500,
            color: isActive ? const Color(0xFF0D76FF) : const Color(0xFF2D3748),
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Padding(
          padding: const EdgeInsets.only(top: 4),
          child: Text(
            session.formattedDate,
            style: GoogleFonts.instrumentSans(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            if (value == 'rename') {
              _renameChat(session);
            } else if (value == 'delete') {
              _showDeleteConfirmation(session);
            }
          },
          itemBuilder: (context) => [
            PopupMenuItem<String>(
              value: 'rename',
              child: Row(
                children: [
                  const Icon(Icons.edit, size: 18, color: Color(0xFF0D76FF)),
                  const SizedBox(width: 8),
                  Text(
                    'Rename',
                    style: GoogleFonts.instrumentSans(
                        color: const Color(0xFF0D76FF)),
                  ),
                ],
              ),
            ),
            PopupMenuItem<String>(
              value: 'delete',
              child: Row(
                children: [
                  const Icon(Icons.delete, size: 18, color: Colors.red),
                  const SizedBox(width: 8),
                  Text(
                    'Delete',
                    style: GoogleFonts.instrumentSans(color: Colors.red),
                  ),
                ],
              ),
            ),
          ],
          child: Icon(
            Icons.more_vert,
            color: Colors.grey[600],
            size: 20,
          ),
        ),
      ),
    );
  }

  void _showDeleteConfirmation(ChatSession session) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Delete Chat',
          style: GoogleFonts.instrumentSans(fontWeight: FontWeight.w600),
        ),
        content: Text(
          'Are you sure you want to delete this chat? This action cannot be undone.',
          style: GoogleFonts.instrumentSans(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Cancel',
              style: GoogleFonts.instrumentSans(color: Colors.grey[600]),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _deleteChat(session);
            },
            child: Text(
              'Delete',
              style: GoogleFonts.instrumentSans(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }
}
