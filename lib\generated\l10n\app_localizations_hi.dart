/// Generated file. Do not edit.
///
/// This file contains the localized strings for the TripWiseGo app.
/// To add a new localized string, add it to the appropriate .arb file
/// in the lib/l10n directory and run `flutter gen-l10n`.


import 'app_localizations.dart';

/// The translations for Hindi (`hi`).
class AppLocalizationsHi extends AppLocalizations {
  AppLocalizationsHi([String locale = 'hi']) : super(locale);

  @override
  String get appTitle => 'TripWiseGo';

  @override
  String get welcome => 'स्वागत है';

  @override
  String get guestUser => 'अतिथि उपयोगकर्ता';

  @override
  String get readyForAdventure => 'आपके अगले रोमांच के लिए तैयार';

  @override
  String get exploringAsGuest => 'अतिथि के रूप में दुनिया की खोज';

  @override
  String get editProfile => 'प्रोफ़ाइल संपादित करें';

  @override
  String get saveChanges => 'परिवर्तन सहेजें';

  @override
  String get cancel => 'रद्द करें';

  @override
  String get username => 'उपयोगकर्ता नाम';

  @override
  String get email => 'ईमेल';

  @override
  String get profileUpdatedSuccessfully => 'प्रोफ़ाइल सफलतापूर्वक अपडेट हुई!';

  @override
  String failedToUpdateProfile(String error) {
    return 'प्रोफ़ाइल अपडेट करने में विफल: $error';
  }

  @override
  String get profilePictureUpdatedSuccessfully => 'प्रोफ़ाइल चित्र सफलतापूर्वक अपडेट हुआ!';

  @override
  String failedToUploadImage(String error) {
    return 'छवि अपलोड करने में विफल: $error';
  }

  @override
  String get profileEditingNotAvailableForGuests => 'अतिथि उपयोगकर्ताओं के लिए प्रोफ़ाइल संपादन उपलब्ध नहीं है';

  @override
  String get profilePictureEditingNotAvailableForGuests => 'अतिथि उपयोगकर्ताओं के लिए प्रोफ़ाइल चित्र संपादन उपलब्ध नहीं है';

  @override
  String get usernameCannotBeEmpty => 'उपयोगकर्ता नाम खाली नहीं हो सकता';

  @override
  String get usernameMustBeBetween2And30Characters => 'उपयोगकर्ता नाम 2 से 30 अक्षरों के बीच होना चाहिए';

  @override
  String get plan => 'योजना';

  @override
  String get termsOfService => 'सेवा की शर्तें';

  @override
  String get language => 'भाषा';

  @override
  String get privacyPolicy => 'गोपनीयता नीति';

  @override
  String get support => 'सहायता';

  @override
  String get helpCenter => 'सहायता केंद्र';

  @override
  String get contactUs => 'हमसे संपर्क करें';

  @override
  String get helpSupport => 'Help & Support';

  @override
  String get howCanWeHelpYou => 'How can we help you?';

  @override
  String get helpSupportGreeting => 'Hi! I\'m here to help you with TripwiseGO. You can ask me about app features, troubleshooting, or report any issues you\'re experiencing.';

  @override
  String get helpSupportWelcome => 'Welcome to TripwiseGO Support! Here are some things I can help you with:';

  @override
  String get helpSupportFeatures => '• App features and how to use them\n• Navigation and account help\n• Troubleshooting common issues\n• Reporting bugs or problems';

  @override
  String get helpSupportAskQuestion => 'Feel free to ask me anything or describe any issues you\'re having!';

  @override
  String get reportIssue => 'Report Issue';

  @override
  String get reportBug => 'Report Bug';

  @override
  String get reportProblem => 'Report Problem';

  @override
  String get issueCategory => 'Issue Category';

  @override
  String get bugReport => 'Bug Report';

  @override
  String get featureRequest => 'Feature Request';

  @override
  String get generalFeedback => 'General Feedback';

  @override
  String get accountIssue => 'Account Issue';

  @override
  String get technicalProblem => 'Technical Problem';

  @override
  String get yourName => 'Your Name';

  @override
  String get yourEmail => 'Your Email';

  @override
  String get issueDescription => 'Issue Description';

  @override
  String get describeIssueDetail => 'Please describe the issue in detail';

  @override
  String get optionalScreenshot => 'Screenshot (Optional)';

  @override
  String get submitReport => 'Submit Report';

  @override
  String get reportSubmitted => 'Report Submitted';

  @override
  String get reportSubmittedSuccess => 'Thank you! Your report has been submitted successfully. We\'ll look into it and get back to you if needed.';

  @override
  String get reportSubmissionFailed => 'Failed to submit report. Please try again later.';

  @override
  String get nameRequired => 'Name is required';

  @override
  String get emailRequired => 'Email is required';

  @override
  String get validEmailRequired => 'Please enter a valid email address';

  @override
  String get descriptionRequired => 'Issue description is required';

  @override
  String get selectCategory => 'Please select a category';

  @override
  String get subscription => 'Subscription';

  @override
  String get subscriptionActive => 'Subscription Active!';

  @override
  String get welcomeToPremium => 'Welcome to TripwiseGO Premium! Enjoy unlimited access to all features.';

  @override
  String get currentPlan => 'Current Plan';

  @override
  String get trial => 'Trial';

  @override
  String get active => 'Active';

  @override
  String trialEndsIn(int days) {
    return 'Trial ends in $days days';
  }

  @override
  String renewsIn(int days) {
    return 'Renews in $days days';
  }

  @override
  String get yourBenefits => 'Your Benefits';

  @override
  String get startPlanningTrip => 'Start Planning Your Trip';

  @override
  String get manageSubscription => 'Manage Subscription';

  @override
  String get basic => 'Basic';

  @override
  String get premium => 'Premium';

  @override
  String get basicPlaceRecommendations => 'Basic Place Recommendations';

  @override
  String get locationRecommendationSwiping => 'Location Recommendation Swiping (20 swipes/day)';

  @override
  String get aiPoweredTravelPlanner => 'AI-Powered Travel Planner';

  @override
  String get unlimitedQuizzes => 'Unlimited Quizzes & Fun Facts';

  @override
  String get noAds => 'No-Ads';

  @override
  String get oneMonth => '1 Month';

  @override
  String get threeMonths => '3 Months';

  @override
  String get fiveMonths => '5 Months';

  @override
  String dayFreeTrial(int days) {
    return '$days-day free trial';
  }

  @override
  String get billedMonthly => 'Billed monthly';

  @override
  String get billedTwiceAnnually => 'Billed twice annually';

  @override
  String get billedAnnually => 'Billed annually';

  @override
  String get save45Percent => 'SAVE 45%';

  @override
  String get continueButton => 'Continue';

  @override
  String get termsAndConditions => 'Terms and Conditions';

  @override
  String get failedToSubscribe => 'Failed to subscribe. Please try again.';

  @override
  String get signOut => 'साइन आउट';

  @override
  String get selectLanguage => 'भाषा चुनें';

  @override
  String get chooseYourPreferredLanguage => 'अपनी पसंदीदा भाषा चुनें';

  @override
  String get languageUpdatedSuccessfully => 'भाषा सफलतापूर्वक अपडेट हुई!';

  @override
  String get home => 'होम';

  @override
  String get match => 'मैच';

  @override
  String get chat => 'चैट';

  @override
  String get profile => 'प्रोफ़ाइल';

  @override
  String get loading => 'लोड हो रहा है...';

  @override
  String get error => 'त्रुटि';

  @override
  String get retry => 'पुनः प्रयास करें';

  @override
  String get ok => 'ठीक है';

  @override
  String get yes => 'हाँ';

  @override
  String get no => 'नहीं';

  @override
  String get save => 'सहेजें';

  @override
  String get delete => 'हटाएं';

  @override
  String get edit => 'संपादित करें';

  @override
  String get add => 'जोड़ें';

  @override
  String get remove => 'हटाएं';

  @override
  String get close => 'बंद करें';

  @override
  String get back => 'वापस';

  @override
  String get next => 'अगला';

  @override
  String get previous => 'पिछला';

  @override
  String get done => 'हो गया';

  @override
  String get search => 'खोजें';

  @override
  String get noResultsFound => 'कोई परिणाम नहीं मिला';

  @override
  String get tryAgain => 'फिर से कोशिश करें';

  @override
  String get somethingWentWrong => 'कुछ गलत हुआ';

  @override
  String get networkError => 'नेटवर्क त्रुटि। कृपया अपना कनेक्शन जांचें।';

  @override
  String get serverError => 'सर्वर त्रुटि। कृपया बाद में पुनः प्रयास करें।';

  @override
  String get invalidInput => 'अमान्य इनपुट';

  @override
  String get required => 'आवश्यक';

  @override
  String get optional => 'वैकल्पिक';

  @override
  String get itinerary => 'Itinerary';

  @override
  String get yourItineraries => 'Your Itineraries';

  @override
  String get startPlanningYourTrip => 'Start Planning Your Trip';

  @override
  String get tripPlanningFeaturesWillAppearHere => 'Your trip planning features will appear here. The persistent authentication is now working!';

  @override
  String get forYou => 'For You';

  @override
  String get liked => 'Liked';

  @override
  String get collab => 'Collab';

  @override
  String get collaborativeItinerary => 'Collaborative';

  @override
  String get endSession => 'End Session';

  @override
  String get logout => 'Logout';

  @override
  String logoutFailed(String error) {
    return 'Logout failed: $error';
  }

  @override
  String get noItineraryFound => 'कोई यात्रा कार्यक्रम नहीं मिला';

  @override
  String get askAiToCreateTravelPlan => 'हमारे AI से आपके लिए यात्रा योजना बनाने को कहें!';

  @override
  String get saturday => 'शनिवार';

  @override
  String get tuesday => 'मंगलवार';

  @override
  String dayNumber(int number) {
    return 'दिन $number';
  }

  @override
  String get itineraryOverview => 'यात्रा कार्यक्रम का अवलोकन';

  @override
  String daysAndNights(int days, int nights) {
    return '$daysदि $nightsरा';
  }

  @override
  String get hiImWanderlyAi => 'नमस्ते, मैं Wanderly AI हूँ 🌏';

  @override
  String get yourTravelAiAssistant => 'आपका यात्रा AI सहायक, आज मैं आपकी कैसे मदद कर सकता हूँ?';

  @override
  String get useThisBubbleChat => 'इस बबल चैट का उपयोग करें';

  @override
  String get aiAssistant => 'AI सहायक';

  @override
  String get chatHistory => 'चैट इतिहास';

  @override
  String get newChat => 'नई चैट';

  @override
  String get addImage => 'छवि जोड़ें';

  @override
  String get camera => 'कैमरा';

  @override
  String get gallery => 'गैलरी';

  @override
  String get microphonePermissionRequired => 'वॉयस इनपुट के लिए माइक्रोफोन की अनुमति आवश्यक है';

  @override
  String get speechRecognitionNotAvailable => 'इस डिवाइस पर स्पीच रिकग्निशन उपलब्ध नहीं है';

  @override
  String get listening => 'सुन रहा है...';

  @override
  String get deleteChat => 'चैट हटाएं';

  @override
  String get deleteChatConfirmation => 'क्या आप वाकई इस चैट को हटाना चाहते हैं? यह क्रिया पूर्ववत नहीं की जा सकती।';

  @override
  String get chatDeletedSuccessfully => 'चैट सफलतापूर्वक हटा दी गई';

  @override
  String get pleaseEnterSearchQuery => 'कृपया खोज क्वेरी दर्ज करें';

  @override
  String get dailySearchLimitReached => 'दैनिक खोज सीमा पहुंच गई। आप प्रति दिन 5 खोज कर सकते हैं।';

  @override
  String get searchingTheWeb => 'वेब खोजा जा रहा है...';

  @override
  String get webSearchModeActive => 'वेब खोज मोड सक्रिय';

  @override
  String get pleaseWaitWhileSearching => 'कृपया प्रतीक्षा करें जबकि मैं जानकारी खोज रहा हूँ';

  @override
  String get yourNextMessageWillSearch => 'आपका अगला संदेश वेब खोजेगा';

  @override
  String get disableWebSearch => 'वेब खोज अक्षम करें';

  @override
  String get enableWebSearch => 'वेब खोज सक्षम करें';

  @override
  String get switchBackToAiChatMode => 'AI चैट मोड पर वापस जाएं';

  @override
  String get searchWebForCurrentInfo => 'वर्तमान जानकारी के लिए वेब खोजें';

  @override
  String get pickImageFromGallery => 'गैलरी से छवि चुनें';

  @override
  String get uploadImageForAiAnalysis => 'AI विश्लेषण के लिए छवि अपलोड करें';

  @override
  String get yourMessage => 'आपका संदेश';

  @override
  String get wanderlyAi => 'Wanderly AI';

  @override
  String get webSearch => 'वेब खोज:';

  @override
  String get like => 'पसंद';

  @override
  String get dislike => 'नापसंद';

  @override
  String get copy => 'कॉपी';

  @override
  String get regenerate => 'पुनर्जनन';

  @override
  String get failedToSubmitFeedback => 'फीडबैक सबमिट करने में विफल। कृपया पुनः प्रयास करें।';

  @override
  String get thankYouForFeedback => 'आपके फीडबैक के लिए धन्यवाद! 🙏';

  @override
  String get feedbackReceivedThanks => 'फीडबैक प्राप्त हुआ। सुधार में मदद के लिए धन्यवाद! 🚀';

  @override
  String get responseCopiedToClipboard => 'प्रतिक्रिया क्लिपबोर्ड पर कॉपी की गई';

  @override
  String get wanderlyAiIsTyping => 'Wanderly AI टाइप कर रहा है';

  @override
  String get stopGeneration => 'जनरेशन रोकें';

  @override
  String get youHaveChatsLeft => 'आपके पास 10 चैट बचे हैं';

  @override
  String get enterSearchQuery => 'अपनी खोज क्वेरी दर्ज करें...';

  @override
  String get askMeAnythingOrLongPress => 'मुझसे कुछ भी पूछें या बोलने के लिए लंबे समय तक दबाएं...';

  @override
  String failedToPickImage(String error) {
    return 'छवि चुनने में विफल: $error';
  }

  @override
  String get failedToAnalyzeImage => 'छवि विश्लेषण में विफल। कृपया पुनः प्रयास करें।';

  @override
  String get responseGenerationStopped => 'प्रतिक्रिया जनरेशन रोक दी गई।';

  @override
  String get unknownDestination => 'अज्ञात गंतव्य';

  @override
  String get congratulations => 'Congratulations';

  @override
  String get itsAMatch => 'It\'s a match';

  @override
  String get travelVibesAligned => 'Travel vibes aligned pack\nyour bags, you\'ve got a match!';

  @override
  String get matchedPreferences => 'Matched Preferences:';

  @override
  String get addToItinerary => 'Add to Itinerary';

  @override
  String get keepSwiping => 'Keep swiping';

  @override
  String get versionInfo => 'Version Info';

  @override
  String get appVersion => 'App Version';

  @override
  String get buildNumber => 'Build Number';

  @override
  String get packageName => 'Package Name';

  @override
  String get appName => 'App Name';

  @override
  String get buildSignature => 'Build Signature';

  @override
  String get installerStore => 'Installer Store';

  @override
  String get deviceInfo => 'Device Information';

  @override
  String get operatingSystem => 'Operating System';

  @override
  String get flutterVersion => 'Flutter Version';

  @override
  String get dartVersion => 'Dart Version';

  @override
  String get leaveAReview => 'Leave a Review';

  @override
  String get rateOurApp => 'Rate Our App';

  @override
  String get enjoyingTripWiseGo => 'Enjoying TripWiseGo?';

  @override
  String get helpUsImproveByLeavingReview => 'Help us improve by leaving a review on the app store. Your feedback means a lot to us!';

  @override
  String get rateNow => 'Rate Now';

  @override
  String get maybeLater => 'Maybe Later';

  @override
  String get reviewFeatureNotAvailable => 'Review feature is only available in production builds';

  @override
  String get unableToOpenStore => 'Unable to open app store. Please try again later.';

  @override
  String get thankYouForReview => 'Thank you for taking the time to review our app!';
}
