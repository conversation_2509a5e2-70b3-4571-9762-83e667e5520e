import 'dart:async';
import 'package:flutter/foundation.dart';
import 'gemini_service.dart';

/// AI service that uses Gemini for both text and image analysis
class HybridAIService {
  static bool _isInitialized = false;

  /// Initialize Gemini service
  static Future<void> initialize({bool forceRefresh = false}) async {
    if (_isInitialized && !forceRefresh) {
      return;
    }

    try {
      if (kDebugMode) {
        print('HybridAIService: Initializing...');
      }

      // Initialize Gemini service for both text and vision
      await GeminiService.initialize(forceRefresh: forceRefresh);

      _isInitialized = true;

      if (kDebugMode) {
        print('HybridAIService: Initialization complete');
      }
    } catch (error) {
      if (kDebugMode) {
        print('HybridAIService: Initialization failed: $error');
      }
      rethrow;
    }
  }

  /// Send a text message using Gemini
  static Future<String> sendMessage(
      String message, List<Map<String, String>> conversationHistory,
      {String? responseLength}) async {
    return await GeminiService.sendMessage(message, conversationHistory,
        responseLength: responseLength);
  }

  /// Analyze an image using Gemini
  static Future<String> analyzeImage(String imagePath, String prompt) async {
    try {
      // Use Gemini for image analysis
      return await GeminiService.analyzeImage(imagePath, prompt);
    } catch (e) {
      if (kDebugMode) {
        print('HybridAIService: Gemini image analysis failed: $e');
      }

      // Return user-friendly error message
      return GeminiService.getErrorMessage(e);
    }
  }

  /// Get error message (delegates to appropriate service)
  static String getErrorMessage(dynamic error) {
    return GeminiService.getErrorMessage(error);
  }

  /// Model selection methods (delegates to Gemini)
  static Future<void> setSelectedModel(String model) async {
    return await GeminiService.setSelectedModel(model);
  }

  static String getSelectedModel() {
    return GeminiService.getSelectedModel();
  }

  static bool isPremiumModelSelected() {
    return GeminiService.isPremiumModelSelected();
  }

  /// Get available models (delegates to Gemini)
  static String get defaultModel => GeminiService.defaultModel;
  static String get premiumModel => GeminiService.premiumModel;

  /// Get last used models for debugging
  static String? getLastUsedTextModel() {
    return GeminiService.getLastUsedTextModel();
  }

  static String? getLastUsedVisionModel() {
    // Since we're using Gemini for vision, delegate to GeminiService
    return GeminiService.getLastUsedVisionModel();
  }

  /// Check if services are initialized
  static bool get isInitialized => _isInitialized;
}
