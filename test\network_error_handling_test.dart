import 'package:flutter_test/flutter_test.dart';
import 'package:tripwisego/services/network_aware_supabase.dart';

void main() {
  group('Network Error Handling Tests', () {
    late NetworkAwareSupabase networkWrapper;

    setUp(() {
      networkWrapper = NetworkAwareSupabase();
    });

    test('should identify network errors correctly', () {
      // Test various network error types
      final networkErrors = [
        'SocketException: Failed host lookup',
        'No address associated with hostname',
        'Connection refused',
        'Network error',
        'AuthRetryableFetchException',
        'ClientException with SocketException',
        'Timeout',
      ];

      for (final errorMessage in networkErrors) {
        final error = Exception(errorMessage);
        expect(
          networkWrapper.isNetworkError(error),
          true,
          reason: 'Should identify "$errorMessage" as a network error',
        );
      }
    });

    test('should not identify non-network errors as network errors', () {
      // Test non-network error types
      final nonNetworkErrors = [
        'Invalid credentials',
        'User not found',
        'Permission denied',
        'Invalid token',
        'Database error',
      ];

      for (final errorMessage in nonNetworkErrors) {
        final error = Exception(errorMessage);
        expect(
          networkWrapper.isNetworkError(error),
          false,
          reason: 'Should not identify "$errorMessage" as a network error',
        );
      }
    });

    test('should handle network operations gracefully', () async {
      // Test that network operations return fallback values on network errors
      final result = await networkWrapper.executeWithNetworkHandling<String>(
        () => throw Exception('SocketException: Failed host lookup'),
        operationName: 'test_operation',
        fallbackValue: 'fallback_value',
      );

      expect(result, equals('fallback_value'));
    });

    test('should rethrow non-network errors', () async {
      // Test that non-network errors are still thrown
      expect(
        () => networkWrapper.executeWithNetworkHandling<String>(
          () => throw Exception('Invalid credentials'),
          operationName: 'test_operation',
          fallbackValue: 'fallback_value',
        ),
        throwsException,
      );
    });

    test('should execute operations successfully when no errors occur', () async {
      // Test successful operation execution
      final result = await networkWrapper.executeWithNetworkHandling<String>(
        () => Future.value('success'),
        operationName: 'test_operation',
        fallbackValue: 'fallback_value',
      );

      expect(result, equals('success'));
    });

    test('should execute silent operations without throwing', () async {
      // Test silent execution that doesn't throw on any errors
      await expectLater(
        networkWrapper.executeSilently(
          () => throw Exception('Any error'),
          operationName: 'test_operation',
        ),
        completes,
      );
    });
  });
}
