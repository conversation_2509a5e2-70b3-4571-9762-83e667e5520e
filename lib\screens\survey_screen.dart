import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../models/survey_models.dart';
import '../services/survey_service.dart';
import '../services/auth_service.dart';
import 'survey_complete_screen.dart';

class SurveyScreen extends StatefulWidget {
  const SurveyScreen({super.key});

  @override
  State<SurveyScreen> createState() => _SurveyScreenState();
}

class _SurveyScreenState extends State<SurveyScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  int _currentPage = 0;
  final int _totalPages = 5;
  bool _isLoading = false;

  // Survey data based on the provided questions
  String _selectedHearAboutUs = '';
  List<String> _selectedFrustrations = [];
  String _selectedTravelCompanion = '';
  List<String> _selectedDestinationTypes = [];
  List<String> _selectedTravelPriorities = [];

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeOut),
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _fadeController, curve: Curves.easeOut));
    _fadeController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  void _nextPage() async {
    if (_currentPage < _totalPages - 1) {
      await _fadeController.reverse();
      setState(() {
        _currentPage++;
      });
      await _fadeController.forward();
    } else {
      _submitSurvey();
    }
  }

  void _previousPage() async {
    if (_currentPage > 0) {
      await _fadeController.reverse();
      setState(() {
        _currentPage--;
      });
      await _fadeController.forward();
    }
  }

  bool _canProceed() {
    switch (_currentPage) {
      case 0: // How did you hear about us
        return _selectedHearAboutUs.isNotEmpty;
      case 1: // Travel frustrations
        return _selectedFrustrations.isNotEmpty;
      case 2: // Travel companion
        return _selectedTravelCompanion.isNotEmpty;
      case 3: // Destination types
        return _selectedDestinationTypes.isNotEmpty;
      case 4: // Travel priorities
        return _selectedTravelPriorities.isNotEmpty;
      default:
        return false;
    }
  }

  Future<void> _submitSurvey() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final user = AuthService.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      final now = DateTime.now();
      final surveyResponse = SurveyResponse(
        userId: user.id,
        hearAboutUs: _selectedHearAboutUs, // Q1: How did you hear about us?
        travelCompanion: _selectedTravelCompanion, // Q3: Travel companion
        travelPriorities: _selectedTravelPriorities, // Q5: Travel priorities
        travelFrustrations: _selectedFrustrations, // Q2: Travel frustrations
        preferredDestinationTypes:
            _selectedDestinationTypes, // Q4: Destination types
        completedAt: now,
        createdAt: now,
        updatedAt: now,
      );

      final success = await SurveyService.saveSurveyResponse(surveyResponse);

      if (mounted) {
        if (success) {
          // Navigate to survey completion screen
          Navigator.of(context).pushReplacement(
            PageRouteBuilder(
              pageBuilder: (context, animation, secondaryAnimation) =>
                  const SurveyCompleteScreen(),
              transitionDuration: const Duration(milliseconds: 300),
              reverseTransitionDuration: const Duration(milliseconds: 300),
              transitionsBuilder:
                  (context, animation, secondaryAnimation, child) {
                const begin = Offset(1.0, 0.0);
                const end = Offset.zero;
                const curve = Curves.easeInOut;

                var tween = Tween(begin: begin, end: end).chain(
                  CurveTween(curve: curve),
                );

                return SlideTransition(
                  position: animation.drive(tween),
                  child: child,
                );
              },
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Failed to save survey. Please try again.',
                style: GoogleFonts.instrumentSans(color: Colors.white),
              ),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      }
    } catch (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Error: ${error.toString()}',
              style: GoogleFonts.instrumentSans(color: Colors.white),
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F9FC),
      body: SafeArea(
        child: Column(
          children: [
            // Progress indicator
            _buildProgressIndicator(),

            // Survey content with fade-up animation
            Expanded(
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: _buildCurrentQuestion(),
                ),
              ),
            ),

            // Navigation buttons
            _buildNavigationButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildCurrentQuestion() {
    switch (_currentPage) {
      case 0:
        return _buildHearAboutUsPage();
      case 1:
        return _buildFrustrationsPage();
      case 2:
        return _buildTravelCompanionPage();
      case 3:
        return _buildDestinationTypePage();
      case 4:
        return _buildTravelPrioritiesPage();
      default:
        return Container();
    }
  }

  void _toggleSelection(List<String> list, String value) {
    setState(() {
      if (list.contains(value)) {
        list.remove(value);
      } else {
        list.add(value);
      }
    });
  }

  void _setSingleSelection(String value, Function(String) setter) {
    setState(() {
      setter(value);
    });
  }

  Widget _buildMultiSelectOption(
      String text, bool isSelected, VoidCallback onTap) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4.0),
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
          decoration: BoxDecoration(
            color: isSelected
                ? const Color(0xFF0D76FF).withOpacity(0.1)
                : Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isSelected ? const Color(0xFF0D76FF) : Colors.grey[300]!,
              width: isSelected ? 2 : 1,
            ),
          ),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  text,
                  style: GoogleFonts.instrumentSans(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: isSelected
                        ? const Color(0xFF0D76FF)
                        : const Color(0xFF2D3748),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color:
                      isSelected ? const Color(0xFF0D76FF) : Colors.transparent,
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(
                    color: isSelected
                        ? const Color(0xFF0D76FF)
                        : Colors.grey[400]!,
                    width: 2,
                  ),
                ),
                child: isSelected
                    ? const Icon(
                        Icons.check,
                        size: 16,
                        color: Colors.white,
                      )
                    : null,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSingleSelectOption(
      String text, bool isSelected, VoidCallback onTap) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4.0),
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
          decoration: BoxDecoration(
            color: isSelected ? const Color(0xFF0D76FF) : Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isSelected ? const Color(0xFF0D76FF) : Colors.grey[300]!,
              width: isSelected ? 2 : 1,
            ),
          ),
          child: Text(
            text,
            style: GoogleFonts.instrumentSans(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: isSelected ? Colors.white : const Color(0xFF2D3748),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildIconOption(String text, IconData icon, Color iconColor,
      bool isSelected, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: isSelected
              ? const Color(0xFF0D76FF).withOpacity(0.1)
              : Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? const Color(0xFF0D76FF) : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: Colors.transparent,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                size: 28,
                color: isSelected ? Colors.white : iconColor,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              text,
              style: GoogleFonts.instrumentSans(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: isSelected
                    ? const Color(0xFF0D76FF)
                    : const Color(0xFF2D3748),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIconImageOption(String text, String image, Color iconColor,
      bool isSelected, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: isSelected
              ? const Color(0xFF0D76FF).withOpacity(0.1)
              : Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? const Color(0xFF0D76FF) : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 65,
              height: 65,
              decoration: BoxDecoration(
                color: Colors.transparent,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Image.asset(image),
            ),
            const SizedBox(height: 24),
            Text(
              text,
              style: GoogleFonts.instrumentSans(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: isSelected
                    ? const Color(0xFF0D76FF)
                    : const Color(0xFF2D3748),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: List.generate(_totalPages, (index) {
          return Expanded(
            child: Container(
              height: 4,
              margin: EdgeInsets.only(right: index < _totalPages - 1 ? 8 : 0),
              decoration: BoxDecoration(
                color: index <= _currentPage
                    ? const Color(0xFF0D76FF)
                    : Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          );
        }),
      ),
    );
  }

  Widget _buildNavigationButtons() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          if (_currentPage > 0)
            Expanded(
              child: OutlinedButton(
                onPressed: _previousPage,
                style: OutlinedButton.styleFrom(
                  side: const BorderSide(color: Color(0xFF0D76FF)),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(26),
                  ),
                ),
                child: Text(
                  'Back',
                  style: GoogleFonts.instrumentSans(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF0D76FF),
                  ),
                ),
              ),
            ),
          if (_currentPage > 0) const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _canProceed() && !_isLoading ? _nextPage : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF0D76FF),
                foregroundColor: Colors.white,
                elevation: 0,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(26),
                ),
              ),
              child: _isLoading
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text(
                      _currentPage == _totalPages - 1 ? 'Complete' : 'Next',
                      style: GoogleFonts.instrumentSans(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHearAboutUsPage() {
    return _buildSurveyPage(
      title: 'How did you hear about us?',
      subtitle:
          'Was it a friend, an ad, or something else? Your insight guides our journey',
      child: SingleChildScrollView(
        child: Column(
          children: [
            const SizedBox(height: 32),
            // Grid layout for icon-based options
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: 0.93,
              children: [
                _buildIconImageOption(
                  'Facebook',
                  "assets/images/facebook.png",
                  Colors.blue,
                  _selectedHearAboutUs == 'Facebook',
                  () => _setSingleSelection(
                      'Facebook', (value) => _selectedHearAboutUs = value),
                ),
                _buildIconImageOption(
                  'Google',
                  "assets/images/google.webp",
                  Colors.white,
                  _selectedHearAboutUs == 'Google',
                  () => _setSingleSelection(
                      'Google', (value) => _selectedHearAboutUs = value),
                ),
                _buildIconImageOption(
                  'TikTok',
                  "assets/images/tiktok.png",
                  Colors.black,
                  _selectedHearAboutUs == 'TikTok',
                  () => _setSingleSelection(
                      'TikTok', (value) => _selectedHearAboutUs = value),
                ),
                _buildIconImageOption(
                  'Instagram',
                  "assets/images/instagram.png",
                  Colors.purple,
                  _selectedHearAboutUs == 'Instagram',
                  () => _setSingleSelection(
                      'Instagram', (value) => _selectedHearAboutUs = value),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // Others option as full-width button
            _buildSingleSelectOption(
              'Others',
              _selectedHearAboutUs == 'Others',
              () => _setSingleSelection(
                  'Others', (value) => _selectedHearAboutUs = value),
            ),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildFrustrationsPage() {
    return _buildSurveyPage(
      title: "What's your BIGGEST frustration when planning trips?",
      subtitle:
          "Help us turn your travel planning headaches into smooth adventures!",
      child: SingleChildScrollView(
        child: Column(
          children: [
            const SizedBox(height: 32),
            _buildMultiSelectOption(
              'Overwhelming options/info',
              _selectedFrustrations.contains('Overwhelming options/info'),
              () => _toggleSelection(
                  _selectedFrustrations, 'Overwhelming options/info'),
            ),
            const SizedBox(height: 12),
            _buildMultiSelectOption(
              'Unreliable pricing or availability',
              _selectedFrustrations
                  .contains('Unreliable pricing or availability'),
              () => _toggleSelection(
                  _selectedFrustrations, 'Unreliable pricing or availability'),
            ),
            const SizedBox(height: 12),
            _buildMultiSelectOption(
              'Difficulty coordinating with others',
              _selectedFrustrations
                  .contains('Difficulty coordinating with others'),
              () => _toggleSelection(
                  _selectedFrustrations, 'Difficulty coordinating with others'),
            ),
            const SizedBox(height: 12),
            _buildMultiSelectOption(
              'Fear of missing hidden gems',
              _selectedFrustrations.contains('Fear of missing hidden gems'),
              () => _toggleSelection(
                  _selectedFrustrations, 'Fear of missing hidden gems'),
            ),
            const SizedBox(height: 12),
            _buildMultiSelectOption(
              'Language barriers',
              _selectedFrustrations.contains('Language barriers'),
              () =>
                  _toggleSelection(_selectedFrustrations, 'Language barriers'),
            ),
            const SizedBox(height: 12),
            _buildMultiSelectOption(
              'Other...',
              _selectedFrustrations.contains('Other...'),
              () => _toggleSelection(_selectedFrustrations, 'Other...'),
            ),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildTravelCompanionPage() {
    return _buildSurveyPage(
      title: 'Who do you MOST OFTEN travel with?',
      subtitle:
          'Your travel crew = your experiences! Tell us for perfectly tailored vibes.',
      child: SingleChildScrollView(
        child: Column(
          children: [
            const SizedBox(height: 32),
            _buildSingleSelectOption(
              'Solo traveler',
              _selectedTravelCompanion == 'Solo traveler',
              () => _setSingleSelection(
                  'Solo traveler', (value) => _selectedTravelCompanion = value),
            ),
            const SizedBox(height: 12),
            _buildSingleSelectOption(
              'Partner/Spouse',
              _selectedTravelCompanion == 'Partner/Spouse',
              () => _setSingleSelection('Partner/Spouse',
                  (value) => _selectedTravelCompanion = value),
            ),
            const SizedBox(height: 12),
            _buildSingleSelectOption(
              'Friends',
              _selectedTravelCompanion == 'Friends',
              () => _setSingleSelection(
                  'Friends', (value) => _selectedTravelCompanion = value),
            ),
            const SizedBox(height: 12),
            _buildSingleSelectOption(
              'Family with kids',
              _selectedTravelCompanion == 'Family with kids',
              () => _setSingleSelection('Family with kids',
                  (value) => _selectedTravelCompanion = value),
            ),
            const SizedBox(height: 12),
            _buildSingleSelectOption(
              'Business colleagues',
              _selectedTravelCompanion == 'Business colleagues',
              () => _setSingleSelection('Business colleagues',
                  (value) => _selectedTravelCompanion = value),
            ),
            const SizedBox(height: 12),
            _buildSingleSelectOption(
              'Other...',
              _selectedTravelCompanion == 'Other...',
              () => _setSingleSelection(
                  'Other...', (value) => _selectedTravelCompanion = value),
            ),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildDestinationTypePage() {
    return _buildSurveyPage(
      title: 'Which type of destinations do you usually explore?',
      subtitle:
          'Your travel style reflects hidden gems! Tell us where you roam for laser-focused recs.',
      child: SingleChildScrollView(
        child: Column(
          children: [
            const SizedBox(height: 32),
            _buildMultiSelectOption(
              'Popular tourist hubs (e.g., Paris, NYC)',
              _selectedDestinationTypes
                  .contains('Popular tourist hubs (e.g., Paris, NYC)'),
              () => _toggleSelection(_selectedDestinationTypes,
                  'Popular tourist hubs (e.g., Paris, NYC)'),
            ),
            const SizedBox(height: 12),
            _buildMultiSelectOption(
              'Offbeat/local gems (e.g., small towns)',
              _selectedDestinationTypes
                  .contains('Offbeat/local gems (e.g., small towns)'),
              () => _toggleSelection(_selectedDestinationTypes,
                  'Offbeat/local gems (e.g., small towns)'),
            ),
            const SizedBox(height: 12),
            _buildMultiSelectOption(
              'Nature/outdoor adventures (e.g., national parks)',
              _selectedDestinationTypes
                  .contains('Nature/outdoor adventures (e.g., national parks)'),
              () => _toggleSelection(_selectedDestinationTypes,
                  'Nature/outdoor adventures (e.g., national parks)'),
            ),
            const SizedBox(height: 12),
            _buildMultiSelectOption(
              'Urban/city experiences (e.g., museums, nightlife)',
              _selectedDestinationTypes.contains(
                  'Urban/city experiences (e.g., museums, nightlife)'),
              () => _toggleSelection(_selectedDestinationTypes,
                  'Urban/city experiences (e.g., museums, nightlife)'),
            ),
            const SizedBox(height: 12),
            _buildMultiSelectOption(
              'Mix of all the above',
              _selectedDestinationTypes.contains('Mix of all the above'),
              () => _toggleSelection(
                  _selectedDestinationTypes, 'Mix of all the above'),
            ),
            const SizedBox(height: 12),
            _buildMultiSelectOption(
              'Other...',
              _selectedDestinationTypes.contains('Other...'),
              () => _toggleSelection(_selectedDestinationTypes, 'Other...'),
            ),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildTravelPrioritiesPage() {
    return _buildSurveyPage(
      title: 'What matters MOST when you travel?',
      subtitle: 'Your top 2 concerns = All that prioritizes what you love!',
      child: SingleChildScrollView(
        child: Column(
          children: [
            const SizedBox(height: 32),
            _buildMultiSelectOption(
              'Trying local food',
              _selectedTravelPriorities.contains('Trying local food'),
              () => _toggleSelection(
                  _selectedTravelPriorities, 'Trying local food'),
            ),
            const SizedBox(height: 12),
            _buildMultiSelectOption(
              'Adventure activities',
              _selectedTravelPriorities.contains('Adventure activities'),
              () => _toggleSelection(
                  _selectedTravelPriorities, 'Adventure activities'),
            ),
            const SizedBox(height: 12),
            _buildMultiSelectOption(
              'Relaxation & wellness',
              _selectedTravelPriorities.contains('Relaxation & wellness'),
              () => _toggleSelection(
                  _selectedTravelPriorities, 'Relaxation & wellness'),
            ),
            const SizedBox(height: 12),
            _buildMultiSelectOption(
              'Cultural/historical sites',
              _selectedTravelPriorities.contains('Cultural/historical sites'),
              () => _toggleSelection(
                  _selectedTravelPriorities, 'Cultural/historical sites'),
            ),
            const SizedBox(height: 12),
            _buildMultiSelectOption(
              'Photography/scenic spots',
              _selectedTravelPriorities.contains('Photography/scenic spots'),
              () => _toggleSelection(
                  _selectedTravelPriorities, 'Photography/scenic spots'),
            ),
            const SizedBox(height: 12),
            _buildMultiSelectOption(
              'Shopping',
              _selectedTravelPriorities.contains('Shopping'),
              () => _toggleSelection(_selectedTravelPriorities, 'Shopping'),
            ),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildSurveyPage({
    required String title,
    required String subtitle,
    required Widget child,
  }) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return Padding(
          padding: EdgeInsets.symmetric(
            horizontal: constraints.maxWidth > 600 ? 40 : 20,
            vertical: 20,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: GoogleFonts.instrumentSans(
                  fontSize: constraints.maxWidth > 600 ? 32 : 28,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF2D3748),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                subtitle,
                style: GoogleFonts.instrumentSans(
                  fontSize: constraints.maxWidth > 600 ? 18 : 16,
                  color: const Color(0xFF718096),
                  height: 1.4,
                ),
              ),
              Expanded(child: child),
            ],
          ),
        );
      },
    );
  }
}
