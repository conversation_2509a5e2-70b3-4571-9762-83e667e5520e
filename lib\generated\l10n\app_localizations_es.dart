/// Generated file. Do not edit.
///
/// This file contains the localized strings for the TripWiseGo app.
/// To add a new localized string, add it to the appropriate .arb file
/// in the lib/l10n directory and run `flutter gen-l10n`.


import 'app_localizations.dart';

/// The translations for Spanish Castilian (`es`).
class AppLocalizationsEs extends AppLocalizations {
  AppLocalizationsEs([String locale = 'es']) : super(locale);

  @override
  String get appTitle => 'TripWiseGo';

  @override
  String get welcome => 'Bienvenido';

  @override
  String get guestUser => 'Usuario invitado';

  @override
  String get readyForAdventure => 'Listo para tu próxima aventura';

  @override
  String get exploringAsGuest => 'Explorando el mundo como invitado';

  @override
  String get editProfile => 'Editar perfil';

  @override
  String get saveChanges => 'Guardar cambios';

  @override
  String get cancel => 'Cancelar';

  @override
  String get username => 'Nombre de usuario';

  @override
  String get email => 'Correo electrónico';

  @override
  String get profileUpdatedSuccessfully => '¡Perfil actualizado exitosamente!';

  @override
  String failedToUpdateProfile(String error) {
    return 'Error al actualizar el perfil: $error';
  }

  @override
  String get profilePictureUpdatedSuccessfully => '¡Foto de perfil actualizada exitosamente!';

  @override
  String failedToUploadImage(String error) {
    return 'Error al subir la imagen: $error';
  }

  @override
  String get profileEditingNotAvailableForGuests => 'La edición de perfil no está disponible para usuarios invitados';

  @override
  String get profilePictureEditingNotAvailableForGuests => 'La edición de foto de perfil no está disponible para usuarios invitados';

  @override
  String get usernameCannotBeEmpty => 'El nombre de usuario no puede estar vacío';

  @override
  String get usernameMustBeBetween2And30Characters => 'El nombre de usuario debe tener entre 2 y 30 caracteres';

  @override
  String get plan => 'Planificar';

  @override
  String get termsOfService => 'Términos de servicio';

  @override
  String get language => 'Idioma';

  @override
  String get privacyPolicy => 'Política de Privacidad';

  @override
  String get support => 'Soporte';

  @override
  String get helpCenter => 'Centro de ayuda';

  @override
  String get contactUs => 'Contáctanos';

  @override
  String get helpSupport => 'Ayuda y Soporte';

  @override
  String get howCanWeHelpYou => '¿Cómo podemos ayudarte?';

  @override
  String get helpSupportGreeting => '¡Hola! Estoy aquí para ayudarte con TripwiseGO. Puedes preguntarme sobre las funciones de la aplicación, solución de problemas, o reportar cualquier problema que estés experimentando.';

  @override
  String get helpSupportWelcome => '¡Bienvenido al Soporte de TripwiseGO! Aquí hay algunas cosas con las que puedo ayudarte:';

  @override
  String get helpSupportFeatures => '• Funciones de la aplicación y cómo usarlas\n• Navegación y ayuda de cuenta\n• Solución de problemas comunes\n• Reportar errores o problemas';

  @override
  String get helpSupportAskQuestion => '¡Siéntete libre de preguntarme cualquier cosa o describir cualquier problema que tengas!';

  @override
  String get reportIssue => 'Reportar Problema';

  @override
  String get reportBug => 'Reportar Error';

  @override
  String get reportProblem => 'Reportar Problema';

  @override
  String get issueCategory => 'Categoría del Problema';

  @override
  String get bugReport => 'Reporte de Error';

  @override
  String get featureRequest => 'Solicitud de Función';

  @override
  String get generalFeedback => 'Comentarios Generales';

  @override
  String get accountIssue => 'Problema de Cuenta';

  @override
  String get technicalProblem => 'Problema Técnico';

  @override
  String get yourName => 'Tu Nombre';

  @override
  String get yourEmail => 'Tu Email';

  @override
  String get issueDescription => 'Descripción del Problema';

  @override
  String get describeIssueDetail => 'Por favor describe el problema en detalle';

  @override
  String get optionalScreenshot => 'Captura de Pantalla (Opcional)';

  @override
  String get submitReport => 'Enviar Reporte';

  @override
  String get reportSubmitted => 'Reporte Enviado';

  @override
  String get reportSubmittedSuccess => '¡Gracias! Tu reporte ha sido enviado exitosamente. Lo revisaremos y te contactaremos si es necesario.';

  @override
  String get reportSubmissionFailed => 'Error al enviar el reporte. Por favor intenta de nuevo más tarde.';

  @override
  String get nameRequired => 'El nombre es requerido';

  @override
  String get emailRequired => 'El email es requerido';

  @override
  String get validEmailRequired => 'Por favor ingresa una dirección de email válida';

  @override
  String get descriptionRequired => 'La descripción del problema es requerida';

  @override
  String get selectCategory => 'Por favor selecciona una categoría';

  @override
  String get subscription => 'Suscripción';

  @override
  String get subscriptionActive => '¡Suscripción Activa!';

  @override
  String get welcomeToPremium => '¡Bienvenido a TripwiseGO Premium! Disfruta de acceso ilimitado a todas las funciones.';

  @override
  String get currentPlan => 'Plan Actual';

  @override
  String get trial => 'Prueba';

  @override
  String get active => 'Activo';

  @override
  String trialEndsIn(int days) {
    return 'La prueba termina en $days días';
  }

  @override
  String renewsIn(int days) {
    return 'Se renueva en $days días';
  }

  @override
  String get yourBenefits => 'Tus Beneficios';

  @override
  String get startPlanningTrip => 'Comenzar a Planificar Tu Viaje';

  @override
  String get manageSubscription => 'Gestionar Suscripción';

  @override
  String get basic => 'Básico';

  @override
  String get premium => 'Premium';

  @override
  String get basicPlaceRecommendations => 'Recomendaciones de Lugares Básicas';

  @override
  String get locationRecommendationSwiping => 'Deslizamiento de Recomendaciones de Ubicación (20 deslizamientos/día)';

  @override
  String get aiPoweredTravelPlanner => 'Planificador de Viajes con IA';

  @override
  String get unlimitedQuizzes => 'Cuestionarios y Datos Curiosos Ilimitados';

  @override
  String get noAds => 'Sin Anuncios';

  @override
  String get oneMonth => '1 Mes';

  @override
  String get threeMonths => '3 Meses';

  @override
  String get fiveMonths => '5 Meses';

  @override
  String dayFreeTrial(int days) {
    return 'Prueba gratuita de $days días';
  }

  @override
  String get billedMonthly => 'Facturado mensualmente';

  @override
  String get billedTwiceAnnually => 'Facturado dos veces al año';

  @override
  String get billedAnnually => 'Facturado anualmente';

  @override
  String get save45Percent => 'AHORRA 45%';

  @override
  String get continueButton => 'Continuar';

  @override
  String get termsAndConditions => 'Términos y Condiciones';

  @override
  String get failedToSubscribe => 'Error al suscribirse. Por favor intenta de nuevo.';

  @override
  String get signOut => 'Cerrar sesión';

  @override
  String get selectLanguage => 'Seleccionar idioma';

  @override
  String get chooseYourPreferredLanguage => 'Elige tu idioma preferido';

  @override
  String get languageUpdatedSuccessfully => '¡Idioma actualizado exitosamente!';

  @override
  String get home => 'Inicio';

  @override
  String get match => 'Coincidencia';

  @override
  String get chat => 'Chat';

  @override
  String get profile => 'Perfil';

  @override
  String get loading => 'Cargando...';

  @override
  String get error => 'Error';

  @override
  String get retry => 'Reintentar';

  @override
  String get ok => 'OK';

  @override
  String get yes => 'Sí';

  @override
  String get no => 'No';

  @override
  String get save => 'Guardar';

  @override
  String get delete => 'Eliminar';

  @override
  String get edit => 'Editar';

  @override
  String get add => 'Agregar';

  @override
  String get remove => 'Quitar';

  @override
  String get close => 'Cerrar';

  @override
  String get back => 'Atrás';

  @override
  String get next => 'Siguiente';

  @override
  String get previous => 'Anterior';

  @override
  String get done => 'Hecho';

  @override
  String get search => 'Buscar';

  @override
  String get noResultsFound => 'No se encontraron resultados';

  @override
  String get tryAgain => 'Intentar de nuevo';

  @override
  String get somethingWentWrong => 'Algo salió mal';

  @override
  String get networkError => 'Error de red. Por favor verifica tu conexión.';

  @override
  String get serverError => 'Error del servidor. Por favor intenta más tarde.';

  @override
  String get invalidInput => 'Entrada inválida';

  @override
  String get required => 'Requerido';

  @override
  String get optional => 'Opcional';

  @override
  String get itinerary => 'Itinerary';

  @override
  String get yourItineraries => 'Your Itineraries';

  @override
  String get startPlanningYourTrip => 'Start Planning Your Trip';

  @override
  String get tripPlanningFeaturesWillAppearHere => 'Your trip planning features will appear here. The persistent authentication is now working!';

  @override
  String get forYou => 'For You';

  @override
  String get liked => 'Liked';

  @override
  String get collab => 'Collab';

  @override
  String get collaborativeItinerary => 'Colaborativo';

  @override
  String get endSession => 'End Session';

  @override
  String get logout => 'Logout';

  @override
  String logoutFailed(String error) {
    return 'Logout failed: $error';
  }

  @override
  String get noItineraryFound => 'No se encontró itinerario';

  @override
  String get askAiToCreateTravelPlan => '¡Pide a nuestra IA que cree un plan de viaje para ti!';

  @override
  String get saturday => 'Sábado';

  @override
  String get tuesday => 'Martes';

  @override
  String dayNumber(int number) {
    return 'Día $number';
  }

  @override
  String get itineraryOverview => 'Resumen del itinerario';

  @override
  String daysAndNights(int days, int nights) {
    return '${days}d ${nights}n';
  }

  @override
  String get hiImWanderlyAi => 'Hola, soy Wanderly AI 🌏';

  @override
  String get yourTravelAiAssistant => 'Tu asistente de viajes IA, ¿cómo puedo ayudarte hoy?';

  @override
  String get useThisBubbleChat => 'Usa este chat de burbujas';

  @override
  String get aiAssistant => 'Asistente IA';

  @override
  String get chatHistory => 'Historial de chat';

  @override
  String get newChat => 'Nuevo chat';

  @override
  String get addImage => 'Agregar imagen';

  @override
  String get camera => 'Cámara';

  @override
  String get gallery => 'Galería';

  @override
  String get microphonePermissionRequired => 'Se requiere permiso de micrófono para entrada de voz';

  @override
  String get speechRecognitionNotAvailable => 'El reconocimiento de voz no está disponible en este dispositivo';

  @override
  String get listening => 'Escuchando...';

  @override
  String get deleteChat => 'Eliminar chat';

  @override
  String get deleteChatConfirmation => '¿Estás seguro de que quieres eliminar este chat? Esta acción no se puede deshacer.';

  @override
  String get chatDeletedSuccessfully => 'Chat eliminado exitosamente';

  @override
  String get pleaseEnterSearchQuery => 'Por favor ingresa una consulta de búsqueda';

  @override
  String get dailySearchLimitReached => 'Límite de búsqueda diaria alcanzado. Puedes realizar 5 búsquedas por día.';

  @override
  String get searchingTheWeb => 'Buscando en la web...';

  @override
  String get webSearchModeActive => 'Modo de búsqueda web activo';

  @override
  String get pleaseWaitWhileSearching => 'Por favor espera mientras busco información';

  @override
  String get yourNextMessageWillSearch => 'Tu próximo mensaje buscará en la web';

  @override
  String get disableWebSearch => 'Desactivar búsqueda web';

  @override
  String get enableWebSearch => 'Activar búsqueda web';

  @override
  String get switchBackToAiChatMode => 'Volver al modo de chat IA';

  @override
  String get searchWebForCurrentInfo => 'Buscar en la web información actual';

  @override
  String get pickImageFromGallery => 'Elegir imagen de la galería';

  @override
  String get uploadImageForAiAnalysis => 'Subir una imagen para análisis IA';

  @override
  String get yourMessage => 'Tu mensaje';

  @override
  String get wanderlyAi => 'Wanderly AI';

  @override
  String get webSearch => 'Búsqueda web:';

  @override
  String get like => 'Me gusta';

  @override
  String get dislike => 'No me gusta';

  @override
  String get copy => 'Copiar';

  @override
  String get regenerate => 'Regenerar';

  @override
  String get failedToSubmitFeedback => 'Error al enviar comentarios. Por favor intenta de nuevo.';

  @override
  String get thankYouForFeedback => '¡Gracias por tus comentarios! 🙏';

  @override
  String get feedbackReceivedThanks => 'Comentarios recibidos. ¡Gracias por ayudarnos a mejorar! 🚀';

  @override
  String get responseCopiedToClipboard => 'Respuesta copiada al portapapeles';

  @override
  String get wanderlyAiIsTyping => 'Wanderly AI está escribiendo';

  @override
  String get stopGeneration => 'Detener generación';

  @override
  String get youHaveChatsLeft => 'Te quedan 10 chats';

  @override
  String get enterSearchQuery => 'Ingresa tu consulta de búsqueda...';

  @override
  String get askMeAnythingOrLongPress => 'Pregúntame cualquier cosa o mantén presionado para hablar...';

  @override
  String failedToPickImage(String error) {
    return 'Error al seleccionar imagen: $error';
  }

  @override
  String get failedToAnalyzeImage => 'Error al analizar imagen. Por favor intenta de nuevo.';

  @override
  String get responseGenerationStopped => 'La generación de respuesta fue detenida.';

  @override
  String get unknownDestination => 'Destino desconocido';

  @override
  String get congratulations => 'Felicitaciones';

  @override
  String get itsAMatch => 'Es un match';

  @override
  String get travelVibesAligned => 'Las vibras de viaje están alineadas\nempaca tus maletas, ¡tienes un match!';

  @override
  String get matchedPreferences => 'Preferencias coincidentes:';

  @override
  String get addToItinerary => 'Agregar al itinerario';

  @override
  String get keepSwiping => 'Seguir deslizando';

  @override
  String get versionInfo => 'Información de versión';

  @override
  String get appVersion => 'Versión de la aplicación';

  @override
  String get buildNumber => 'Número de compilación';

  @override
  String get packageName => 'Nombre del paquete';

  @override
  String get appName => 'Nombre de la aplicación';

  @override
  String get buildSignature => 'Firma de compilación';

  @override
  String get installerStore => 'Tienda de instalación';

  @override
  String get deviceInfo => 'Información del dispositivo';

  @override
  String get operatingSystem => 'Sistema operativo';

  @override
  String get flutterVersion => 'Versión de Flutter';

  @override
  String get dartVersion => 'Versión de Dart';

  @override
  String get leaveAReview => 'Dejar una reseña';

  @override
  String get rateOurApp => 'Califica nuestra app';

  @override
  String get enjoyingTripWiseGo => '¿Disfrutando TripWiseGo?';

  @override
  String get helpUsImproveByLeavingReview => 'Ayúdanos a mejorar dejando una reseña en la tienda de aplicaciones. ¡Tu opinión es muy importante para nosotros!';

  @override
  String get rateNow => 'Calificar ahora';

  @override
  String get maybeLater => 'Tal vez más tarde';

  @override
  String get reviewFeatureNotAvailable => 'La función de reseñas solo está disponible en versiones de producción';

  @override
  String get unableToOpenStore => 'No se puede abrir la tienda de aplicaciones. Por favor intenta más tarde.';

  @override
  String get thankYouForReview => '¡Gracias por tomarte el tiempo de reseñar nuestra app!';
}
