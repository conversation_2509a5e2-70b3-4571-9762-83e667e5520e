import 'dart:math';

class SuggestionGeneratorService {
  // Travel-related keywords and their associated follow-up suggestions
  static final Map<String, List<String>> _travelKeywords = {
    // Destinations
    'paris': [
      'Best time to visit Paris',
      'Paris museum passes',
      'Day trips from Paris',
      'Paris food tours',
      'Louvre visiting tips'
    ],
    'tokyo': [
      'Tokyo neighborhoods guide',
      'Tokyo food recommendations',
      'Tokyo transportation tips',
      'Best Tokyo temples',
      'Tokyo shopping districts'
    ],
    'london': [
      'London theater shows',
      'London pub recommendations',
      'London day trips',
      'London museums guide',
      'London transport passes'
    ],
    'rome': [
      'Rome ancient sites',
      'Rome food tours',
      'Vatican visiting tips',
      'Rome walking routes',
      'Best Rome gelato'
    ],
    'new york': [
      'NYC neighborhoods guide',
      'Broadway show tickets',
      'NYC food scene',
      'Central Park activities',
      'NYC rooftop bars'
    ],
    'bali': [
      'Bali temple tours',
      'Best Bali beaches',
      'Bali rice terraces',
      'Ubud activities',
      'Bali cultural experiences'
    ],
    'thailand': [
      'Thailand island hopping',
      'Thai street food guide',
      'Thailand temples',
      'Bangkok attractions',
      'Thailand beaches'
    ],
    'italy': [
      'Italy train travel',
      'Italian cuisine guide',
      'Italy wine regions',
      'Italian art museums',
      'Italy coastal towns'
    ],
    'spain': [
      'Spanish tapas guide',
      'Spain flamenco shows',
      'Spanish beaches',
      'Madrid vs Barcelona',
      'Spain cultural sites'
    ],
    'greece': [
      'Greek island hopping',
      'Greek mythology sites',
      'Greek cuisine guide',
      'Athens attractions',
      'Santorini activities'
    ],

    // Travel activities
    'hotel': [
      'Hotel booking tips',
      'Best hotel locations',
      'Hotel amenities guide',
      'Budget hotel options',
      'Luxury hotel experiences'
    ],
    'restaurant': [
      'Local dining etiquette',
      'Restaurant reservation tips',
      'Must-try local dishes',
      'Food allergy translations',
      'Best dining neighborhoods'
    ],
    'museum': [
      'Museum visiting strategies',
      'Art museum highlights',
      'Museum pass benefits',
      'Photography rules',
      'Audio guide recommendations'
    ],
    'beach': [
      'Beach safety tips',
      'Best beach activities',
      'Beach packing essentials',
      'Water sports options',
      'Sunset viewing spots'
    ],
    'temple': [
      'Temple visiting etiquette',
      'Religious site dress codes',
      'Temple architecture styles',
      'Spiritual experiences',
      'Photography permissions'
    ],
    'market': [
      'Market bargaining tips',
      'Local market specialties',
      'Market visiting hours',
      'Food market hygiene',
      'Souvenir shopping guide'
    ],

    // Travel logistics
    'flight': [
      'Flight booking strategies',
      'Airport arrival tips',
      'Baggage regulations',
      'Jet lag prevention',
      'Flight upgrade tips'
    ],
    'visa': [
      'Visa requirements',
      'Visa application process',
      'Travel document checklist',
      'Visa processing times',
      'Entry requirements'
    ],
    'budget': [
      'Budget travel tips',
      'Money-saving strategies',
      'Free activities',
      'Budget accommodation',
      'Local transportation costs'
    ],
    'itinerary': [
      'Itinerary optimization',
      'Day trip planning',
      'Time management tips',
      'Must-see priorities',
      'Flexible scheduling'
    ],
    'transportation': [
      'Local transport options',
      'Transport pass benefits',
      'Getting around safely',
      'Rush hour avoidance',
      'Alternative transport'
    ],
    'safety': [
      'Travel safety tips',
      'Emergency contacts',
      'Health precautions',
      'Scam awareness',
      'Insurance recommendations'
    ],
    'weather': [
      'Seasonal weather patterns',
      'Packing for weather',
      'Weather-dependent activities',
      'Climate considerations',
      'Best travel seasons'
    ],
    'culture': [
      'Cultural etiquette',
      'Local customs',
      'Cultural experiences',
      'Language basics',
      'Cultural festivals'
    ]
  };

  // Generic travel suggestions for fallback
  static final List<String> _genericSuggestions = [
    'Plan a weekend getaway',
    'Find budget travel tips',
    'Discover hidden gems',
    'Local food recommendations',
    'Cultural experiences',
    'Adventure activities',
    'Photography spots',
    'Transportation options',
    'Accommodation advice',
    'Travel safety tips',
    'Packing essentials',
    'Best travel seasons'
  ];

  // Initial suggestions for new conversations
  static final List<String> _initialSuggestions = [
    'Plan a trip to Paris',
    'Best hotels in Tokyo',
    'Create a 5-day Bali itinerary',
    'Budget travel tips for Europe',
    'Best time to visit Thailand'
  ];

  /// Generate contextual suggestions based on AI response
  static List<String> generateSuggestions(String aiResponse, {bool isInitial = false}) {
    if (isInitial) {
      return _getRandomSuggestions(_initialSuggestions, 3);
    }

    final suggestions = <String>[];
    final responseText = aiResponse.toLowerCase();
    
    // Extract locations and topics from the response
    final detectedKeywords = <String>[];
    
    for (final keyword in _travelKeywords.keys) {
      if (responseText.contains(keyword)) {
        detectedKeywords.add(keyword);
      }
    }

    // Generate suggestions based on detected keywords
    for (final keyword in detectedKeywords.take(2)) { // Limit to 2 keywords
      final keywordSuggestions = _travelKeywords[keyword] ?? [];
      suggestions.addAll(_getRandomSuggestions(keywordSuggestions, 2));
    }

    // If we don't have enough suggestions, add generic ones
    if (suggestions.length < 3) {
      final needed = 3 - suggestions.length;
      final genericToAdd = _getRandomSuggestions(_genericSuggestions, needed);
      suggestions.addAll(genericToAdd);
    }

    // Remove duplicates and limit to 5
    final uniqueSuggestions = suggestions.toSet().toList();
    return uniqueSuggestions.take(5).toList();
  }

  /// Get random suggestions from a list
  static List<String> _getRandomSuggestions(List<String> source, int count) {
    if (source.isEmpty) return [];
    
    final random = Random();
    final shuffled = List<String>.from(source)..shuffle(random);
    return shuffled.take(count).toList();
  }

  /// Generate follow-up questions based on specific topics mentioned
  static List<String> generateFollowUpQuestions(String aiResponse) {
    final suggestions = <String>[];
    final responseText = aiResponse.toLowerCase();

    // Look for specific patterns and generate follow-ups
    if (responseText.contains('recommend') || responseText.contains('suggest')) {
      suggestions.add('Tell me more about these recommendations');
    }

    if (responseText.contains('cost') || responseText.contains('price') || responseText.contains('budget')) {
      suggestions.add('How to save money on this');
    }

    if (responseText.contains('time') || responseText.contains('hour') || responseText.contains('day')) {
      suggestions.add('Best times to visit');
    }

    if (responseText.contains('food') || responseText.contains('restaurant') || responseText.contains('cuisine')) {
      suggestions.add('Local dining etiquette');
    }

    if (responseText.contains('transport') || responseText.contains('getting around')) {
      suggestions.add('Alternative transportation options');
    }

    return suggestions.take(2).toList();
  }

  /// Extract location names from AI response for location-specific suggestions
  static List<String> extractLocations(String aiResponse) {
    final locations = <String>[];
    final responseText = aiResponse.toLowerCase();

    // Common location patterns
    final locationKeywords = [
      'paris', 'london', 'tokyo', 'rome', 'new york', 'bali', 'thailand',
      'italy', 'spain', 'greece', 'france', 'japan', 'england', 'indonesia'
    ];

    for (final location in locationKeywords) {
      if (responseText.contains(location)) {
        locations.add(location);
      }
    }

    return locations;
  }
}
