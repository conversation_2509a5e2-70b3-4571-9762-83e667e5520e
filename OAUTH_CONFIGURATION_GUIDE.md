# OAuth Configuration Guide for TripWiseGo

This document outlines the OAuth configuration required for TripWiseGo to work properly with Google, Twitter/X, and Facebook authentication.

## Supabase Configuration

### 1. Redirect URLs

In your Supabase project settings (Authentication > URL Configuration), add the following redirect URLs:

#### Web Platform
```
https://ktdstluymbpqejmjkpsg.supabase.co/auth/v1/callback
```

#### Mobile Platform (Android & iOS)
```
io.supabase.tripwisego://login-callback/
```

### 2. Site URL
Set the site URL to your app's domain or localhost for development:
```
http://localhost:3000
```

## Provider-Specific Configuration

### Google OAuth

#### Supabase Settings
- **Provider**: Google
- **Enabled**: ✅ Yes
- **Client ID**: `************-1h0pvgq6b45okjjoi0ob9q6d3acuucsl.apps.googleusercontent.com`
- **Client Secret**: [Your Google Client Secret]

#### Google Cloud Console Settings
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Navigate to APIs & Services > Credentials
3. Add authorized redirect URIs:
   - `https://ktdstluymbpqejmjkpsg.supabase.co/auth/v1/callback`
   - `io.supabase.tripwisego://login-callback/`

### Twitter/X OAuth

#### Supabase Settings
- **Provider**: Twitter
- **Enabled**: ✅ Yes
- **API Key**: `*************************`
- **API Secret**: `iPzRRCQqqvsOCn6K4QKlXDp9cuGRX9CLDwJqpPpJVsysZANzNo`
- **Client ID**: `cTNfaHJDOUxMZmVHMEJleThUYVA6MTpjaQ`
- **Client Secret**: `lDxWJAp5ktuiOeeuri1yBgBdau-Pnu54IFYVRdXNyOJNe1Qw5I`

#### Twitter Developer Portal Settings
1. Go to [Twitter Developer Portal](https://developer.twitter.com/)
2. Navigate to your app settings
3. Add callback URLs:
   - `https://ktdstluymbpqejmjkpsg.supabase.co/auth/v1/callback`
   - `io.supabase.tripwisego://login-callback/`

### Facebook OAuth

#### Supabase Settings
- **Provider**: Facebook
- **Enabled**: ✅ Yes
- **App ID**: [Your Facebook App ID]
- **App Secret**: [Your Facebook App Secret]

#### Facebook Developer Console Settings
1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Navigate to your app settings
3. Add Valid OAuth Redirect URIs:
   - `https://ktdstluymbpqejmjkpsg.supabase.co/auth/v1/callback`
   - `io.supabase.tripwisego://login-callback/`

## URL Scheme Configuration

### Android (android/app/src/main/AndroidManifest.xml)
```xml
<intent-filter android:autoVerify="true">
    <action android:name="android.intent.action.VIEW" />
    <category android:name="android.intent.category.DEFAULT" />
    <category android:name="android.intent.category.BROWSABLE" />
    <data android:scheme="io.supabase.tripwisego" />
</intent-filter>
```

### iOS (ios/Runner/Info.plist)
```xml
<key>CFBundleURLTypes</key>
<array>
    <dict>
        <key>CFBundleURLName</key>
        <string>io.supabase.tripwisego</string>
        <key>CFBundleURLSchemes</key>
        <array>
            <string>io.supabase.tripwisego</string>
        </array>
    </dict>
</array>
```

## Testing OAuth Flow

### Expected Flow
1. User clicks OAuth button (Google/Twitter/Facebook)
2. Browser/WebView opens with OAuth provider
3. User completes authentication
4. Provider redirects to callback URL
5. App receives deep link and processes OAuth callback
6. Session is established and user is logged in
7. User is redirected to homepage

### Debugging
- Check browser network tab for redirect URLs
- Monitor Flutter debug console for OAuth callback logs
- Verify deep link handling in device logs
- Ensure Supabase project has correct redirect URLs configured

## Common Issues

1. **Invalid redirect URI**: Ensure all redirect URLs are exactly configured in provider settings
2. **Deep link not working**: Verify URL scheme configuration in Android/iOS
3. **Session not persisting**: Check auth state management and session refresh logic
4. **Web OAuth not working**: Verify web OAuth handler is properly detecting callbacks

## Security Notes

- Never expose client secrets in client-side code
- Use PKCE flow for mobile OAuth when possible
- Regularly rotate OAuth credentials
- Monitor OAuth usage in provider dashboards
