/// Generated file. Do not edit.
///
/// This file contains the localized strings for the TripWiseGo app.
/// To add a new localized string, add it to the appropriate .arb file
/// in the lib/l10n directory and run `flutter gen-l10n`.


import 'app_localizations.dart';

/// The translations for Thai (`th`).
class AppLocalizationsTh extends AppLocalizations {
  AppLocalizationsTh([String locale = 'th']) : super(locale);

  @override
  String get appTitle => 'TripWiseGo';

  @override
  String get welcome => 'ยินดีต้อนรับ';

  @override
  String get guestUser => 'ผู้ใช้แขก';

  @override
  String get readyForAdventure => 'พร้อมสำหรับการผจญภัยครั้งต่อไป';

  @override
  String get exploringAsGuest => 'สำรวจโลกในฐานะแขก';

  @override
  String get editProfile => 'แก้ไขโปรไฟล์';

  @override
  String get saveChanges => 'บันทึกการเปลี่ยนแปลง';

  @override
  String get cancel => 'ยกเลิก';

  @override
  String get username => 'ชื่อผู้ใช้';

  @override
  String get email => 'อีเมล';

  @override
  String get profileUpdatedSuccessfully => 'อัปเดตโปรไฟล์สำเร็จ!';

  @override
  String failedToUpdateProfile(String error) {
    return 'อัปเดตโปรไฟล์ไม่สำเร็จ: $error';
  }

  @override
  String get profilePictureUpdatedSuccessfully => 'อัปเดตรูปโปรไฟล์สำเร็จ!';

  @override
  String failedToUploadImage(String error) {
    return 'อัปโหลดรูปภาพไม่สำเร็จ: $error';
  }

  @override
  String get profileEditingNotAvailableForGuests => 'การแก้ไขโปรไฟล์ไม่พร้อมใช้งานสำหรับผู้ใช้แขก';

  @override
  String get profilePictureEditingNotAvailableForGuests => 'การแก้ไขรูปโปรไฟล์ไม่พร้อมใช้งานสำหรับผู้ใช้แขก';

  @override
  String get usernameCannotBeEmpty => 'ชื่อผู้ใช้ไม่สามารถเว้นว่างได้';

  @override
  String get usernameMustBeBetween2And30Characters => 'ชื่อผู้ใช้ต้องมี 2 ถึง 30 ตัวอักษร';

  @override
  String get plan => 'แผน';

  @override
  String get termsOfService => 'เงื่อนไขการใช้บริการ';

  @override
  String get language => 'ภาษา';

  @override
  String get privacyPolicy => 'นโยบายความเป็นส่วนตัว';

  @override
  String get support => 'การสนับสนุน';

  @override
  String get helpCenter => 'ศูนย์ช่วยเหลือ';

  @override
  String get contactUs => 'ติดต่อเรา';

  @override
  String get helpSupport => 'Help & Support';

  @override
  String get howCanWeHelpYou => 'How can we help you?';

  @override
  String get helpSupportGreeting => 'Hi! I\'m here to help you with TripwiseGO. You can ask me about app features, troubleshooting, or report any issues you\'re experiencing.';

  @override
  String get helpSupportWelcome => 'Welcome to TripwiseGO Support! Here are some things I can help you with:';

  @override
  String get helpSupportFeatures => '• App features and how to use them\n• Navigation and account help\n• Troubleshooting common issues\n• Reporting bugs or problems';

  @override
  String get helpSupportAskQuestion => 'Feel free to ask me anything or describe any issues you\'re having!';

  @override
  String get reportIssue => 'Report Issue';

  @override
  String get reportBug => 'Report Bug';

  @override
  String get reportProblem => 'Report Problem';

  @override
  String get issueCategory => 'Issue Category';

  @override
  String get bugReport => 'Bug Report';

  @override
  String get featureRequest => 'Feature Request';

  @override
  String get generalFeedback => 'General Feedback';

  @override
  String get accountIssue => 'Account Issue';

  @override
  String get technicalProblem => 'Technical Problem';

  @override
  String get yourName => 'Your Name';

  @override
  String get yourEmail => 'Your Email';

  @override
  String get issueDescription => 'Issue Description';

  @override
  String get describeIssueDetail => 'Please describe the issue in detail';

  @override
  String get optionalScreenshot => 'Screenshot (Optional)';

  @override
  String get submitReport => 'Submit Report';

  @override
  String get reportSubmitted => 'Report Submitted';

  @override
  String get reportSubmittedSuccess => 'Thank you! Your report has been submitted successfully. We\'ll look into it and get back to you if needed.';

  @override
  String get reportSubmissionFailed => 'Failed to submit report. Please try again later.';

  @override
  String get nameRequired => 'Name is required';

  @override
  String get emailRequired => 'Email is required';

  @override
  String get validEmailRequired => 'Please enter a valid email address';

  @override
  String get descriptionRequired => 'Issue description is required';

  @override
  String get selectCategory => 'Please select a category';

  @override
  String get subscription => 'Subscription';

  @override
  String get subscriptionActive => 'Subscription Active!';

  @override
  String get welcomeToPremium => 'Welcome to TripwiseGO Premium! Enjoy unlimited access to all features.';

  @override
  String get currentPlan => 'Current Plan';

  @override
  String get trial => 'Trial';

  @override
  String get active => 'Active';

  @override
  String trialEndsIn(int days) {
    return 'Trial ends in $days days';
  }

  @override
  String renewsIn(int days) {
    return 'Renews in $days days';
  }

  @override
  String get yourBenefits => 'Your Benefits';

  @override
  String get startPlanningTrip => 'Start Planning Your Trip';

  @override
  String get manageSubscription => 'Manage Subscription';

  @override
  String get basic => 'Basic';

  @override
  String get premium => 'Premium';

  @override
  String get basicPlaceRecommendations => 'Basic Place Recommendations';

  @override
  String get locationRecommendationSwiping => 'Location Recommendation Swiping (20 swipes/day)';

  @override
  String get aiPoweredTravelPlanner => 'AI-Powered Travel Planner';

  @override
  String get unlimitedQuizzes => 'Unlimited Quizzes & Fun Facts';

  @override
  String get noAds => 'No-Ads';

  @override
  String get oneMonth => '1 Month';

  @override
  String get threeMonths => '3 Months';

  @override
  String get fiveMonths => '5 Months';

  @override
  String dayFreeTrial(int days) {
    return '$days-day free trial';
  }

  @override
  String get billedMonthly => 'Billed monthly';

  @override
  String get billedTwiceAnnually => 'Billed twice annually';

  @override
  String get billedAnnually => 'Billed annually';

  @override
  String get save45Percent => 'SAVE 45%';

  @override
  String get continueButton => 'Continue';

  @override
  String get termsAndConditions => 'Terms and Conditions';

  @override
  String get failedToSubscribe => 'Failed to subscribe. Please try again.';

  @override
  String get signOut => 'ออกจากระบบ';

  @override
  String get selectLanguage => 'เลือกภาษา';

  @override
  String get chooseYourPreferredLanguage => 'เลือกภาษาที่คุณต้องการ';

  @override
  String get languageUpdatedSuccessfully => 'อัปเดตภาษาสำเร็จ!';

  @override
  String get home => 'หน้าแรก';

  @override
  String get match => 'จับคู่';

  @override
  String get chat => 'แชท';

  @override
  String get profile => 'โปรไฟล์';

  @override
  String get loading => 'กำลังโหลด...';

  @override
  String get error => 'ข้อผิดพลาด';

  @override
  String get retry => 'ลองใหม่';

  @override
  String get ok => 'ตกลง';

  @override
  String get yes => 'ใช่';

  @override
  String get no => 'ไม่';

  @override
  String get save => 'บันทึก';

  @override
  String get delete => 'ลบ';

  @override
  String get edit => 'แก้ไข';

  @override
  String get add => 'เพิ่ม';

  @override
  String get remove => 'ลบออก';

  @override
  String get close => 'ปิด';

  @override
  String get back => 'กลับ';

  @override
  String get next => 'ถัดไป';

  @override
  String get previous => 'ก่อนหน้า';

  @override
  String get done => 'เสร็จสิ้น';

  @override
  String get search => 'ค้นหา';

  @override
  String get noResultsFound => 'ไม่พบผลลัพธ์';

  @override
  String get tryAgain => 'ลองใหม่';

  @override
  String get somethingWentWrong => 'เกิดข้อผิดพลาด';

  @override
  String get networkError => 'ข้อผิดพลาดเครือข่าย กรุณาตรวจสอบการเชื่อมต่อ';

  @override
  String get serverError => 'ข้อผิดพลาดเซิร์ฟเวอร์ กรุณาลองใหม่ภายหลัง';

  @override
  String get invalidInput => 'ข้อมูลไม่ถูกต้อง';

  @override
  String get required => 'จำเป็น';

  @override
  String get optional => 'ไม่บังคับ';

  @override
  String get itinerary => 'Itinerary';

  @override
  String get yourItineraries => 'Your Itineraries';

  @override
  String get startPlanningYourTrip => 'Start Planning Your Trip';

  @override
  String get tripPlanningFeaturesWillAppearHere => 'Your trip planning features will appear here. The persistent authentication is now working!';

  @override
  String get forYou => 'For You';

  @override
  String get liked => 'Liked';

  @override
  String get collab => 'Collab';

  @override
  String get collaborativeItinerary => 'Collaborative';

  @override
  String get endSession => 'End Session';

  @override
  String get logout => 'Logout';

  @override
  String logoutFailed(String error) {
    return 'Logout failed: $error';
  }

  @override
  String get noItineraryFound => 'ไม่พบกำหนดการเดินทาง';

  @override
  String get askAiToCreateTravelPlan => 'ขอให้ AI ของเราสร้างแผนการเดินทางให้คุณ!';

  @override
  String get saturday => 'วันเสาร์';

  @override
  String get tuesday => 'วันอังคาร';

  @override
  String dayNumber(int number) {
    return 'วันที่ $number';
  }

  @override
  String get itineraryOverview => 'ภาพรวมกำหนดการเดินทาง';

  @override
  String daysAndNights(int days, int nights) {
    return '$daysวัน $nightsคืน';
  }

  @override
  String get hiImWanderlyAi => 'สวัสดี ฉันคือ Wanderly AI 🌏';

  @override
  String get yourTravelAiAssistant => 'ผู้ช่วย AI การเดินทางของคุณ วันนี้ฉันจะช่วยคุณอย่างไร?';

  @override
  String get useThisBubbleChat => 'ใช้แชทแบบฟองอากาศนี้';

  @override
  String get aiAssistant => 'ผู้ช่วย AI';

  @override
  String get chatHistory => 'ประวัติการแชท';

  @override
  String get newChat => 'แชทใหม่';

  @override
  String get addImage => 'เพิ่มรูปภาพ';

  @override
  String get camera => 'กล้อง';

  @override
  String get gallery => 'แกลเลอรี';

  @override
  String get microphonePermissionRequired => 'ต้องการอนุญาตไมโครโฟนสำหรับการป้อนเสียง';

  @override
  String get speechRecognitionNotAvailable => 'การรู้จำเสียงไม่พร้อมใช้งานบนอุปกรณ์นี้';

  @override
  String get listening => 'กำลังฟัง...';

  @override
  String get deleteChat => 'ลบแชท';

  @override
  String get deleteChatConfirmation => 'คุณแน่ใจหรือไม่ว่าต้องการลบแชทนี้? การกระทำนี้ไม่สามารถยกเลิกได้';

  @override
  String get chatDeletedSuccessfully => 'ลบแชทสำเร็จแล้ว';

  @override
  String get pleaseEnterSearchQuery => 'กรุณาป้อนคำค้นหา';

  @override
  String get dailySearchLimitReached => 'ถึงขีดจำกัดการค้นหารายวันแล้ว คุณสามารถค้นหาได้ 5 ครั้งต่อวัน';

  @override
  String get searchingTheWeb => 'กำลังค้นหาเว็บ...';

  @override
  String get webSearchModeActive => 'โหมดค้นหาเว็บเปิดใช้งาน';

  @override
  String get pleaseWaitWhileSearching => 'กรุณารอสักครู่ขณะที่ฉันค้นหาข้อมูล';

  @override
  String get yourNextMessageWillSearch => 'ข้อความถัดไปของคุณจะค้นหาเว็บ';

  @override
  String get disableWebSearch => 'ปิดการค้นหาเว็บ';

  @override
  String get enableWebSearch => 'เปิดการค้นหาเว็บ';

  @override
  String get switchBackToAiChatMode => 'กลับไปยังโหมดแชท AI';

  @override
  String get searchWebForCurrentInfo => 'ค้นหาข้อมูลปัจจุบันบนเว็บ';

  @override
  String get pickImageFromGallery => 'เลือกรูปภาพจากแกลเลอรี';

  @override
  String get uploadImageForAiAnalysis => 'อัปโหลดรูปภาพสำหรับการวิเคราะห์ AI';

  @override
  String get yourMessage => 'ข้อความของคุณ';

  @override
  String get wanderlyAi => 'Wanderly AI';

  @override
  String get webSearch => 'การค้นหาเว็บ:';

  @override
  String get like => 'ชอบ';

  @override
  String get dislike => 'ไม่ชอบ';

  @override
  String get copy => 'คัดลอก';

  @override
  String get regenerate => 'สร้างใหม่';

  @override
  String get failedToSubmitFeedback => 'ส่งความคิดเห็นไม่สำเร็จ กรุณาลองใหม่อีกครั้ง';

  @override
  String get thankYouForFeedback => 'ขอบคุณสำหรับความคิดเห็นของคุณ! 🙏';

  @override
  String get feedbackReceivedThanks => 'ได้รับความคิดเห็นแล้ว ขอบคุณที่ช่วยให้เราพัฒนา! 🚀';

  @override
  String get responseCopiedToClipboard => 'คัดลอกคำตอบไปยังคลิปบอร์ดแล้ว';

  @override
  String get wanderlyAiIsTyping => 'Wanderly AI กำลังพิมพ์';

  @override
  String get stopGeneration => 'หยุดการสร้าง';

  @override
  String get youHaveChatsLeft => 'คุณเหลือแชท 10 ครั้ง';

  @override
  String get enterSearchQuery => 'ป้อนคำค้นหาของคุณ...';

  @override
  String get askMeAnythingOrLongPress => 'ถามอะไรก็ได้หรือกดค้างเพื่อพูด...';

  @override
  String failedToPickImage(String error) {
    return 'เลือกรูปภาพไม่สำเร็จ: $error';
  }

  @override
  String get failedToAnalyzeImage => 'วิเคราะห์รูปภาพไม่สำเร็จ กรุณาลองใหม่อีกครั้ง';

  @override
  String get responseGenerationStopped => 'การสร้างคำตอบถูกหยุด';

  @override
  String get unknownDestination => 'จุดหมายปลายทางที่ไม่รู้จัก';

  @override
  String get congratulations => 'Congratulations';

  @override
  String get itsAMatch => 'It\'s a match';

  @override
  String get travelVibesAligned => 'Travel vibes aligned pack\nyour bags, you\'ve got a match!';

  @override
  String get matchedPreferences => 'Matched Preferences:';

  @override
  String get addToItinerary => 'Add to Itinerary';

  @override
  String get keepSwiping => 'Keep swiping';

  @override
  String get versionInfo => 'Version Info';

  @override
  String get appVersion => 'App Version';

  @override
  String get buildNumber => 'Build Number';

  @override
  String get packageName => 'Package Name';

  @override
  String get appName => 'App Name';

  @override
  String get buildSignature => 'Build Signature';

  @override
  String get installerStore => 'Installer Store';

  @override
  String get deviceInfo => 'Device Information';

  @override
  String get operatingSystem => 'Operating System';

  @override
  String get flutterVersion => 'Flutter Version';

  @override
  String get dartVersion => 'Dart Version';

  @override
  String get leaveAReview => 'Leave a Review';

  @override
  String get rateOurApp => 'Rate Our App';

  @override
  String get enjoyingTripWiseGo => 'Enjoying TripWiseGo?';

  @override
  String get helpUsImproveByLeavingReview => 'Help us improve by leaving a review on the app store. Your feedback means a lot to us!';

  @override
  String get rateNow => 'Rate Now';

  @override
  String get maybeLater => 'Maybe Later';

  @override
  String get reviewFeatureNotAvailable => 'Review feature is only available in production builds';

  @override
  String get unableToOpenStore => 'Unable to open app store. Please try again later.';

  @override
  String get thankYouForReview => 'Thank you for taking the time to review our app!';
}
