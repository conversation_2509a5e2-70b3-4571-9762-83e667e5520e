import 'package:flutter/material.dart';
import '../generated/l10n/app_localizations.dart';

class HelpSupportChatbot {
  static String generateResponse(String userMessage, BuildContext context) {
    final lowerMessage = userMessage.toLowerCase();

    // Navigation and basic app usage
    if (_containsKeywords(lowerMessage,
        ['navigate', 'navigation', 'how to use', 'menu', 'tab'])) {
      return "**🧭 App Navigation**\n\nTripwiseGO has **5 main sections**:\n\n• **Chat** - Talk with Wanderly AI for travel advice\n• **Plan** - View and manage your itineraries\n• **Match** - Swipe through destination recommendations\n• **Collaborative** - Work on itineraries with others\n• **Profile** - Manage your account and settings\n\n💡 **Tip:** Use the bottom navigation bar to switch between sections!";
    }

    // Itinerary and planning
    if (_containsKeywords(
        lowerMessage, ['itinerary', 'plan', 'trip', 'planning', 'create'])) {
      return "**✈️ Creating Itineraries**\n\n**How to create an itinerary:**\n\n1. **Go to the Chat tab**\n2. **Ask Wanderly AI** to create an itinerary\n   *Example: \"Plan a 3-day trip to Paris\"*\n3. **AI generates** a detailed itinerary\n4. **Automatically appears** in your Plan tab\n5. **Edit, share, or collaborate** on it\n\n🤝 **Pro tip:** Create collaborative itineraries to plan with friends!";
    }

    // Matching and swiping
    if (_containsKeywords(
        lowerMessage, ['match', 'swipe', 'destinations', 'recommendations'])) {
      return "**💖 Match Feature**\n\n**How destination matching works:**\n\n• **Swipe right** ➡️ on places you like\n• **Swipe left** ⬅️ on places you don't\n• **App learns** your preferences\n• **Get personalized** recommendations\n• **Add liked destinations** to your itineraries\n\n📋 **Important:** Complete your travel survey for better matches!";
    }

    // Account and profile
    if (_containsKeywords(lowerMessage,
        ['account', 'profile', 'login', 'register', 'password'])) {
      return "**👤 Account Help**\n\n**Profile Management:**\n• **Tap Profile tab** to edit your info\n• **Update username** and avatar\n• **Change language** preferences\n\n**Account Issues:**\n• **Reset password** through login screen\n• **Contact support** for account recovery\n• **Guest users** have limited features\n\n🚨 **Need help?** Say 'report problem' to submit an issue!";
    }

    // Language and settings
    if (_containsKeywords(
        lowerMessage, ['language', 'settings', 'change language'])) {
      return "**🌍 Language Settings**\n\nTripwiseGO supports **14 languages**!\n\n**How to change language:**\n1. **Go to Profile** tab\n2. **Tap Language**\n3. **Select** your preferred language\n4. **App updates** immediately\n\n**Supported languages:**\n🇺🇸 English • 🇫🇷 French • 🇪🇸 Spanish • 🇮🇹 Italian\n🇨🇳 Chinese • 🇯🇵 Japanese • 🇰🇷 Korean • 🇮🇩 Indonesian\n🇵🇭 Filipino • 🇹🇭 Thai • 🇸🇦 Arabic • 🇮🇳 Hindi\n🇷🇺 Russian • 🇵🇹 Portuguese\n\n✨ **Note:** RTL languages like Arabic are fully supported!";
    }

    // Collaborative features
    if (_containsKeywords(lowerMessage,
        ['collaborate', 'share', 'friends', 'group', 'together'])) {
      return "**👥 Collaborative Planning**\n\n**Features for group travel:**\n\n• **Create collaborative itineraries** in Collaborative tab\n• **Invite friends** to plan together\n• **Real-time editing** - see changes instantly\n• **Comments system** - discuss plans\n• **Shared timeline** - drag and drop activities\n\n🎯 **Perfect for:** Group trips and family vacations!";
    }

    // AI and chat features
    if (_containsKeywords(
        lowerMessage, ['ai', 'chat', 'wanderly', 'ask', 'questions'])) {
      return "**🤖 Wanderly AI Assistant**\n\n**Your personal travel companion:**\n\n• **Ask anything** about travel\n• **Get personalized** recommendations\n• **Create detailed** itineraries\n• **Web search integration** for real-time info\n• **Voice input** support\n• **Multiple conversation** history\n\n💬 **Try asking:**\n*\"Plan a romantic weekend in Rome\"*\n*\"What's the weather like in Tokyo?\"*";
    }

    // Technical issues
    if (_containsKeywords(
        lowerMessage, ['slow', 'crash', 'freeze', 'loading', 'technical'])) {
      return "**🔧 Technical Issues**\n\n**Quick fixes to try:**\n\n1. **Restart the app**\n2. **Check internet connection**\n3. **Update to latest version**\n4. **Clear app cache** (Android)\n5. **Restart your device**\n\n**Still having problems?**\n\nPlease report with these details:\n• **What you were doing** when it happened\n• **Your device model**\n• **Any error messages**\n\n🚨 **Quick action:** Say 'report bug' to submit a detailed report!";
    }

    // Troubleshooting
    if (_containsKeywords(
        lowerMessage, ['help', 'problem', 'issue', 'trouble', 'not working'])) {
      return "**🆘 Troubleshooting Help**\n\nI'm here to help! **Common issues and solutions:**\n\n**📱 App not responding:**\n• **Force close** and restart the app\n• **Check for app updates**\n\n**⚡ Features not working:**\n• **Ensure internet connection**\n• **Try logging out** and back in\n\n**🔍 Can't find something:**\n• **Use search** in chat history\n• **Check Profile menu** for settings\n\n**Need more help?**\nDescribe your specific issue or say **'report problem'** to submit a detailed report!";
    }

    // Default response
    return "**💬 I understand you're asking about:**\n*\"$userMessage\"*\n\n**🎯 I can help you with:**\n\n• **App features** and how to use them\n• **Navigation** and account help\n• **Troubleshooting** common issues\n• **Creating itineraries** and planning trips\n• **Matching** and destination discovery\n• **Collaborative** planning features\n\n**Could you be more specific** about what you need help with?\n\n🚨 **Or if you're experiencing a problem,** say **'report issue'** and I'll help you submit a detailed report!";
  }

  static bool _containsKeywords(String message, List<String> keywords) {
    return keywords.any((keyword) => message.contains(keyword));
  }
}
