import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:tripwisego/screens/profile_screen.dart';
import 'package:tripwisego/config/supabase_config.dart';

void main() {
  group('Profile Picture Tests', () {
    setUpAll(() async {
      // Initialize Supabase for testing
      try {
        await SupabaseConfig.initialize();
      } catch (e) {
        // Continue with tests even if Supabase initialization fails
        print('Supabase initialization failed in tests: $e');
      }
    });

    testWidgets('Profile screen displays correctly',
        (WidgetTester tester) async {
      // Build the ProfileScreen widget
      await tester.pumpWidget(
        MaterialApp(
          home: const ProfileScreen(),
        ),
      );

      // Wait for animations to complete
      await tester.pumpAndSettle();

      // Verify that the profile screen is displayed
      expect(find.byType(ProfileScreen), findsOneWidget);

      // Verify that the profile avatar container is present
      expect(find.byType(Container), findsWidgets);

      // Verify that the camera icon is present for profile picture editing
      expect(find.byIcon(Icons.camera_alt), findsOneWidget);
    });

    testWidgets('Profile picture edit button is visible for non-guest users',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const ProfileScreen(),
        ),
      );

      await tester.pumpAndSettle();

      // Look for the camera icon which indicates the edit button
      expect(find.byIcon(Icons.camera_alt), findsOneWidget);
    });

    testWidgets('Profile screen handles loading states',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const ProfileScreen(),
        ),
      );

      // Pump the widget to trigger initial load
      await tester.pump();

      // Verify the widget builds without errors
      expect(find.byType(ProfileScreen), findsOneWidget);

      // Wait for any async operations to complete
      await tester.pumpAndSettle();
    });
  });

  group('Profile Picture Storage Tests', () {
    test('Profile picture URL validation', () {
      // Test URL format validation
      const validUrl =
          'https://ktdstluymbpqejmjkpsg.supabase.co/storage/v1/object/public/avatars/profiles/profile_123_1234567890.jpg';
      const invalidUrl = 'invalid-url';

      expect(Uri.tryParse(validUrl), isNotNull);
      expect(Uri.tryParse(invalidUrl), isNotNull); // Uri.tryParse is lenient

      // Test that valid URL has expected structure
      final uri = Uri.parse(validUrl);
      expect(uri.pathSegments.contains('avatars'), isTrue);
      expect(uri.pathSegments.contains('profiles'), isTrue);
    });

    test('File path extraction from URL', () {
      const imageUrl =
          'https://ktdstluymbpqejmjkpsg.supabase.co/storage/v1/object/public/avatars/profiles/profile_123_1234567890.jpg';
      final uri = Uri.parse(imageUrl);
      final filePath = uri.pathSegments
          .skip(5)
          .join('/'); // Skip /storage/v1/object/public/avatars/

      expect(filePath, equals('profiles/profile_123_1234567890.jpg'));
    });
  });
}
