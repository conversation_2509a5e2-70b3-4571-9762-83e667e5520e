/// Generated file. Do not edit.
///
/// This file contains the localized strings for the TripWiseGo app.
/// To add a new localized string, add it to the appropriate .arb file
/// in the lib/l10n directory and run `flutter gen-l10n`.


import 'app_localizations.dart';

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'TripWiseGo';

  @override
  String get welcome => 'Welcome';

  @override
  String get guestUser => 'Guest User';

  @override
  String get readyForAdventure => 'Ready for your next adventure';

  @override
  String get exploringAsGuest => 'Exploring the world as a guest';

  @override
  String get editProfile => 'Edit Profile';

  @override
  String get saveChanges => 'Save Changes';

  @override
  String get cancel => 'Cancel';

  @override
  String get username => 'Username';

  @override
  String get email => 'Email';

  @override
  String get profileUpdatedSuccessfully => 'Profile updated successfully!';

  @override
  String failedToUpdateProfile(String error) {
    return 'Failed to update profile: $error';
  }

  @override
  String get profilePictureUpdatedSuccessfully => 'Profile picture updated successfully!';

  @override
  String failedToUploadImage(String error) {
    return 'Failed to upload image: $error';
  }

  @override
  String get profileEditingNotAvailableForGuests => 'Profile editing is not available for guest users';

  @override
  String get profilePictureEditingNotAvailableForGuests => 'Profile picture editing is not available for guest users';

  @override
  String get usernameCannotBeEmpty => 'Username cannot be empty';

  @override
  String get usernameMustBeBetween2And30Characters => 'Username must be between 2 and 30 characters';

  @override
  String get plan => 'Plan';

  @override
  String get termsOfService => 'Terms of Service';

  @override
  String get language => 'Language';

  @override
  String get privacyPolicy => 'Privacy Policy';

  @override
  String get support => 'Support';

  @override
  String get helpCenter => 'Help Center';

  @override
  String get contactUs => 'Contact Us';

  @override
  String get helpSupport => 'Help & Support';

  @override
  String get howCanWeHelpYou => 'How can we help you?';

  @override
  String get helpSupportGreeting => 'Hi! I\'m here to help you with TripwiseGO. You can ask me about app features, troubleshooting, or report any issues you\'re experiencing.';

  @override
  String get helpSupportWelcome => 'Welcome to TripwiseGO Support! Here are some things I can help you with:';

  @override
  String get helpSupportFeatures => '• App features and how to use them\n• Navigation and account help\n• Troubleshooting common issues\n• Reporting bugs or problems';

  @override
  String get helpSupportAskQuestion => 'Feel free to ask me anything or describe any issues you\'re having!';

  @override
  String get reportIssue => 'Report Issue';

  @override
  String get reportBug => 'Report Bug';

  @override
  String get reportProblem => 'Report Problem';

  @override
  String get issueCategory => 'Issue Category';

  @override
  String get bugReport => 'Bug Report';

  @override
  String get featureRequest => 'Feature Request';

  @override
  String get generalFeedback => 'General Feedback';

  @override
  String get accountIssue => 'Account Issue';

  @override
  String get technicalProblem => 'Technical Problem';

  @override
  String get yourName => 'Your Name';

  @override
  String get yourEmail => 'Your Email';

  @override
  String get issueDescription => 'Issue Description';

  @override
  String get describeIssueDetail => 'Please describe the issue in detail';

  @override
  String get optionalScreenshot => 'Screenshot (Optional)';

  @override
  String get submitReport => 'Submit Report';

  @override
  String get reportSubmitted => 'Report Submitted';

  @override
  String get reportSubmittedSuccess => 'Thank you! Your report has been submitted successfully. We\'ll look into it and get back to you if needed.';

  @override
  String get reportSubmissionFailed => 'Failed to submit report. Please try again later.';

  @override
  String get nameRequired => 'Name is required';

  @override
  String get emailRequired => 'Email is required';

  @override
  String get validEmailRequired => 'Please enter a valid email address';

  @override
  String get descriptionRequired => 'Issue description is required';

  @override
  String get selectCategory => 'Please select a category';

  @override
  String get subscription => 'Subscription';

  @override
  String get subscriptionActive => 'Subscription Active!';

  @override
  String get welcomeToPremium => 'Welcome to TripwiseGO Premium! Enjoy unlimited access to all features.';

  @override
  String get currentPlan => 'Current Plan';

  @override
  String get trial => 'Trial';

  @override
  String get active => 'Active';

  @override
  String trialEndsIn(int days) {
    return 'Trial ends in $days days';
  }

  @override
  String renewsIn(int days) {
    return 'Renews in $days days';
  }

  @override
  String get yourBenefits => 'Your Benefits';

  @override
  String get startPlanningTrip => 'Start Planning Your Trip';

  @override
  String get manageSubscription => 'Manage Subscription';

  @override
  String get basic => 'Basic';

  @override
  String get premium => 'Premium';

  @override
  String get basicPlaceRecommendations => 'Basic Place Recommendations';

  @override
  String get locationRecommendationSwiping => 'Location Recommendation Swiping (20 swipes/day)';

  @override
  String get aiPoweredTravelPlanner => 'AI-Powered Travel Planner';

  @override
  String get unlimitedQuizzes => 'Unlimited Quizzes & Fun Facts';

  @override
  String get noAds => 'No-Ads';

  @override
  String get oneMonth => '1 Month';

  @override
  String get threeMonths => '3 Months';

  @override
  String get fiveMonths => '5 Months';

  @override
  String dayFreeTrial(int days) {
    return '$days-day free trial';
  }

  @override
  String get billedMonthly => 'Billed monthly';

  @override
  String get billedTwiceAnnually => 'Billed twice annually';

  @override
  String get billedAnnually => 'Billed annually';

  @override
  String get save45Percent => 'SAVE 45%';

  @override
  String get continueButton => 'Continue';

  @override
  String get termsAndConditions => 'Terms and Conditions';

  @override
  String get failedToSubscribe => 'Failed to subscribe. Please try again.';

  @override
  String get signOut => 'Sign Out';

  @override
  String get selectLanguage => 'Select Language';

  @override
  String get chooseYourPreferredLanguage => 'Choose your preferred language';

  @override
  String get languageUpdatedSuccessfully => 'Language updated successfully!';

  @override
  String get home => 'Home';

  @override
  String get match => 'Match';

  @override
  String get chat => 'Chat';

  @override
  String get profile => 'Profile';

  @override
  String get loading => 'Loading...';

  @override
  String get error => 'Error';

  @override
  String get retry => 'Retry';

  @override
  String get ok => 'OK';

  @override
  String get yes => 'Yes';

  @override
  String get no => 'No';

  @override
  String get save => 'Save';

  @override
  String get delete => 'Delete';

  @override
  String get edit => 'Edit';

  @override
  String get add => 'Add';

  @override
  String get remove => 'Remove';

  @override
  String get close => 'Close';

  @override
  String get back => 'Back';

  @override
  String get next => 'Next';

  @override
  String get previous => 'Previous';

  @override
  String get done => 'Done';

  @override
  String get search => 'Search';

  @override
  String get noResultsFound => 'No results found';

  @override
  String get tryAgain => 'Try again';

  @override
  String get somethingWentWrong => 'Something went wrong';

  @override
  String get networkError => 'Network error. Please check your connection.';

  @override
  String get serverError => 'Server error. Please try again later.';

  @override
  String get invalidInput => 'Invalid input';

  @override
  String get required => 'Required';

  @override
  String get optional => 'Optional';

  @override
  String get itinerary => 'Itinerary';

  @override
  String get yourItineraries => 'Your Itineraries';

  @override
  String get startPlanningYourTrip => 'Start Planning Your Trip';

  @override
  String get tripPlanningFeaturesWillAppearHere => 'Your trip planning features will appear here. The persistent authentication is now working!';

  @override
  String get forYou => 'For You';

  @override
  String get liked => 'Liked';

  @override
  String get collab => 'Collab';

  @override
  String get collaborativeItinerary => 'Collaborative';

  @override
  String get endSession => 'End Session';

  @override
  String get logout => 'Logout';

  @override
  String logoutFailed(String error) {
    return 'Logout failed: $error';
  }

  @override
  String get noItineraryFound => 'No Itinerary Found';

  @override
  String get askAiToCreateTravelPlan => 'Ask our AI to create a travel plan for you!';

  @override
  String get saturday => 'Saturday';

  @override
  String get tuesday => 'Tuesday';

  @override
  String dayNumber(int number) {
    return 'Day $number';
  }

  @override
  String get itineraryOverview => 'Itinerary Overview';

  @override
  String daysAndNights(int days, int nights) {
    return '${days}d ${nights}n';
  }

  @override
  String get hiImWanderlyAi => 'Hi, I\'m Wanderly AI 🌏';

  @override
  String get yourTravelAiAssistant => 'Your Travel AI Assistant, how can I help you today?';

  @override
  String get useThisBubbleChat => 'Use this bubble chat';

  @override
  String get aiAssistant => 'AI Assistant';

  @override
  String get chatHistory => 'Chat History';

  @override
  String get newChat => 'New Chat';

  @override
  String get addImage => 'Add Image';

  @override
  String get camera => 'Camera';

  @override
  String get gallery => 'Gallery';

  @override
  String get microphonePermissionRequired => 'Microphone permission is required for voice input';

  @override
  String get speechRecognitionNotAvailable => 'Speech recognition is not available on this device';

  @override
  String get listening => 'Listening...';

  @override
  String get deleteChat => 'Delete Chat';

  @override
  String get deleteChatConfirmation => 'Are you sure you want to delete this chat? This action cannot be undone.';

  @override
  String get chatDeletedSuccessfully => 'Chat deleted successfully';

  @override
  String get pleaseEnterSearchQuery => 'Please enter a search query';

  @override
  String get dailySearchLimitReached => 'Daily search limit reached. You can perform 5 searches per day.';

  @override
  String get searchingTheWeb => 'Searching the Web...';

  @override
  String get webSearchModeActive => 'Web Search Mode Active';

  @override
  String get pleaseWaitWhileSearching => 'Please wait while I search for information';

  @override
  String get yourNextMessageWillSearch => 'Your next message will search the web';

  @override
  String get disableWebSearch => 'Disable Web Search';

  @override
  String get enableWebSearch => 'Enable Web Search';

  @override
  String get switchBackToAiChatMode => 'Switch back to AI chat mode';

  @override
  String get searchWebForCurrentInfo => 'Search the web for current information';

  @override
  String get pickImageFromGallery => 'Pick Image from Gallery';

  @override
  String get uploadImageForAiAnalysis => 'Upload an image for AI analysis';

  @override
  String get yourMessage => 'Your Message';

  @override
  String get wanderlyAi => 'Wanderly AI';

  @override
  String get webSearch => 'Web Search:';

  @override
  String get like => 'Like';

  @override
  String get dislike => 'Dislike';

  @override
  String get copy => 'Copy';

  @override
  String get regenerate => 'Regenerate';

  @override
  String get failedToSubmitFeedback => 'Failed to submit feedback. Please try again.';

  @override
  String get thankYouForFeedback => 'Thank you for your feedback! 🙏';

  @override
  String get feedbackReceivedThanks => 'Feedback received. Thank you for helping us improve! 🚀';

  @override
  String get responseCopiedToClipboard => 'Response copied to clipboard';

  @override
  String get wanderlyAiIsTyping => 'Wanderly AI is typing';

  @override
  String get stopGeneration => 'Stop Generation';

  @override
  String get youHaveChatsLeft => 'You have 10 chats left';

  @override
  String get enterSearchQuery => 'Enter your search query...';

  @override
  String get askMeAnythingOrLongPress => 'Ask me anything or long press to speak...';

  @override
  String failedToPickImage(String error) {
    return 'Failed to pick image: $error';
  }

  @override
  String get failedToAnalyzeImage => 'Failed to analyze image. Please try again.';

  @override
  String get responseGenerationStopped => 'Response generation was stopped.';

  @override
  String get unknownDestination => 'Unknown Destination';

  @override
  String get congratulations => 'Congratulations';

  @override
  String get itsAMatch => 'It\'s a match';

  @override
  String get travelVibesAligned => 'Travel vibes aligned pack\nyour bags, you\'ve got a match!';

  @override
  String get matchedPreferences => 'Matched Preferences:';

  @override
  String get addToItinerary => 'Add to Itinerary';

  @override
  String get keepSwiping => 'Keep swiping';

  @override
  String get versionInfo => 'Version Info';

  @override
  String get appVersion => 'App Version';

  @override
  String get buildNumber => 'Build Number';

  @override
  String get packageName => 'Package Name';

  @override
  String get appName => 'App Name';

  @override
  String get buildSignature => 'Build Signature';

  @override
  String get installerStore => 'Installer Store';

  @override
  String get deviceInfo => 'Device Information';

  @override
  String get operatingSystem => 'Operating System';

  @override
  String get flutterVersion => 'Flutter Version';

  @override
  String get dartVersion => 'Dart Version';

  @override
  String get leaveAReview => 'Leave a Review';

  @override
  String get rateOurApp => 'Rate Our App';

  @override
  String get enjoyingTripWiseGo => 'Enjoying TripWiseGo?';

  @override
  String get helpUsImproveByLeavingReview => 'Help us improve by leaving a review on the app store. Your feedback means a lot to us!';

  @override
  String get rateNow => 'Rate Now';

  @override
  String get maybeLater => 'Maybe Later';

  @override
  String get reviewFeatureNotAvailable => 'Review feature is only available in production builds';

  @override
  String get unableToOpenStore => 'Unable to open app store. Please try again later.';

  @override
  String get thankYouForReview => 'Thank you for taking the time to review our app!';
}
