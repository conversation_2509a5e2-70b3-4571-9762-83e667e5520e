/// Generated file. Do not edit.
///
/// This file contains the localized strings for the TripWiseGo app.
/// To add a new localized string, add it to the appropriate .arb file
/// in the lib/l10n directory and run `flutter gen-l10n`.


import 'app_localizations.dart';

/// The translations for Japanese (`ja`).
class AppLocalizationsJa extends AppLocalizations {
  AppLocalizationsJa([String locale = 'ja']) : super(locale);

  @override
  String get appTitle => 'TripWiseGo';

  @override
  String get welcome => 'ようこそ';

  @override
  String get guestUser => 'ゲストユーザー';

  @override
  String get readyForAdventure => '次の冒険の準備ができました';

  @override
  String get exploringAsGuest => 'ゲストとして世界を探索';

  @override
  String get editProfile => 'プロフィールを編集';

  @override
  String get saveChanges => '変更を保存';

  @override
  String get cancel => 'キャンセル';

  @override
  String get username => 'ユーザー名';

  @override
  String get email => 'メールアドレス';

  @override
  String get profileUpdatedSuccessfully => 'プロフィールが正常に更新されました！';

  @override
  String failedToUpdateProfile(String error) {
    return 'プロフィールの更新に失敗しました：$error';
  }

  @override
  String get profilePictureUpdatedSuccessfully => 'プロフィール画像が正常に更新されました！';

  @override
  String failedToUploadImage(String error) {
    return '画像のアップロードに失敗しました：$error';
  }

  @override
  String get profileEditingNotAvailableForGuests => 'ゲストユーザーはプロフィールを編集できません';

  @override
  String get profilePictureEditingNotAvailableForGuests => 'ゲストユーザーはプロフィール画像を編集できません';

  @override
  String get usernameCannotBeEmpty => 'ユーザー名は空にできません';

  @override
  String get usernameMustBeBetween2And30Characters => 'ユーザー名は2文字から30文字の間である必要があります';

  @override
  String get plan => 'プラン';

  @override
  String get termsOfService => '利用規約';

  @override
  String get language => '言語';

  @override
  String get privacyPolicy => 'プライバシーポリシー';

  @override
  String get support => 'サポート';

  @override
  String get helpCenter => 'ヘルプセンター';

  @override
  String get contactUs => 'お問い合わせ';

  @override
  String get helpSupport => 'Help & Support';

  @override
  String get howCanWeHelpYou => 'How can we help you?';

  @override
  String get helpSupportGreeting => 'Hi! I\'m here to help you with TripwiseGO. You can ask me about app features, troubleshooting, or report any issues you\'re experiencing.';

  @override
  String get helpSupportWelcome => 'Welcome to TripwiseGO Support! Here are some things I can help you with:';

  @override
  String get helpSupportFeatures => '• App features and how to use them\n• Navigation and account help\n• Troubleshooting common issues\n• Reporting bugs or problems';

  @override
  String get helpSupportAskQuestion => 'Feel free to ask me anything or describe any issues you\'re having!';

  @override
  String get reportIssue => 'Report Issue';

  @override
  String get reportBug => 'Report Bug';

  @override
  String get reportProblem => 'Report Problem';

  @override
  String get issueCategory => 'Issue Category';

  @override
  String get bugReport => 'Bug Report';

  @override
  String get featureRequest => 'Feature Request';

  @override
  String get generalFeedback => 'General Feedback';

  @override
  String get accountIssue => 'Account Issue';

  @override
  String get technicalProblem => 'Technical Problem';

  @override
  String get yourName => 'Your Name';

  @override
  String get yourEmail => 'Your Email';

  @override
  String get issueDescription => 'Issue Description';

  @override
  String get describeIssueDetail => 'Please describe the issue in detail';

  @override
  String get optionalScreenshot => 'Screenshot (Optional)';

  @override
  String get submitReport => 'Submit Report';

  @override
  String get reportSubmitted => 'Report Submitted';

  @override
  String get reportSubmittedSuccess => 'Thank you! Your report has been submitted successfully. We\'ll look into it and get back to you if needed.';

  @override
  String get reportSubmissionFailed => 'Failed to submit report. Please try again later.';

  @override
  String get nameRequired => 'Name is required';

  @override
  String get emailRequired => 'Email is required';

  @override
  String get validEmailRequired => 'Please enter a valid email address';

  @override
  String get descriptionRequired => 'Issue description is required';

  @override
  String get selectCategory => 'Please select a category';

  @override
  String get subscription => 'Subscription';

  @override
  String get subscriptionActive => 'Subscription Active!';

  @override
  String get welcomeToPremium => 'Welcome to TripwiseGO Premium! Enjoy unlimited access to all features.';

  @override
  String get currentPlan => 'Current Plan';

  @override
  String get trial => 'Trial';

  @override
  String get active => 'Active';

  @override
  String trialEndsIn(int days) {
    return 'Trial ends in $days days';
  }

  @override
  String renewsIn(int days) {
    return 'Renews in $days days';
  }

  @override
  String get yourBenefits => 'Your Benefits';

  @override
  String get startPlanningTrip => 'Start Planning Your Trip';

  @override
  String get manageSubscription => 'Manage Subscription';

  @override
  String get basic => 'Basic';

  @override
  String get premium => 'Premium';

  @override
  String get basicPlaceRecommendations => 'Basic Place Recommendations';

  @override
  String get locationRecommendationSwiping => 'Location Recommendation Swiping (20 swipes/day)';

  @override
  String get aiPoweredTravelPlanner => 'AI-Powered Travel Planner';

  @override
  String get unlimitedQuizzes => 'Unlimited Quizzes & Fun Facts';

  @override
  String get noAds => 'No-Ads';

  @override
  String get oneMonth => '1 Month';

  @override
  String get threeMonths => '3 Months';

  @override
  String get fiveMonths => '5 Months';

  @override
  String dayFreeTrial(int days) {
    return '$days-day free trial';
  }

  @override
  String get billedMonthly => 'Billed monthly';

  @override
  String get billedTwiceAnnually => 'Billed twice annually';

  @override
  String get billedAnnually => 'Billed annually';

  @override
  String get save45Percent => 'SAVE 45%';

  @override
  String get continueButton => 'Continue';

  @override
  String get termsAndConditions => 'Terms and Conditions';

  @override
  String get failedToSubscribe => 'Failed to subscribe. Please try again.';

  @override
  String get signOut => 'サインアウト';

  @override
  String get selectLanguage => '言語を選択';

  @override
  String get chooseYourPreferredLanguage => 'お好みの言語を選択してください';

  @override
  String get languageUpdatedSuccessfully => '言語が正常に更新されました！';

  @override
  String get home => 'ホーム';

  @override
  String get match => 'マッチ';

  @override
  String get chat => 'チャット';

  @override
  String get profile => 'プロフィール';

  @override
  String get loading => '読み込み中...';

  @override
  String get error => 'エラー';

  @override
  String get retry => '再試行';

  @override
  String get ok => 'OK';

  @override
  String get yes => 'はい';

  @override
  String get no => 'いいえ';

  @override
  String get save => '保存';

  @override
  String get delete => '削除';

  @override
  String get edit => '編集';

  @override
  String get add => '追加';

  @override
  String get remove => '削除';

  @override
  String get close => '閉じる';

  @override
  String get back => '戻る';

  @override
  String get next => '次へ';

  @override
  String get previous => '前へ';

  @override
  String get done => '完了';

  @override
  String get search => '検索';

  @override
  String get noResultsFound => '結果が見つかりません';

  @override
  String get tryAgain => 'もう一度試す';

  @override
  String get somethingWentWrong => '何かが間違っています';

  @override
  String get networkError => 'ネットワークエラー。接続を確認してください。';

  @override
  String get serverError => 'サーバーエラー。後でもう一度お試しください。';

  @override
  String get invalidInput => '無効な入力';

  @override
  String get required => '必須';

  @override
  String get optional => 'オプション';

  @override
  String get itinerary => 'Itinerary';

  @override
  String get yourItineraries => 'Your Itineraries';

  @override
  String get startPlanningYourTrip => 'Start Planning Your Trip';

  @override
  String get tripPlanningFeaturesWillAppearHere => 'Your trip planning features will appear here. The persistent authentication is now working!';

  @override
  String get forYou => 'For You';

  @override
  String get liked => 'Liked';

  @override
  String get collab => 'Collab';

  @override
  String get collaborativeItinerary => 'Collaborative';

  @override
  String get endSession => 'End Session';

  @override
  String get logout => 'Logout';

  @override
  String logoutFailed(String error) {
    return 'Logout failed: $error';
  }

  @override
  String get noItineraryFound => '旅程が見つかりません';

  @override
  String get askAiToCreateTravelPlan => 'AIに旅行プランを作成してもらいましょう！';

  @override
  String get saturday => '土曜日';

  @override
  String get tuesday => '火曜日';

  @override
  String dayNumber(int number) {
    return '$number日目';
  }

  @override
  String get itineraryOverview => '旅程の概要';

  @override
  String daysAndNights(int days, int nights) {
    return '$days日$nights泊';
  }

  @override
  String get hiImWanderlyAi => 'こんにちは、私はWanderly AI 🌏です';

  @override
  String get yourTravelAiAssistant => 'あなたの旅行AIアシスタントです。今日はどのようにお手伝いできますか？';

  @override
  String get useThisBubbleChat => 'このバブルチャットを使用してください';

  @override
  String get aiAssistant => 'AIアシスタント';

  @override
  String get chatHistory => 'チャット履歴';

  @override
  String get newChat => '新しいチャット';

  @override
  String get addImage => '画像を追加';

  @override
  String get camera => 'カメラ';

  @override
  String get gallery => 'ギャラリー';

  @override
  String get microphonePermissionRequired => '音声入力にはマイクの許可が必要です';

  @override
  String get speechRecognitionNotAvailable => 'このデバイスでは音声認識が利用できません';

  @override
  String get listening => '聞いています...';

  @override
  String get deleteChat => 'チャットを削除';

  @override
  String get deleteChatConfirmation => 'このチャットを削除してもよろしいですか？この操作は元に戻せません。';

  @override
  String get chatDeletedSuccessfully => 'チャットが正常に削除されました';

  @override
  String get pleaseEnterSearchQuery => '検索クエリを入力してください';

  @override
  String get dailySearchLimitReached => '1日の検索制限に達しました。1日に5回検索できます。';

  @override
  String get searchingTheWeb => 'ウェブを検索中...';

  @override
  String get webSearchModeActive => 'ウェブ検索モードがアクティブです';

  @override
  String get pleaseWaitWhileSearching => '情報を検索していますのでお待ちください';

  @override
  String get yourNextMessageWillSearch => '次のメッセージでウェブを検索します';

  @override
  String get disableWebSearch => 'ウェブ検索を無効にする';

  @override
  String get enableWebSearch => 'ウェブ検索を有効にする';

  @override
  String get switchBackToAiChatMode => 'AIチャットモードに戻る';

  @override
  String get searchWebForCurrentInfo => '最新情報をウェブで検索';

  @override
  String get pickImageFromGallery => 'ギャラリーから画像を選択';

  @override
  String get uploadImageForAiAnalysis => 'AI分析用に画像をアップロード';

  @override
  String get yourMessage => 'あなたのメッセージ';

  @override
  String get wanderlyAi => 'Wanderly AI';

  @override
  String get webSearch => 'ウェブ検索：';

  @override
  String get like => 'いいね';

  @override
  String get dislike => 'よくない';

  @override
  String get copy => 'コピー';

  @override
  String get regenerate => '再生成';

  @override
  String get failedToSubmitFeedback => 'フィードバックの送信に失敗しました。もう一度お試しください。';

  @override
  String get thankYouForFeedback => 'フィードバックをありがとうございます！🙏';

  @override
  String get feedbackReceivedThanks => 'フィードバックを受け取りました。改善にご協力いただきありがとうございます！🚀';

  @override
  String get responseCopiedToClipboard => '回答がクリップボードにコピーされました';

  @override
  String get wanderlyAiIsTyping => 'Wanderly AIが入力中';

  @override
  String get stopGeneration => '生成を停止';

  @override
  String get youHaveChatsLeft => '残り10回のチャットがあります';

  @override
  String get enterSearchQuery => '検索クエリを入力...';

  @override
  String get askMeAnythingOrLongPress => '何でも聞いてください。長押しで音声入力...';

  @override
  String failedToPickImage(String error) {
    return '画像の選択に失敗しました：$error';
  }

  @override
  String get failedToAnalyzeImage => '画像の分析に失敗しました。もう一度お試しください。';

  @override
  String get responseGenerationStopped => '回答の生成が停止されました。';

  @override
  String get unknownDestination => '不明な目的地';

  @override
  String get congratulations => 'Congratulations';

  @override
  String get itsAMatch => 'It\'s a match';

  @override
  String get travelVibesAligned => 'Travel vibes aligned pack\nyour bags, you\'ve got a match!';

  @override
  String get matchedPreferences => 'Matched Preferences:';

  @override
  String get addToItinerary => 'Add to Itinerary';

  @override
  String get keepSwiping => 'Keep swiping';

  @override
  String get versionInfo => 'Version Info';

  @override
  String get appVersion => 'App Version';

  @override
  String get buildNumber => 'Build Number';

  @override
  String get packageName => 'Package Name';

  @override
  String get appName => 'App Name';

  @override
  String get buildSignature => 'Build Signature';

  @override
  String get installerStore => 'Installer Store';

  @override
  String get deviceInfo => 'Device Information';

  @override
  String get operatingSystem => 'Operating System';

  @override
  String get flutterVersion => 'Flutter Version';

  @override
  String get dartVersion => 'Dart Version';

  @override
  String get leaveAReview => 'Leave a Review';

  @override
  String get rateOurApp => 'Rate Our App';

  @override
  String get enjoyingTripWiseGo => 'Enjoying TripWiseGo?';

  @override
  String get helpUsImproveByLeavingReview => 'Help us improve by leaving a review on the app store. Your feedback means a lot to us!';

  @override
  String get rateNow => 'Rate Now';

  @override
  String get maybeLater => 'Maybe Later';

  @override
  String get reviewFeatureNotAvailable => 'Review feature is only available in production builds';

  @override
  String get unableToOpenStore => 'Unable to open app store. Please try again later.';

  @override
  String get thankYouForReview => 'Thank you for taking the time to review our app!';
}
