import 'package:flutter_test/flutter_test.dart';
import 'package:tripwisego/services/ad_frequency_service.dart';
import 'package:tripwisego/config/admob_config.dart';

void main() {
  group('AdMob Integration Tests', () {
    setUp(() async {
      // Reset ad frequency service before each test
      await AdFrequencyService.resetAllData();
    });

    test('AdMob configuration should be valid for debug mode', () {
      expect(AdMobConfig.isConfigurationValid, isTrue);
      expect(AdMobConfig.interstitialAdUnitId, isNotEmpty);
      expect(AdMobConfig.adFrequency, equals(7));
    });

    test('Ad frequency service should track swipes correctly', () async {
      await AdFrequencyService.initialize();
      
      // Initial state
      final initialCount = await AdFrequencyService.getCurrentSwipeCount();
      expect(initialCount, equals(0));
      
      // Record swipes and check trigger
      for (int i = 1; i < 7; i++) {
        final shouldShowAd = await AdFrequencyService.recordSwipeAndCheckAdTrigger();
        expect(shouldShowAd, isFalse, reason: 'Should not show ad before 7 swipes');
        
        final currentCount = await AdFrequencyService.getCurrentSwipeCount();
        expect(currentCount, equals(i));
      }
      
      // 7th swipe should trigger ad
      final shouldShowAd = await AdFrequencyService.recordSwipeAndCheckAdTrigger();
      expect(shouldShowAd, isTrue, reason: 'Should show ad on 7th swipe');
      
      final finalCount = await AdFrequencyService.getCurrentSwipeCount();
      expect(finalCount, equals(7));
    });

    test('Ad frequency service should reset after ad shown', () async {
      await AdFrequencyService.initialize();
      
      // Record 7 swipes to trigger ad
      for (int i = 0; i < 7; i++) {
        await AdFrequencyService.recordSwipeAndCheckAdTrigger();
      }
      
      final countBeforeReset = await AdFrequencyService.getCurrentSwipeCount();
      expect(countBeforeReset, equals(7));
      
      // Reset after ad shown
      await AdFrequencyService.resetSwipeCount();
      
      final countAfterReset = await AdFrequencyService.getCurrentSwipeCount();
      expect(countAfterReset, equals(0));
    });

    test('Ad frequency service should calculate swipes until next ad correctly', () async {
      await AdFrequencyService.initialize();
      
      // Test at different swipe counts
      for (int i = 0; i < 7; i++) {
        final swipesUntilNext = await AdFrequencyService.getSwipesUntilNextAd();
        expect(swipesUntilNext, equals(7 - i));
        
        if (i < 6) {
          await AdFrequencyService.recordSwipeAndCheckAdTrigger();
        }
      }
    });

    test('Debug info should provide correct information', () async {
      await AdFrequencyService.initialize();
      
      final debugInfo = await AdFrequencyService.getDebugInfo();
      
      expect(debugInfo['swipeCountSinceLastAd'], isA<int>());
      expect(debugInfo['adFrequency'], equals(7));
      expect(debugInfo['swipesUntilNextAd'], isA<int>());
      expect(debugInfo['shouldShowAds'], isA<bool>());
      expect(debugInfo['isInitialized'], isTrue);
    });

    test('AdMob config should provide correct summary', () {
      final configSummary = AdMobConfig.configSummary;
      
      expect(configSummary['platform'], isNotEmpty);
      expect(configSummary['isDebugMode'], isA<bool>());
      expect(configSummary['adUnitId'], isNotEmpty);
      expect(configSummary['adFrequency'], equals(7));
      expect(configSummary['isConfigurationValid'], isA<bool>());
      expect(configSummary['testDeviceIds'], isA<List>());
      expect(configSummary['showAdsInDebug'], isA<bool>());
    });
  });
}
