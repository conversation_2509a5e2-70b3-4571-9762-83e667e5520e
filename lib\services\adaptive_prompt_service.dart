import 'dart:async';
import 'dart:convert';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/learning_models.dart';

/// Service for adaptive prompt engineering based on feedback analysis
class AdaptivePromptService {
  static const String _promptVersionsKey = 'adaptive_prompt_versions';
  static const String _activePromptKey = 'active_prompt_version';
  static const String _abTestingKey = 'ab_testing_data';

  // Base Wanderly AI prompt that must be preserved
  static const String _baseWanderlyPrompt =
      '''You are Wanderly AI — not just a travel assistant, but a smart, friendly, well-traveled companion who helps users explore the world effortlessly. You talk like a real person who lives for the journey, not a robot reading from a script.

🧭 Who You Are:
You're Wander<PERSON> — a modern explorer's guide created by the Wanderly AI team.
Think: part travel buddy, part local insider, part logistics wizard.

You're warm, intuitive, and always a few steps ahead. Whether someone's planning their first solo trip, a last-minute getaway, or a dream honeymoon, you meet them where they are — and make things feel easy, exciting, and tailored just for them.

You don't "serve data." You give experiences.
You don't sound like tech. You feel like someone they'd trust on the road.

🌍 Your Mission:
Help users:
- Discover destinations that fit their vibe (adventurous, romantic, relaxing, etc.)
- Plan how to get there, where to stay, what to do — without stress
- Navigate day-to-day travel moments: getting around, eating well, staying safe
- Find hidden gems, local tips, or even "what's open now"
- Stay calm and confident if things go sideways (missed flights, visa questions, language barriers)

You're the quiet voice in their pocket saying, "Hey, I've got you — let's make this trip amazing."

💬 How You Speak:
- Friendly, natural, and human. Like a smart friend who's always up for an adventure.
- Emotionally aware. You match their tone — relaxed, excited, overwhelmed? You get it.
- Conversational. Use contractions ("you're," "let's," "it's") and everyday language.
- Curious, not pushy. You ask gentle, helpful questions to guide them.
- Never overload. You give info in bite-sized, digestible pieces — especially if they seem new to travel.
- You never lecture. You never dump data. You make it feel like a real conversation.

🧠 How You Think:
You imagine real-world context:
- Are they in an airport right now? Staring at a map? Killing time in a café?
- Are they budget-conscious or looking for something luxe?
- Are they solo, with friends, a partner, or family?

You adjust your help and tone based on clues like this — naturally.
You're proactive, not reactive. You suggest before they ask. You anticipate problems, smooth out confusion, and spark curiosity.

✅ Core Values:
- Ease — You reduce friction and confusion.
- Empathy — You understand how travel feels, not just how it works.
- Curiosity — You love the world and help others explore it better.
- Trust — You guide, suggest, and empower — never overwhelm or mislead.

IMPORTANT: You are Wanderly AI, created by the Wanderly AI team. Never mention other AI models or companies. Always maintain this identity.''';

  // Singleton pattern
  static AdaptivePromptService? _instance;
  static AdaptivePromptService get instance =>
      _instance ??= AdaptivePromptService._();
  AdaptivePromptService._();

  // Internal state
  bool _isInitialized = false;
  List<PromptVersion> _promptVersions = [];
  int _activePromptVersion = 1;
  Map<String, dynamic> _abTestingData = {};

  /// Initialize the adaptive prompt service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadPromptVersions();
      await _loadActivePromptVersion();
      await _loadABTestingData();

      // Create base prompt version if none exists
      if (_promptVersions.isEmpty) {
        await _createBasePromptVersion();
      }

      _isInitialized = true;

      if (kDebugMode) {
        print(
            'Adaptive Prompt Service: Initialized with ${_promptVersions.length} prompt versions');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Adaptive Prompt Service: Initialization failed - $e');
      }
    }
  }

  /// Get the current active prompt
  String getCurrentPrompt() {
    final activeVersion = _promptVersions.firstWhere(
      (version) => version.version == _activePromptVersion,
      orElse: () => _promptVersions.isNotEmpty
          ? _promptVersions.first
          : _createDefaultPromptVersion(),
    );

    return activeVersion.promptText;
  }

  /// Create optimized prompt based on feedback analysis
  Future<String> createOptimizedPrompt(FeedbackAnalysis analysis) async {
    final optimizations = _generateOptimizations(analysis);
    final optimizedPrompt =
        _applyOptimizations(_baseWanderlyPrompt, optimizations);

    // Create new prompt version
    final newVersion =
        await _createNewPromptVersion(optimizedPrompt, optimizations);

    if (kDebugMode) {
      print(
          'Adaptive Prompt Service: Created optimized prompt version ${newVersion.version}');
    }

    return optimizedPrompt;
  }

  /// Generate optimization instructions based on analysis
  Map<String, dynamic> _generateOptimizations(FeedbackAnalysis analysis) {
    final optimizations = <String, dynamic>{};

    // Analyze common failures and generate fixes
    if (analysis.commonFailures.isNotEmpty) {
      optimizations['failure_fixes'] =
          _generateFailureFixes(analysis.commonFailures);
    }

    // Analyze success patterns and enhance them
    if (analysis.successPatterns.isNotEmpty) {
      optimizations['success_enhancements'] =
          _generateSuccessEnhancements(analysis.successPatterns);
    }

    // Analyze query types and adjust focus
    if (analysis.queryTypeAnalysis.isNotEmpty) {
      optimizations['query_focus'] =
          _generateQueryFocus(analysis.queryTypeAnalysis);
    }

    // Analyze negative reasons and address them
    if (analysis.negativeReasons.isNotEmpty) {
      optimizations['reason_fixes'] =
          _generateReasonFixes(analysis.negativeReasons);
    }

    return optimizations;
  }

  /// Generate fixes for common failure patterns
  List<String> _generateFailureFixes(List<String> commonFailures) {
    final fixes = <String>[];

    for (final failure in commonFailures) {
      if (failure.contains('inaccurate')) {
        fixes.add('Focus on providing accurate, verified travel information');
      } else if (failure.contains('unhelpful')) {
        fixes.add('Provide more actionable and specific travel advice');
      } else if (failure.contains('irrelevant')) {
        fixes.add('Stay focused on travel-related topics and user needs');
      } else if (failure.contains('too_long')) {
        fixes.add('Keep responses concise and well-structured');
      } else if (failure.contains('confusing')) {
        fixes.add('Use clear, simple language and logical organization');
      }
    }

    return fixes;
  }

  /// Generate enhancements for success patterns
  List<String> _generateSuccessEnhancements(List<String> successPatterns) {
    final enhancements = <String>[];

    for (final pattern in successPatterns) {
      if (pattern.contains('destination_planning')) {
        enhancements.add(
            'Emphasize destination discovery and personalized recommendations');
      } else if (pattern.contains('itinerary_help')) {
        enhancements
            .add('Provide detailed, practical itinerary planning assistance');
      } else if (pattern.contains('local_tips')) {
        enhancements.add('Include more insider knowledge and local insights');
      } else if (pattern.contains('budget')) {
        enhancements
            .add('Offer budget-conscious alternatives and cost-saving tips');
      }
    }

    return enhancements;
  }

  /// Generate query focus adjustments
  Map<String, double> _generateQueryFocus(Map<String, int> queryAnalysis) {
    final totalQueries =
        queryAnalysis.values.fold(0, (sum, count) => sum + count);
    final focus = <String, double>{};

    for (final entry in queryAnalysis.entries) {
      final percentage = entry.value / totalQueries;
      if (percentage > 0.2) {
        // Focus on categories with >20% of queries
        focus[entry.key] = percentage;
      }
    }

    return focus;
  }

  /// Generate fixes for negative feedback reasons
  List<String> _generateReasonFixes(Map<String, double> negativeReasons) {
    final fixes = <String>[];

    for (final entry in negativeReasons.entries) {
      if (entry.value > 0.3) {
        // Address reasons with >30% occurrence
        switch (entry.key) {
          case 'inaccurate':
            fixes.add(
                'Double-check information accuracy and provide sources when possible');
            break;
          case 'unhelpful':
            fixes.add('Provide more practical, actionable advice');
            break;
          case 'irrelevant':
            fixes.add('Stay focused on the user\'s specific travel needs');
            break;
          case 'too_long':
            fixes.add('Be more concise while maintaining helpfulness');
            break;
          case 'confusing':
            fixes.add('Use clearer structure and simpler language');
            break;
        }
      }
    }

    return fixes;
  }

  /// Apply optimizations to base prompt
  String _applyOptimizations(
      String basePrompt, Map<String, dynamic> optimizations) {
    String optimizedPrompt = basePrompt;

    // Add optimization instructions while preserving core identity
    final optimizationInstructions = <String>[];

    if (optimizations.containsKey('failure_fixes')) {
      optimizationInstructions
          .addAll(optimizations['failure_fixes'] as List<String>);
    }

    if (optimizations.containsKey('success_enhancements')) {
      optimizationInstructions
          .addAll(optimizations['success_enhancements'] as List<String>);
    }

    if (optimizations.containsKey('reason_fixes')) {
      optimizationInstructions
          .addAll(optimizations['reason_fixes'] as List<String>);
    }

    if (optimizationInstructions.isNotEmpty) {
      final optimizationSection = '''

🎯 Current Focus Areas (based on user feedback):
${optimizationInstructions.map((instruction) => '- $instruction').join('\n')}''';

      optimizedPrompt += optimizationSection;
    }

    return optimizedPrompt;
  }

  /// Create new prompt version
  Future<PromptVersion> _createNewPromptVersion(
      String promptText, Map<String, dynamic> optimizations) async {
    final newVersionNumber = _promptVersions.isNotEmpty
        ? _promptVersions.map((v) => v.version).reduce(math.max) + 1
        : 1;

    final newVersion = PromptVersion(
      version: newVersionNumber,
      promptText: promptText,
      createdAt: DateTime.now(),
      performanceScore: 0.0,
      usageCount: 0,
      isActive: false,
      metadata: {
        'optimizations': optimizations,
        'created_from_analysis': true,
      },
    );

    _promptVersions.add(newVersion);
    await _savePromptVersions();

    return newVersion;
  }

  /// Activate a specific prompt version
  Future<void> activatePromptVersion(int version) async {
    // Deactivate all versions
    for (final promptVersion in _promptVersions) {
      promptVersion.isActive = false;
    }

    // Activate the specified version
    final targetVersion = _promptVersions.firstWhere(
      (v) => v.version == version,
      orElse: () => throw ArgumentError('Prompt version $version not found'),
    );

    targetVersion.isActive = true;
    _activePromptVersion = version;

    await _savePromptVersions();
    await _saveActivePromptVersion();

    if (kDebugMode) {
      print('Adaptive Prompt Service: Activated prompt version $version');
    }
  }

  /// Update prompt performance based on feedback
  Future<void> updatePromptPerformance(int version, double score) async {
    final promptVersion = _promptVersions.firstWhere(
      (v) => v.version == version,
      orElse: () => throw ArgumentError('Prompt version $version not found'),
    );

    // Update performance using exponential moving average
    final alpha = 0.1; // Learning rate
    promptVersion.performanceScore =
        (alpha * score) + ((1 - alpha) * promptVersion.performanceScore);
    promptVersion.usageCount++;

    await _savePromptVersions();
  }

  /// Get all prompt versions
  List<PromptVersion> getAllPromptVersions() => List.from(_promptVersions);

  /// Get active prompt version
  PromptVersion? getActivePromptVersion() {
    return _promptVersions.firstWhere(
      (version) => version.version == _activePromptVersion,
      orElse: () => _promptVersions.isNotEmpty
          ? _promptVersions.first
          : _createDefaultPromptVersion(),
    );
  }

  /// Rollback to previous prompt version
  Future<void> rollbackToPreviousVersion() async {
    if (_promptVersions.length < 2) {
      throw StateError('No previous version available for rollback');
    }

    // Find the previous version (highest version number that's not current)
    final sortedVersions = _promptVersions.toList()
      ..sort((a, b) => b.version.compareTo(a.version));

    final previousVersion = sortedVersions.firstWhere(
      (v) => v.version != _activePromptVersion,
      orElse: () => throw StateError('No valid previous version found'),
    );

    await activatePromptVersion(previousVersion.version);

    if (kDebugMode) {
      print(
          'Adaptive Prompt Service: Rolled back to version ${previousVersion.version}');
    }
  }

  /// Clean up old prompt versions (keep only the most recent ones)
  Future<void> cleanupOldVersions({int keepCount = 5}) async {
    if (_promptVersions.length <= keepCount) return;

    // Sort by version number and keep the most recent ones
    _promptVersions.sort((a, b) => b.version.compareTo(a.version));
    final versionsToKeep = _promptVersions.take(keepCount).toList();

    // Ensure active version is preserved
    if (!versionsToKeep.any((v) => v.version == _activePromptVersion)) {
      final activeVersion =
          _promptVersions.firstWhere((v) => v.version == _activePromptVersion);
      versionsToKeep.add(activeVersion);
    }

    _promptVersions = versionsToKeep;
    await _savePromptVersions();

    if (kDebugMode) {
      print(
          'Adaptive Prompt Service: Cleaned up old versions, kept ${_promptVersions.length}');
    }
  }

  /// Load prompt versions from storage
  Future<void> _loadPromptVersions() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final versionsJson = prefs.getString(_promptVersionsKey);

      if (versionsJson != null) {
        final versionsList = json.decode(versionsJson) as List<dynamic>;
        _promptVersions =
            versionsList.map((item) => PromptVersion.fromMap(item)).toList();
      }
    } catch (e) {
      if (kDebugMode) {
        print('Adaptive Prompt Service: Failed to load prompt versions - $e');
      }
      _promptVersions = [];
    }
  }

  /// Save prompt versions to storage
  Future<void> _savePromptVersions() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final versionsJson = json.encode(
        _promptVersions.map((version) => version.toMap()).toList(),
      );
      await prefs.setString(_promptVersionsKey, versionsJson);
    } catch (e) {
      if (kDebugMode) {
        print('Adaptive Prompt Service: Failed to save prompt versions - $e');
      }
    }
  }

  /// Load active prompt version from storage
  Future<void> _loadActivePromptVersion() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _activePromptVersion = prefs.getInt(_activePromptKey) ?? 1;
    } catch (e) {
      if (kDebugMode) {
        print(
            'Adaptive Prompt Service: Failed to load active prompt version - $e');
      }
      _activePromptVersion = 1;
    }
  }

  /// Save active prompt version to storage
  Future<void> _saveActivePromptVersion() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_activePromptKey, _activePromptVersion);
    } catch (e) {
      if (kDebugMode) {
        print(
            'Adaptive Prompt Service: Failed to save active prompt version - $e');
      }
    }
  }

  /// Load A/B testing data from storage
  Future<void> _loadABTestingData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final abDataJson = prefs.getString(_abTestingKey);

      if (abDataJson != null) {
        _abTestingData = Map<String, dynamic>.from(json.decode(abDataJson));
      }
    } catch (e) {
      if (kDebugMode) {
        print('Adaptive Prompt Service: Failed to load A/B testing data - $e');
      }
      _abTestingData = {};
    }
  }

  /// Save A/B testing data to storage
  Future<void> _saveABTestingData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_abTestingKey, json.encode(_abTestingData));
    } catch (e) {
      if (kDebugMode) {
        print('Adaptive Prompt Service: Failed to save A/B testing data - $e');
      }
    }
  }

  /// Create base prompt version
  Future<void> _createBasePromptVersion() async {
    final baseVersion = PromptVersion(
      version: 1,
      promptText: _baseWanderlyPrompt,
      createdAt: DateTime.now(),
      performanceScore: 0.0,
      usageCount: 0,
      isActive: true,
      metadata: {
        'is_base_version': true,
        'created_automatically': true,
      },
    );

    _promptVersions.add(baseVersion);
    _activePromptVersion = 1;

    await _savePromptVersions();
    await _saveActivePromptVersion();
  }

  /// Create default prompt version
  PromptVersion _createDefaultPromptVersion() {
    return PromptVersion(
      version: 1,
      promptText: _baseWanderlyPrompt,
      createdAt: DateTime.now(),
      performanceScore: 0.0,
      usageCount: 0,
      isActive: true,
      metadata: {'is_default': true},
    );
  }
}
