import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../services/chat_limit_service.dart';

/// Global notifier for chat counter updates
class ChatCounterNotifier {
  static final ValueNotifier<int> _updateNotifier = ValueNotifier<int>(0);

  static ValueNotifier<int> get updateNotifier => _updateNotifier;

  static void notifyUpdate() {
    _updateNotifier.value++;
  }
}

/// Widget that displays the remaining chat count for the day
class ChatCounterWidget extends StatefulWidget {
  final bool showOnlyWhenLimited;
  final EdgeInsets? padding;

  const ChatCounterWidget({
    super.key,
    this.showOnlyWhenLimited = false,
    this.padding,
  });

  @override
  State<ChatCounterWidget> createState() => _ChatCounterWidgetState();

  /// Static method to refresh all chat counter widgets
  static void refreshAll() {
    ChatCounterNotifier.notifyUpdate();
  }
}

class _ChatCounterWidgetState extends State<ChatCounterWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  Map<String, int>? _chatStats;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, -0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    // Listen to chat counter updates
    ChatCounterNotifier.updateNotifier.addListener(_onCounterUpdate);

    _loadChatStats();
  }

  @override
  void dispose() {
    ChatCounterNotifier.updateNotifier.removeListener(_onCounterUpdate);
    _animationController.dispose();
    super.dispose();
  }

  void _onCounterUpdate() {
    _loadChatStats();
  }

  Future<void> _loadChatStats() async {
    try {
      final stats = await ChatLimitService.getChatUsageStats();
      if (mounted) {
        setState(() {
          _chatStats = stats;
          _isLoading = false;
        });
        _animationController.forward();
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Refresh the chat stats (call this after sending a message)
  Future<void> refresh() async {
    await _loadChatStats();
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading || _chatStats == null) {
      return const SizedBox.shrink();
    }

    final used = _chatStats!['used']!;
    final limit = _chatStats!['limit']!;
    final remaining = _chatStats!['remaining']!;

    // If showOnlyWhenLimited is true, only show when approaching limit or premium
    if (widget.showOnlyWhenLimited) {
      // Show if premium user (unlimited chats)
      if (limit >= 1000) {
        // Always show for premium users
      }
      // Show if approaching limit (≤ 10 remaining)
      else if (remaining <= 10) {
        // Show when getting close to limit
      }
      // Hide if plenty of chats remaining
      else {
        return const SizedBox.shrink();
      }
    }

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: Container(
              margin: widget.padding ??
                  const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
              padding: const EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 6,
              ),
              decoration: BoxDecoration(
                color: _getBackgroundColor(remaining, limit),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: _getBorderColor(remaining, limit),
                  width: 1,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    _getIcon(remaining, limit),
                    size: 16,
                    color: _getTextColor(remaining, limit),
                  ),
                  const SizedBox(width: 6),
                  Text(
                    _getDisplayText(remaining, used, limit),
                    style: GoogleFonts.instrumentSans(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: _getTextColor(remaining, limit),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  String _getDisplayText(int remaining, int used, int limit) {
    if (remaining == 0) {
      return 'Daily limit reached';
    } else if (remaining <= 5) {
      return '$remaining chats left today';
    } else if (limit >= 1000) {
      return 'Premium: Unlimited';
    } else {
      return '$remaining chats remaining';
    }
  }

  Color _getBackgroundColor(int remaining, int limit) {
    if (remaining == 0) {
      return Colors.red.shade50;
    } else if (remaining <= 5) {
      return Colors.orange.shade50;
    } else if (limit >= 1000) {
      return const Color(0xFF0D76FF).withOpacity(0.1);
    } else {
      return Colors.green.shade50;
    }
  }

  Color _getBorderColor(int remaining, int limit) {
    if (remaining == 0) {
      return Colors.red.shade200;
    } else if (remaining <= 5) {
      return Colors.orange.shade200;
    } else if (limit >= 1000) {
      return const Color(0xFF0D76FF).withOpacity(0.3);
    } else {
      return Colors.green.shade200;
    }
  }

  Color _getTextColor(int remaining, int limit) {
    if (remaining == 0) {
      return Colors.red.shade700;
    } else if (remaining <= 5) {
      return Colors.orange.shade700;
    } else if (limit >= 1000) {
      return const Color(0xFF0D76FF);
    } else {
      return Colors.green.shade700;
    }
  }

  IconData _getIcon(int remaining, int limit) {
    if (remaining == 0) {
      return Icons.block;
    } else if (remaining <= 5) {
      return Icons.warning_amber;
    } else if (limit >= 1000) {
      return Icons.star;
    } else {
      return Icons.chat_bubble_outline;
    }
  }
}

/// Compact version of the chat counter for smaller spaces
class CompactChatCounter extends StatefulWidget {
  const CompactChatCounter({super.key});

  @override
  State<CompactChatCounter> createState() => _CompactChatCounterState();
}

class _CompactChatCounterState extends State<CompactChatCounter> {
  Map<String, int>? _chatStats;

  @override
  void initState() {
    super.initState();
    _loadChatStats();
  }

  Future<void> _loadChatStats() async {
    try {
      final stats = await ChatLimitService.getChatUsageStats();
      if (mounted) {
        setState(() {
          _chatStats = stats;
        });
      }
    } catch (error) {
      // Handle error silently
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_chatStats == null) {
      return const SizedBox.shrink();
    }

    final remaining = _chatStats!['remaining']!;
    final limit = _chatStats!['limit']!;

    // Only show if approaching limit or premium
    if (limit < 1000 && remaining > 10) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: limit >= 1000
            ? const Color(0xFF0D76FF).withOpacity(0.1)
            : remaining <= 5
                ? Colors.orange.shade50
                : Colors.green.shade50,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            limit >= 1000 ? Icons.star : Icons.chat_bubble_outline,
            size: 12,
            color: limit >= 1000
                ? const Color(0xFF0D76FF)
                : remaining <= 5
                    ? Colors.orange.shade700
                    : Colors.green.shade700,
          ),
          const SizedBox(width: 4),
          Text(
            limit >= 1000 ? '∞' : '$remaining',
            style: GoogleFonts.instrumentSans(
              fontSize: 10,
              fontWeight: FontWeight.w600,
              color: limit >= 1000
                  ? const Color(0xFF0D76FF)
                  : remaining <= 5
                      ? Colors.orange.shade700
                      : Colors.green.shade700,
            ),
          ),
        ],
      ),
    );
  }
}
