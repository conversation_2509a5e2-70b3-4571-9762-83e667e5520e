import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'package:csv/csv.dart';
import '../models/feedback_models.dart';
import '../services/auth_service.dart';
import '../services/google_sheets_service.dart';

class FeedbackService {
  static const String _feedbackStorageKey = 'ai_feedback_storage';
  static const String _csvUrl =
      'https://docs.google.com/spreadsheets/d/e/2PACX-1vTVe-oLINSSBYuwfNP-vN8udMPm9p5yn98_GxMBvs99Tpd2t4GiAqF3eE4Hn2ERyNIDgrj_Hnw1_G7x/pub?gid=1288808570&single=true&output=csv';

  static List<AIResponseFeedback> _feedbackCache = [];
  static bool _isInitialized = false;

  /// Initialize the feedback service
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialize Google Sheets service first
      await GoogleSheetsService.initialize();

      // Load existing feedback from SharedPreferences
      await _loadFeedbackFromStorage();

      _isInitialized = true;

      if (kDebugMode) {
        print('Feedback Service: Initialized successfully');
        print('Google Sheets Service Ready: ${GoogleSheetsService.isReady}');
        print(
            'Feedback Service: Found ${_feedbackCache.length} existing backup feedback entries');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Feedback Service: Initialization failed: $e');
      }
      _isInitialized = true; // Set to true to prevent blocking
    }
  }

  /// Load feedback data from SharedPreferences
  static Future<void> _loadFeedbackFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final feedbackJson = prefs.getStringList(_feedbackStorageKey) ?? [];

      _feedbackCache = feedbackJson.map((jsonStr) {
        final Map<String, dynamic> data = jsonDecode(jsonStr);
        return AIResponseFeedback.fromJson(data);
      }).toList();

      if (kDebugMode) {
        print(
            'Feedback Service: Loaded ${_feedbackCache.length} feedback entries from storage');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Feedback Service: Failed to load feedback from storage: $e');
      }
      _feedbackCache = [];
    }
  }

  /// Save feedback data to SharedPreferences
  static Future<void> _saveFeedbackToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final feedbackJson = _feedbackCache
          .map((feedback) => jsonEncode(feedback.toJson()))
          .toList();
      await prefs.setStringList(_feedbackStorageKey, feedbackJson);

      if (kDebugMode) {
        print(
            'Feedback Service: Saved ${_feedbackCache.length} feedback entries to storage');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Feedback Service: Failed to save feedback to storage: $e');
      }
    }
  }

  /// Ensure service is initialized
  static Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  /// Submit feedback for an AI response
  static Future<bool> submitFeedback({
    required String userMessage,
    required String aiResponse,
    required FeedbackType feedbackType,
    FeedbackReason? feedbackReason,
    String? customFeedbackText,
    required List<String> conversationContext,
    required String sessionId,
    String? messageId,
  }) async {
    try {
      await _ensureInitialized();

      // Get current user ID
      final userId = AuthService.currentUser?.id ?? 'anonymous';

      // Create feedback object
      final feedback = AIResponseFeedback(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: userId,
        userMessage: userMessage,
        aiResponse: aiResponse,
        feedbackType: feedbackType,
        feedbackReason: feedbackReason,
        customFeedbackText: customFeedbackText,
        timestamp: DateTime.now(),
        conversationContext: conversationContext,
        sessionId: sessionId,
        messageId: messageId,
        isSynced:
            false, // Will be set to true after successful Google Sheets submission
      );

      // Try to submit directly to Google Sheets
      bool sheetsSuccess = false;
      if (GoogleSheetsService.isReady) {
        sheetsSuccess = await GoogleSheetsService.submitFeedback(feedback);
        if (sheetsSuccess) {
          feedback.isSynced = true;
          if (kDebugMode) {
            print(
                'Feedback Service: Feedback submitted to Google Sheets - ${feedback.id}');
          }
        }
      }

      // Store locally as backup (always store, regardless of Sheets success)
      _feedbackCache.add(feedback);
      await _saveFeedbackToStorage();
      if (kDebugMode) {
        print(
            'Feedback Service: Feedback stored locally as backup - ${feedback.id}');
      }

      // If Google Sheets failed, try to sync in background
      if (!sheetsSuccess) {
        if (kDebugMode) {
          print(
              'Feedback Service: Google Sheets submission failed, will retry in background');
        }
        _syncFeedbackInBackground();
      }

      return true; // Return true if at least local storage succeeded
    } catch (e) {
      if (kDebugMode) {
        print('Feedback Service: Failed to submit feedback - $e');
      }
      return false;
    }
  }

  /// Get all feedback entries
  static Future<List<AIResponseFeedback>> getAllFeedback() async {
    await _ensureInitialized();
    return List.from(_feedbackCache);
  }

  /// Get feedback for a specific message
  static Future<AIResponseFeedback?> getFeedbackForMessage(
      String messageId) async {
    await _ensureInitialized();

    try {
      return _feedbackCache
          .where((feedback) => feedback.messageId == messageId)
          .firstOrNull;
    } catch (e) {
      return null;
    }
  }

  /// Update existing feedback
  static Future<bool> updateFeedback(
    String feedbackId, {
    FeedbackType? feedbackType,
    FeedbackReason? feedbackReason,
    String? customFeedbackText,
  }) async {
    try {
      await _ensureInitialized();

      final feedbackIndex =
          _feedbackCache.indexWhere((f) => f.id == feedbackId);
      if (feedbackIndex == -1) return false;

      final feedback = _feedbackCache[feedbackIndex];

      // Update fields
      if (feedbackType != null) feedback.feedbackType = feedbackType;
      if (feedbackReason != null) feedback.feedbackReason = feedbackReason;
      if (customFeedbackText != null) {
        feedback.customFeedbackText = customFeedbackText;
      }

      // Mark as not synced since it was updated
      feedback.isSynced = false;

      // Save changes to storage
      await _saveFeedbackToStorage();

      if (kDebugMode) {
        print('Feedback Service: Feedback updated - $feedbackId');
      }

      // Try to sync
      _syncFeedbackInBackground();

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Feedback Service: Failed to update feedback - $e');
      }
      return false;
    }
  }

  /// Sync feedback to Google Sheets (background operation)
  static void _syncFeedbackInBackground() {
    // Run sync in background without blocking UI
    Future.delayed(Duration.zero, () async {
      try {
        await syncFeedbackToSheets();
      } catch (e) {
        if (kDebugMode) {
          print('Feedback Service: Background sync failed - $e');
        }
      }
    });
  }

  /// Sync unsynced feedback to Google Sheets
  static Future<bool> syncFeedbackToSheets() async {
    try {
      await _ensureInitialized();

      // Get unsynced feedback
      final unsyncedFeedback =
          _feedbackCache.where((feedback) => !feedback.isSynced).toList();

      if (unsyncedFeedback.isEmpty) {
        if (kDebugMode) {
          print('Feedback Service: No unsynced feedback to upload');
        }
        return true;
      }

      if (kDebugMode) {
        print(
            'Feedback Service: Syncing ${unsyncedFeedback.length} feedback entries');
      }

      // Check if Google Sheets service is ready
      if (!GoogleSheetsService.isReady) {
        if (kDebugMode) {
          print('Feedback Service: Google Sheets service not ready');
        }
        return false;
      }

      // Submit to Google Sheets
      bool success =
          await GoogleSheetsService.submitMultipleFeedback(unsyncedFeedback);

      if (success) {
        // Mark all as synced
        for (final feedback in unsyncedFeedback) {
          feedback.isSynced = true;
        }
        await _saveFeedbackToStorage();

        if (kDebugMode) {
          print(
              'Feedback Service: Successfully synced ${unsyncedFeedback.length} entries to Google Sheets');
        }
      } else {
        if (kDebugMode) {
          print('Feedback Service: Failed to sync to Google Sheets');
        }
      }

      return success;
    } catch (e) {
      if (kDebugMode) {
        print('Feedback Service: Sync failed - $e');
      }
      return false;
    }
  }

  /// Read feedback data from Google Sheets
  static Future<List<AIResponseFeedback>> readFeedbackFromSheets() async {
    try {
      // Use Google Sheets service if available
      if (GoogleSheetsService.isReady) {
        return await GoogleSheetsService.readAllFeedback();
      }

      // Fallback to CSV URL if Google Sheets service is not ready
      final response = await http.get(Uri.parse(_csvUrl));

      if (response.statusCode == 200) {
        final csvData = const CsvToListConverter().convert(response.body);

        // Skip header row
        final dataRows = csvData.skip(1);

        final feedbackList = <AIResponseFeedback>[];

        for (final row in dataRows) {
          try {
            if (row.length >= 12) {
              final feedback = AIResponseFeedback.fromCsvRow(
                row.map((e) => e.toString()).toList(),
              );
              feedbackList.add(feedback);
            }
          } catch (e) {
            if (kDebugMode) {
              print('Feedback Service: Failed to parse CSV row - $e');
            }
          }
        }

        if (kDebugMode) {
          print(
              'Feedback Service: Read ${feedbackList.length} feedback entries from sheets (CSV fallback)');
        }

        return feedbackList;
      } else {
        throw Exception('Failed to fetch CSV: ${response.statusCode}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Feedback Service: Failed to read from sheets - $e');
      }
      return [];
    }
  }

  /// Analyze feedback patterns
  static Future<FeedbackAnalytics> analyzeFeedback() async {
    try {
      final allFeedback = await getAllFeedback();

      final likesCount =
          allFeedback.where((f) => f.feedbackType == FeedbackType.like).length;
      final dislikesCount = allFeedback
          .where((f) => f.feedbackType == FeedbackType.dislike)
          .length;
      final totalFeedbacks = allFeedback.length;

      final likeRatio = totalFeedbacks > 0 ? likesCount / totalFeedbacks : 0.0;

      // Count feedback reasons
      final reasonCounts = <FeedbackReason, int>{};
      for (final feedback in allFeedback) {
        if (feedback.feedbackReason != null) {
          reasonCounts[feedback.feedbackReason!] =
              (reasonCounts[feedback.feedbackReason!] ?? 0) + 1;
        }
      }

      // Extract common issues and success patterns
      final commonIssues = <String>[];
      final successPatterns = <String>[];

      // TODO: Implement more sophisticated pattern analysis

      return FeedbackAnalytics(
        totalFeedbacks: totalFeedbacks,
        likesCount: likesCount,
        dislikesCount: dislikesCount,
        likeRatio: likeRatio,
        reasonCounts: reasonCounts,
        commonIssues: commonIssues,
        successPatterns: successPatterns,
      );
    } catch (e) {
      if (kDebugMode) {
        print('Feedback Service: Analysis failed - $e');
      }
      return FeedbackAnalytics(
        totalFeedbacks: 0,
        likesCount: 0,
        dislikesCount: 0,
        likeRatio: 0.0,
        reasonCounts: {},
        commonIssues: [],
        successPatterns: [],
      );
    }
  }

  /// Clear all feedback data
  static Future<void> clearAllFeedback() async {
    await _ensureInitialized();
    _feedbackCache.clear();
    await _saveFeedbackToStorage();

    if (kDebugMode) {
      print('Feedback Service: All feedback data cleared');
    }
  }

  /// Get feedback count
  static Future<int> getFeedbackCount() async {
    await _ensureInitialized();
    return _feedbackCache.length;
  }

  /// Get the Google Sheets URL for viewing feedback data
  static String get feedbackSheetsUrl => GoogleSheetsService.spreadsheetUrl;

  /// Check if Google Sheets integration is ready
  static bool get isSheetsReady => GoogleSheetsService.isReady;

  /// Close the service
  static Future<void> dispose() async {
    _feedbackCache.clear();
    _isInitialized = false;
  }
}
