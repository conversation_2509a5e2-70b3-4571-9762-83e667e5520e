import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import '../models/itinerary.dart';
import '../services/itinerary_conversion_service.dart';
import '../services/enhanced_content_service.dart';
import '../services/itinerary_service.dart';
import '../services/ai_itinerary_parser.dart';
import '../widgets/enhanced_itinerary_display.dart';

/// Interactive itinerary widget that supports both view and edit modes
class InteractiveItineraryWidget extends StatefulWidget {
  final ParsedContent content;
  final VoidCallback? onSave;
  final bool initialEditMode;

  const InteractiveItineraryWidget({
    super.key,
    required this.content,
    this.onSave,
    this.initialEditMode = false,
  });

  @override
  State<InteractiveItineraryWidget> createState() =>
      _InteractiveItineraryWidgetState();
}

class _InteractiveItineraryWidgetState extends State<InteractiveItineraryWidget>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  bool _isEditMode = false;
  Itinerary? _editableItinerary;
  int _selectedDayIndex = 0;
  bool _isDragging = false;
  String? _draggedActivityKey;

  // Activity times for timeline positioning
  Map<String, Map<String, Map<String, String>>> _activityTimes = {};

  @override
  void initState() {
    super.initState();
    _isEditMode = widget.initialEditMode;

    // Initialize animations
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    // Convert to editable itinerary if needed
    if (widget.content.itinerary != null) {
      _editableItinerary = ItineraryConversionService.convertParsedItinerary(
        widget.content.itinerary!,
      );
      _activityTimes = _editableItinerary!.activityTimes ?? {};
    }

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggleEditMode() {
    setState(() {
      _isEditMode = !_isEditMode;
    });

    // Trigger animation
    _animationController.reset();
    _animationController.forward();

    HapticFeedback.lightImpact();
  }

  void _saveItinerary() async {
    if (_editableItinerary != null) {
      try {
        // Save to itinerary service
        final success =
            await ItineraryService.saveItinerary(_editableItinerary!);

        if (success) {
          // Call callback
          widget.onSave?.call();

          // Show success message
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'Itinerary saved successfully!',
                  style: GoogleFonts.instrumentSans(),
                ),
                backgroundColor: const Color(0xFF0D76FF),
              ),
            );
          }
        } else {
          throw Exception('Failed to save itinerary');
        }
      } catch (e) {
        // Show error message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Failed to save itinerary: $e',
                style: GoogleFonts.instrumentSans(),
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Container(
          margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
          decoration: BoxDecoration(
            color: const Color(0xFFF7F9FC),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(),
              _buildContent(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          colors: [Color(0xFF0D76FF), Color(0xFF1E88E5)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Icon(
            _isEditMode ? Icons.edit : Icons.map,
            size: 20,
            color: Colors.white,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.content.title,
                  style: GoogleFonts.instrumentSans(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _isEditMode ? 'Interactive Mode' : 'View Mode',
                  style: GoogleFonts.instrumentSans(
                    fontSize: 12,
                    color: Colors.white.withOpacity(0.8),
                  ),
                ),
              ],
            ),
          ),
          Row(
            children: [
              if (_isEditMode) ...[
                IconButton(
                  onPressed: _saveItinerary,
                  icon: const Icon(
                    Icons.save,
                    size: 20,
                    color: Colors.white,
                  ),
                  tooltip: 'Save Itinerary',
                ),
                const SizedBox(width: 8),
              ],
              IconButton(
                onPressed: _toggleEditMode,
                icon: Icon(
                  _isEditMode ? Icons.visibility : Icons.edit,
                  size: 20,
                  color: Colors.white,
                ),
                tooltip: _isEditMode ? 'View Mode' : 'Edit Mode',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    if (_isEditMode && _editableItinerary != null) {
      return _buildInteractiveContent();
    } else {
      return _buildReadOnlyContent();
    }
  }

  Widget _buildReadOnlyContent() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: EnhancedItineraryDisplay(
        itinerary: widget.content.itinerary!,
        isCompact: false,
      ),
    );
  }

  Widget _buildInteractiveContent() {
    if (_editableItinerary == null) {
      return const Padding(
        padding: EdgeInsets.all(16),
        child: Center(
          child: Text('Unable to load interactive mode'),
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDaySelector(),
          const SizedBox(height: 16),
          _buildTimelineView(),
        ],
      ),
    );
  }

  Widget _buildDaySelector() {
    final dayCount =
        ItineraryConversionService.getItineraryDayCount(_editableItinerary!);

    if (dayCount <= 1) {
      return const SizedBox.shrink();
    }

    return Container(
      height: 50,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: dayCount,
        itemBuilder: (context, index) {
          final isSelected = index == _selectedDayIndex;

          return GestureDetector(
            onTap: () {
              setState(() {
                _selectedDayIndex = index;
              });
              HapticFeedback.lightImpact();
            },
            child: Container(
              margin: const EdgeInsets.only(right: 12),
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              decoration: BoxDecoration(
                color: isSelected ? const Color(0xFF0D76FF) : Colors.white,
                borderRadius: BorderRadius.circular(25),
                border: Border.all(
                  color: isSelected
                      ? const Color(0xFF0D76FF)
                      : const Color(0xFFE2E8F0),
                  width: 1,
                ),
              ),
              child: Text(
                'Day ${index + 1}',
                style: GoogleFonts.instrumentSans(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: isSelected ? Colors.white : const Color(0xFF2D3748),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildTimelineView() {
    return Container(
      height: 400, // Fixed height for timeline
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTimeAxis(),
          const SizedBox(width: 16),
          Expanded(
            child: _buildActivitiesColumn(),
          ),
        ],
      ),
    );
  }

  Widget _buildTimeAxis() {
    return SizedBox(
      width: 60,
      child: Column(
        children: List.generate(12, (index) {
          final hour = 8 + index;
          return Container(
            height: 60,
            alignment: Alignment.topCenter,
            child: Text(
              '${hour.toString().padLeft(2, '0')}:00',
              style: GoogleFonts.instrumentSans(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: const Color(0xFF718096),
              ),
            ),
          );
        }),
      ),
    );
  }

  Widget _buildActivitiesColumn() {
    return Container(
      height: 720, // 12 hours * 60px
      child: Stack(
        children: [
          // Timeline line
          Positioned(
            left: 0,
            top: 0,
            bottom: 0,
            child: Container(
              width: 2,
              color: const Color(0xFFE2E8F0),
            ),
          ),
          // Time markers
          ...List.generate(12, (index) {
            return Positioned(
              left: 0,
              top: index * 60.0,
              child: Container(
                width: 8,
                height: 2,
                color: const Color(0xFFE2E8F0),
              ),
            );
          }),
          // Drop targets for time slots
          ...List.generate(12, (index) {
            final hour = 8 + index;
            return Positioned(
              left: 16,
              top: index * 60.0,
              right: 0,
              height: 60,
              child: _buildTimeSlotDropTarget(hour),
            );
          }),
          // Activities
          _buildActivityWidgets(),
        ],
      ),
    );
  }

  Widget _buildActivityWidgets() {
    final dayNumber = _selectedDayIndex + 1;
    final activitiesForDay = _editableItinerary!.getActivitiesForDay(dayNumber);

    final widgets = <Widget>[];

    for (final destinationEntry in activitiesForDay.entries) {
      final destination = destinationEntry.key;
      final activities = destinationEntry.value;

      for (final activity in activities) {
        final activityTime = _activityTimes[destination]?[activity];
        if (activityTime != null) {
          final startTime = activityTime['startTime'] ?? '09:00';
          final endTime = activityTime['endTime'] ?? '11:00';

          final position = _calculateActivityPosition(startTime, endTime);

          widgets.add(
            Positioned(
              left: 16,
              top: position['top']!,
              right: 0,
              height: position['height']!,
              child: _buildActivityCard({
                'name': activity,
                'destination': destination,
                'startTime': startTime,
                'endTime': endTime,
                'day': dayNumber.toString(),
              }),
            ),
          );
        }
      }
    }

    return Stack(children: widgets);
  }

  Map<String, double> _calculateActivityPosition(
      String startTime, String endTime) {
    final startHour = _parseTimeToHour(startTime);
    final endHour = _parseTimeToHour(endTime);

    // Timeline starts at 8 AM, each hour is 60px
    final top = (startHour - 8) * 60.0;
    final height = (endHour - startHour) * 60.0;

    return {
      'top': top,
      'height': height.clamp(40.0, 240.0), // Min 40px, max 4 hours
    };
  }

  double _parseTimeToHour(String time) {
    final parts = time.split(':');
    final hour = int.parse(parts[0]);
    final minute = parts.length > 1 ? int.parse(parts[1]) : 0;
    return hour + (minute / 60.0);
  }

  Widget _buildActivityCard(Map<String, String> activity) {
    final destination = activity['destination'] ?? '';
    final activityName = activity['name'] ?? '';
    final startTime = activity['startTime'] ?? '09:00';
    final endTime = activity['endTime'] ?? '10:00';

    return Draggable<Map<String, String>>(
      data: activity,
      onDragStarted: () {
        setState(() {
          _isDragging = true;
          _draggedActivityKey = '$destination-$activityName';
        });
        HapticFeedback.lightImpact();
      },
      onDragEnd: (details) {
        setState(() {
          _isDragging = false;
          _draggedActivityKey = null;
        });
      },
      feedback: Material(
        elevation: 8,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          width: 200,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: const Color(0xFF0D76FF).withOpacity(0.9),
            borderRadius: BorderRadius.circular(12),
          ),
          child: _buildActivityContent(
              activityName, destination, startTime, endTime, true),
        ),
      ),
      childWhenDragging: Opacity(
        opacity: 0.5,
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey.withOpacity(0.3),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: const Color(0xFFE2E8F0),
              width: 1,
            ),
          ),
          child: _buildActivityContent(
              activityName, destination, startTime, endTime),
        ),
      ),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: const Color(0xFFE2E8F0),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: _buildActivityContent(
            activityName, destination, startTime, endTime),
      ),
    );
  }

  Widget _buildActivityContent(
      String activity, String destination, String startTime, String endTime,
      [bool isDragging = false]) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          activity,
          style: GoogleFonts.instrumentSans(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: isDragging ? Colors.white : const Color(0xFF2D3748),
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 4),
        Text(
          destination,
          style: GoogleFonts.instrumentSans(
            fontSize: 12,
            color: isDragging
                ? Colors.white.withOpacity(0.8)
                : const Color(0xFF718096),
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 4),
        Text(
          '$startTime - $endTime',
          style: GoogleFonts.instrumentSans(
            fontSize: 11,
            fontWeight: FontWeight.w500,
            color: isDragging
                ? Colors.white.withOpacity(0.9)
                : const Color(0xFF0D76FF),
          ),
        ),
      ],
    );
  }

  Widget _buildTimeSlotDropTarget(int hour) {
    return DragTarget<Map<String, String>>(
      onAcceptWithDetails: (details) {
        _onActivityDroppedOnTimeSlot(details.data, hour);
      },
      builder: (context, candidateData, rejectedData) {
        final isHovering = candidateData.isNotEmpty;
        return Container(
          decoration: isHovering
              ? BoxDecoration(
                  color: const Color(0xFF0D76FF).withOpacity(0.1),
                  border: Border.all(
                    color: const Color(0xFF0D76FF),
                    width: 2,
                  ),
                  borderRadius: BorderRadius.circular(8),
                )
              : null,
          child: isHovering
              ? Center(
                  child: Text(
                    'Drop here for ${_formatHour(hour)}',
                    style: GoogleFonts.instrumentSans(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: const Color(0xFF0D76FF),
                    ),
                  ),
                )
              : null,
        );
      },
    );
  }

  String _formatHour(int hour) {
    return '${hour.toString().padLeft(2, '0')}:00';
  }

  void _onActivityDroppedOnTimeSlot(Map<String, String> activity, int hour) {
    final destination = activity['destination'] ?? '';
    final activityName = activity['name'] ?? '';
    final dayNumber = _selectedDayIndex + 1;

    // Calculate new end time (default 2 hours)
    final startTime = _formatHour(hour);
    final endTime = _formatHour(hour + 2);

    setState(() {
      // Update activity times
      if (!_activityTimes.containsKey(destination)) {
        _activityTimes[destination] = {};
      }

      _activityTimes[destination]![activityName] = {
        'startTime': startTime,
        'endTime': endTime,
        'day': dayNumber.toString(),
      };

      // Update the editable itinerary
      _editableItinerary =
          ItineraryConversionService.updateItineraryActivityTimes(
        _editableItinerary!,
        _activityTimes,
      );
    });

    HapticFeedback.mediumImpact();
  }

  void _addNewActivity() {
    // This would open a dialog to add a new activity
    // For now, just show a placeholder
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Add Activity',
          style: GoogleFonts.instrumentSans(fontWeight: FontWeight.bold),
        ),
        content: Text(
          'Activity creation dialog coming soon...',
          style: GoogleFonts.instrumentSans(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Close',
              style: GoogleFonts.instrumentSans(color: const Color(0xFF0D76FF)),
            ),
          ),
        ],
      ),
    );
  }

  void _removeActivity(Map<String, String> activity) {
    final destination = activity['destination'] ?? '';
    final activityName = activity['name'] ?? '';
    final dayNumber = _selectedDayIndex + 1;

    setState(() {
      // Remove from activity times
      _activityTimes[destination]?.remove(activityName);

      // Remove from day-specific activities
      if (_editableItinerary!.daySpecificActivities != null) {
        _editableItinerary!.daySpecificActivities![dayNumber]?[destination]
            ?.remove(activityName);

        // Clean up empty lists/maps
        if (_editableItinerary!
                .daySpecificActivities![dayNumber]?[destination]?.isEmpty ==
            true) {
          _editableItinerary!.daySpecificActivities![dayNumber]
              ?.remove(destination);
        }
        if (_editableItinerary!.daySpecificActivities![dayNumber]?.isEmpty ==
            true) {
          _editableItinerary!.daySpecificActivities!.remove(dayNumber);
        }
      }
    });

    HapticFeedback.lightImpact();
  }
}
