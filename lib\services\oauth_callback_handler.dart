import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:app_links/app_links.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../config/supabase_config.dart';
import '../utils/oauth_debug_helper.dart';

class OAuthCallbackHandler {
  static final OAuthCallbackHandler _instance =
      OAuthCallbackHandler._internal();
  factory OAuthCallbackHandler() => _instance;
  OAuthCallbackHandler._internal();

  final AppLinks _appLinks = AppLinks();
  StreamSubscription<Uri>? _linkSubscription;
  final SupabaseClient _supabase = SupabaseConfig.client;

  // Initialize the OAuth callback handler
  Future<void> initialize() async {
    try {
      if (kDebugMode) {
        print('OAuth Callback Handler: Initializing...');
      }

      // Listen for incoming deep links
      _linkSubscription = _appLinks.uriLinkStream.listen(
        (Uri uri) {
          if (kDebugMode) {
            print('OAuth Callback Handler: Received deep link: $uri');
          }
          _handleDeepLink(uri);
        },
        onError: (error) {
          if (kDebugMode) {
            print('OAuth Callback Handler: Deep link error: $error');
          }
        },
      );

      // Check for initial link when app is launched from a deep link
      final Uri? initialLink = await _appLinks.getInitialLink();
      if (initialLink != null) {
        if (kDebugMode) {
          print('OAuth Callback Handler: Initial deep link: $initialLink');
        }
        _handleDeepLink(initialLink);
      }

      if (kDebugMode) {
        print('OAuth Callback Handler: Initialization complete');
      }
    } catch (error) {
      if (kDebugMode) {
        print('OAuth Callback Handler: Initialization error: $error');
      }
    }
  }

  // Handle incoming deep links
  void _handleDeepLink(Uri uri) {
    try {
      if (kDebugMode) {
        print('OAuth Callback Handler: Processing deep link: $uri');
        OAuthDebugHelper.logOAuthCallback(uri);
      }

      // Check if this is an OAuth callback with our custom scheme
      if (uri.scheme == 'io.supabase.tripwisego') {
        // Handle different callback paths
        if (uri.host == 'login-callback' ||
            uri.path.contains('login-callback')) {
          _handleOAuthCallback(uri);
        } else if (uri.host == 'reset-password' ||
            uri.path.contains('reset-password')) {
          // Handle password reset callback
          if (kDebugMode) {
            print('OAuth Callback Handler: Password reset callback received');
          }
          // TODO: Handle password reset if needed
        } else {
          if (kDebugMode) {
            print(
                'OAuth Callback Handler: Unknown callback path: ${uri.host}${uri.path}');
          }
        }
      } else if (uri.scheme == 'https' &&
          uri.host.contains('supabase.co') &&
          uri.path.contains('/auth/v1/callback')) {
        // Handle web OAuth callback (should not happen on mobile but just in case)
        _handleWebOAuthCallback(uri);
      } else {
        if (kDebugMode) {
          print('OAuth Callback Handler: Unhandled deep link: $uri');
        }
      }
    } catch (error) {
      if (kDebugMode) {
        print('OAuth Callback Handler: Error processing deep link: $error');
      }
    }
  }

  // Handle OAuth callback for mobile apps
  void _handleOAuthCallback(Uri uri) async {
    try {
      if (kDebugMode) {
        print('OAuth Callback Handler: Processing OAuth callback...');
      }

      // Extract query parameters and fragment parameters
      final Map<String, String> params =
          Map<String, String>.from(uri.queryParameters);

      // Also check fragment parameters (some OAuth providers use fragments)
      if (uri.fragment.isNotEmpty) {
        final fragmentParams = Uri.splitQueryString(uri.fragment);
        params.addAll(fragmentParams);
      }

      if (kDebugMode) {
        print('OAuth Callback Handler: All parameters: $params');
      }

      // Check for error in callback
      if (params.containsKey('error')) {
        final error = params['error'];
        final errorDescription = params['error_description'] ?? 'Unknown error';
        if (kDebugMode) {
          print(
              'OAuth Callback Handler: OAuth error: $error - $errorDescription');
        }
        return;
      }

      // Check for access token (implicit flow)
      if (params.containsKey('access_token')) {
        if (kDebugMode) {
          print(
              'OAuth Callback Handler: Received access token (implicit flow)');
        }

        // For implicit flow, the session should already be established
        // Just trigger auth state refresh
        await _refreshAuthState();
        return;
      }

      // Check for authorization code (PKCE flow)
      if (params.containsKey('code')) {
        final code = params['code'];
        if (kDebugMode) {
          print(
              'OAuth Callback Handler: Received authorization code: ${code?.substring(0, 10)}...');
        }

        // Exchange code for session
        await _exchangeCodeForSession(code!);
      } else {
        if (kDebugMode) {
          print(
              'OAuth Callback Handler: No authorization code or access token found in callback');
        }
      }
    } catch (error) {
      if (kDebugMode) {
        print('OAuth Callback Handler: Error handling OAuth callback: $error');
      }
    }
  }

  // Handle web OAuth callback
  void _handleWebOAuthCallback(Uri uri) async {
    try {
      if (kDebugMode) {
        print('OAuth Callback Handler: Processing web OAuth callback...');
      }

      // For web callbacks, Supabase should handle this automatically
      // But we can trigger a session refresh to ensure the app state is updated
      await _refreshAuthState();
    } catch (error) {
      if (kDebugMode) {
        print(
            'OAuth Callback Handler: Error handling web OAuth callback: $error');
      }
    }
  }

  // Exchange authorization code for session
  Future<void> _exchangeCodeForSession(String code) async {
    try {
      if (kDebugMode) {
        print('OAuth Callback Handler: Exchanging code for session...');
      }

      // Use Supabase's session exchange method
      final AuthSessionUrlResponse response =
          await _supabase.auth.exchangeCodeForSession(code);

      // Session was established successfully
      final session = response.session;
      final user = session.user;

      if (kDebugMode) {
        print('OAuth Callback Handler: Session established successfully');
        print('OAuth Callback Handler: User ID: ${user.id}');
        print('OAuth Callback Handler: User email: ${user.email}');
      }

      // Trigger auth state update
      await _refreshAuthState();

      // Log final auth state for debugging
      if (kDebugMode) {
        OAuthDebugHelper.logAuthState();
      }
    } catch (error) {
      if (kDebugMode) {
        print(
            'OAuth Callback Handler: Error exchanging code for session: $error');
      }
    }
  }

  // Refresh authentication state
  Future<void> _refreshAuthState() async {
    try {
      if (kDebugMode) {
        print('OAuth Callback Handler: Refreshing auth state...');
      }

      // Get the current session
      final session = _supabase.auth.currentSession;
      if (session != null) {
        if (kDebugMode) {
          print('OAuth Callback Handler: Valid session found');
          print('OAuth Callback Handler: User ID: ${session.user.id}');
          print('OAuth Callback Handler: User email: ${session.user.email}');
        }

        // The AuthStateManager should automatically pick up the session change
        // through the Supabase auth state change listener
        // No need to manually trigger initialization
      } else {
        if (kDebugMode) {
          print('OAuth Callback Handler: No valid session found');
        }
      }
    } catch (error) {
      if (kDebugMode) {
        print('OAuth Callback Handler: Error refreshing auth state: $error');
      }
    }
  }

  // Check if the app was launched from an OAuth callback
  Future<bool> checkForOAuthCallback() async {
    try {
      final Uri? initialLink = await _appLinks.getInitialLink();
      if (initialLink != null) {
        if (kDebugMode) {
          print('OAuth Callback Handler: App launched with link: $initialLink');
        }

        // Check if this is an OAuth callback
        if ((initialLink.scheme == 'io.supabase.tripwisego' &&
                initialLink.host == 'login-callback') ||
            (initialLink.scheme == 'https' &&
                initialLink.host.contains('supabase.co') &&
                initialLink.path.contains('/auth/v1/callback'))) {
          return true;
        }
      }
      return false;
    } catch (error) {
      if (kDebugMode) {
        print(
            'OAuth Callback Handler: Error checking for OAuth callback: $error');
      }
      return false;
    }
  }

  // Dispose resources
  void dispose() {
    _linkSubscription?.cancel();
  }
}
