import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../services/hybrid_ai_service.dart';

/// A widget that allows users to select between default and premium AI models
class ModelSelector extends StatefulWidget {
  final Function(String)? onModelChanged;
  final bool showLabels;
  final bool isCompact;

  const ModelSelector({
    super.key,
    this.onModelChanged,
    this.showLabels = true,
    this.isCompact = false,
  });

  @override
  State<ModelSelector> createState() => _ModelSelectorState();
}

class _ModelSelectorState extends State<ModelSelector>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<Color?> _colorAnimation;

  bool _isPremiumSelected = false;
  bool _isChanging = false;

  @override
  void initState() {
    super.initState();
    _isPremiumSelected = HybridAIService.isPremiumModelSelected();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _colorAnimation = ColorTween(
      begin: const Color(0xFF0D76FF),
      end: const Color(0xFF0D76FF).withOpacity(0.8),
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _toggleModel() async {
    if (_isChanging) return;

    setState(() {
      _isChanging = true;
    });

    _animationController.forward().then((_) {
      _animationController.reverse();
    });

    try {
      final newModel = _isPremiumSelected
          ? HybridAIService.defaultModel
          : HybridAIService.premiumModel;

      await HybridAIService.setSelectedModel(newModel);

      setState(() {
        _isPremiumSelected = !_isPremiumSelected;
      });

      widget.onModelChanged?.call(newModel);
    } catch (error) {
      // Handle error silently or show a snackbar
      debugPrint('Failed to change model: $error');
    } finally {
      setState(() {
        _isChanging = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isCompact) {
      return _buildCompactSelector();
    } else {
      return _buildFullSelector();
    }
  }

  Widget _buildCompactSelector() {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTap: _toggleModel,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: _isPremiumSelected
                    ? const Color(0xFF0D76FF)
                    : const Color(0xFFF7F9FC),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: const Color(0xFF0D76FF),
                  width: 1,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    _isPremiumSelected ? Icons.auto_awesome : Icons.speed,
                    size: 14,
                    color: _isPremiumSelected
                        ? Colors.white
                        : const Color(0xFF0D76FF),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _isPremiumSelected ? 'Premium' : 'Default',
                    style: GoogleFonts.instrumentSans(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: _isPremiumSelected
                          ? Colors.white
                          : const Color(0xFF0D76FF),
                    ),
                  ),
                  if (_isChanging) ...[
                    const SizedBox(width: 4),
                    SizedBox(
                      width: 10,
                      height: 10,
                      child: CircularProgressIndicator(
                        strokeWidth: 1.5,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          _isPremiumSelected
                              ? Colors.white
                              : const Color(0xFF0D76FF),
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildFullSelector() {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                if (widget.showLabels) ...[
                  Text(
                    'AI Model Selection',
                    style: GoogleFonts.instrumentSans(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: const Color(0xFF2D3748),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Choose between performance and speed',
                    style: GoogleFonts.instrumentSans(
                      fontSize: 12,
                      color: const Color(0xFF718096),
                    ),
                  ),
                  const SizedBox(height: 16),
                ],
                Row(
                  children: [
                    Expanded(
                      child: _buildModelOption(
                        title: 'Default',
                        subtitle: 'Fast responses',
                        icon: Icons.speed,
                        isSelected: !_isPremiumSelected,
                        onTap: () => _selectModel(false),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildModelOption(
                        title: 'Premium',
                        subtitle: 'Better quality',
                        icon: Icons.auto_awesome,
                        isSelected: _isPremiumSelected,
                        onTap: () => _selectModel(true),
                      ),
                    ),
                  ],
                ),
                if (_isChanging) ...[
                  const SizedBox(height: 12),
                  Center(
                    child: SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          _colorAnimation.value ?? const Color(0xFF0D76FF),
                        ),
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildModelOption({
    required String title,
    required String subtitle,
    required IconData icon,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: _isChanging ? null : onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF0D76FF) : const Color(0xFFF7F9FC),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color:
                isSelected ? const Color(0xFF0D76FF) : const Color(0xFFE2E8F0),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              size: 24,
              color: isSelected ? Colors.white : const Color(0xFF0D76FF),
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: GoogleFonts.instrumentSans(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: isSelected ? Colors.white : const Color(0xFF2D3748),
              ),
            ),
            const SizedBox(height: 2),
            Text(
              subtitle,
              style: GoogleFonts.instrumentSans(
                fontSize: 11,
                color: isSelected
                    ? Colors.white.withOpacity(0.8)
                    : const Color(0xFF718096),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectModel(bool isPremium) async {
    if (_isPremiumSelected == isPremium || _isChanging) return;

    setState(() {
      _isChanging = true;
    });

    _animationController.forward().then((_) {
      _animationController.reverse();
    });

    try {
      final newModel = isPremium
          ? HybridAIService.premiumModel
          : HybridAIService.defaultModel;

      await HybridAIService.setSelectedModel(newModel);

      setState(() {
        _isPremiumSelected = isPremium;
      });

      widget.onModelChanged?.call(newModel);
    } catch (error) {
      debugPrint('Failed to change model: $error');
    } finally {
      setState(() {
        _isChanging = false;
      });
    }
  }
}
