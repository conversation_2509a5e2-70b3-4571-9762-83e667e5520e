import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/survey_models.dart';
import 'survey_service.dart';
import 'auth_service.dart';

class MatchService {
  // Store match history to prevent duplicate match screens
  static final Set<String> _shownMatches = <String>{};
  static const String _matchHistoryKey = 'shown_matches_history';
  static bool _isInitialized = false;

  /// Initialize the match service and load match history
  static Future<void> _initializeIfNeeded() async {
    if (_isInitialized) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      final savedMatches = prefs.getStringList(_matchHistoryKey) ?? [];
      _shownMatches.addAll(savedMatches);
      _isInitialized = true;

      if (kDebugMode) {
        print(
            'Match Service: Initialized with ${_shownMatches.length} saved matches');
      }
    } catch (error) {
      if (kDebugMode) {
        print('Match Service: Error initializing: $error');
      }
      _isInitialized = true; // Mark as initialized even if loading failed
    }
  }

  /// Save match history to persistent storage
  static Future<void> _saveMatchHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList(_matchHistoryKey, _shownMatches.toList());

      if (kDebugMode) {
        print(
            'Match Service: Saved ${_shownMatches.length} matches to storage');
      }
    } catch (error) {
      if (kDebugMode) {
        print('Match Service: Error saving match history: $error');
      }
    }
  }

  /// Check if a destination matches user preferences based on survey responses
  /// Returns a MatchResult with match status and matched categories
  static Future<MatchResult> checkDestinationMatch(
      Map<String, dynamic> destination) async {
    try {
      // Initialize if needed
      await _initializeIfNeeded();

      final user = AuthService.currentUser;
      if (user == null) {
        return MatchResult(isMatch: false, matchedCategories: []);
      }

      // Get user's survey response
      final surveyResponse = await SurveyService.getSurveyResponse(user.id);
      if (surveyResponse == null) {
        if (kDebugMode) {
          print('Match Service: No survey response found for user');
        }
        return MatchResult(isMatch: false, matchedCategories: []);
      }

      // Check if we've already shown a match for this destination
      final destinationId = destination['id']?.toString() ?? '';
      final matchKey = '${user.id}_$destinationId';
      if (_shownMatches.contains(matchKey)) {
        return MatchResult(isMatch: false, matchedCategories: []);
      }

      // Extract destination tags and properties
      final destinationTags = List<String>.from(destination['tags'] ?? []);

      // Perform matching against 5 categories
      final matchedCategories = <String>[];

      // 1. Travel Companion Matching
      if (_matchesTravelCompanion(
          surveyResponse.travelCompanion, destinationTags, destination)) {
        matchedCategories.add('Travel Companion');
      }

      // 2. Travel Priorities Matching
      if (_matchesTravelPriorities(
          surveyResponse.travelPriorities, destinationTags, destination)) {
        matchedCategories.add('Travel Priorities');
      }

      // 3. Travel Frustrations Avoidance
      if (_avoidsTravelFrustrations(
          surveyResponse.travelFrustrations, destinationTags, destination)) {
        matchedCategories.add('Frustration Avoidance');
      }

      // 4. Destination Type Matching
      if (_matchesDestinationType(surveyResponse.preferredDestinationTypes,
          destinationTags, destination)) {
        matchedCategories.add('Destination Type');
      }

      // 5. Activity/Experience Matching
      if (_matchesActivityInterests(
          destinationTags, destination, surveyResponse)) {
        matchedCategories.add('Activity Interests');
      }

      // Check if we have at least 3 out of 5 matches
      final isMatch = matchedCategories.length >= 3;

      if (isMatch) {
        // Mark this match as shown to prevent duplicates
        _shownMatches.add(matchKey);
        // Save to persistent storage
        await _saveMatchHistory();

        if (kDebugMode) {
          print(
              'Match Service: Found match for ${destination['name']} with categories: $matchedCategories');
        }
      }

      return MatchResult(
        isMatch: isMatch,
        matchedCategories: matchedCategories,
        destination: destination,
      );
    } catch (error) {
      if (kDebugMode) {
        print('Match Service: Error checking destination match: $error');
      }
      return MatchResult(isMatch: false, matchedCategories: []);
    }
  }

  /// Check if destination matches travel companion preferences
  static bool _matchesTravelCompanion(String? travelCompanion,
      List<String> destinationTags, Map<String, dynamic> destination) {
    if (travelCompanion == null) return false;

    final companion = travelCompanion.toLowerCase();
    final tags = destinationTags.map((tag) => tag.toLowerCase()).toList();

    // Solo traveler preferences - focus on self-discovery and independence
    if (companion.contains('solo')) {
      return tags.any((tag) => [
            'adventure',
            'hiking',
            'photography',
            'culture',
            'meditation',
            'wellness',
            'remote',
            'nature',
            'scenic',
            'peaceful',
            'authentic',
            'hidden gem',
            'temples',
            'historical'
          ].contains(tag));
    }

    // Partner/Spouse preferences - romantic and intimate experiences
    if (companion.contains('partner') || companion.contains('spouse')) {
      return tags.any((tag) => [
            'romance',
            'sunset',
            'luxury',
            'spa',
            'wellness',
            'fine dining',
            'couples',
            'beach',
            'mediterranean',
            'coastal',
            'wine',
            'scenic',
            'photography'
          ].contains(tag));
    }

    // Family with kids preferences - safe, educational, and fun
    if (companion.contains('family') || companion.contains('kids')) {
      return tags.any((tag) => [
            'family-friendly',
            'beach',
            'theme park',
            'zoo',
            'aquarium',
            'safe',
            'educational',
            'wildlife',
            'nature',
            'outdoor',
            'cultural',
            'museums',
            'accessible'
          ].contains(tag));
    }

    // Friends preferences - social and exciting activities
    if (companion.contains('friends')) {
      return tags.any((tag) => [
            'nightlife',
            'adventure',
            'group activities',
            'festivals',
            'party',
            'social',
            'fun',
            'urban',
            'shopping',
            'food',
            'diving',
            'extreme sports',
            'hiking'
          ].contains(tag));
    }

    // Business colleagues - professional and networking friendly
    if (companion.contains('business') || companion.contains('colleagues')) {
      return tags.any((tag) => [
            'urban',
            'luxury',
            'fine dining',
            'cultural',
            'museums',
            'architecture',
            'technology',
            'modern',
            'accessible',
            'reliable'
          ].contains(tag));
    }

    return false;
  }

  /// Check if destination matches travel priorities
  static bool _matchesTravelPriorities(List<String> travelPriorities,
      List<String> destinationTags, Map<String, dynamic> destination) {
    if (travelPriorities.isEmpty) return false;

    final priorities = travelPriorities.map((p) => p.toLowerCase()).toList();
    final tags = destinationTags.map((tag) => tag.toLowerCase()).toList();

    int matchCount = 0;

    for (final priority in priorities) {
      // "Trying local food" priority
      if (priority.contains('food') || priority.contains('local food')) {
        if (tags.any((tag) => [
              'cuisine',
              'food',
              'local food',
              'street food',
              'restaurant',
              'culinary',
              'fine dining',
              'authentic'
            ].contains(tag))) {
          matchCount++;
        }
      }
      // "Adventure activities" priority
      else if (priority.contains('adventure')) {
        if (tags.any((tag) => [
              'adventure',
              'hiking',
              'diving',
              'extreme sports',
              'outdoor',
              'climbing',
              'snorkeling',
              'marine life',
              'wildlife',
              'nature',
              'mountains',
              'glaciers',
              'volcanoes'
            ].contains(tag))) {
          matchCount++;
        }
      }
      // "Relaxation & wellness" priority
      else if (priority.contains('relaxation') ||
          priority.contains('wellness')) {
        if (tags.any((tag) => [
              'spa',
              'wellness',
              'relaxation',
              'meditation',
              'peaceful',
              'tranquil',
              'yoga',
              'beach',
              'luxury',
              'resort'
            ].contains(tag))) {
          matchCount++;
        }
      }
      // "Cultural/historical sites" priority
      else if (priority.contains('cultural') ||
          priority.contains('historical')) {
        if (tags.any((tag) => [
              'culture',
              'historical',
              'heritage',
              'museum',
              'temple',
              'architecture',
              'ancient',
              'traditional',
              'unesco',
              'castles',
              'authentic'
            ].contains(tag))) {
          matchCount++;
        }
      }
      // "Photography/scenic spots" priority
      else if (priority.contains('photography') ||
          priority.contains('scenic')) {
        if (tags.any((tag) => [
              'photography',
              'scenic',
              'landscape',
              'sunset',
              'viewpoint',
              'instagram',
              'waterfalls',
              'fjords',
              'mountains',
              'coastal',
              'nature'
            ].contains(tag))) {
          matchCount++;
        }
      }
      // "Shopping" priority
      else if (priority.contains('shopping')) {
        if (tags.any((tag) => [
              'shopping',
              'market',
              'bazaar',
              'mall',
              'boutique',
              'souvenir',
              'urban',
              'luxury'
            ].contains(tag))) {
          matchCount++;
        }
      }
    }

    // Match if at least 50% of priorities are met
    return matchCount >= (priorities.length * 0.5).ceil();
  }

  /// Check if destination avoids user's travel frustrations
  static bool _avoidsTravelFrustrations(List<String> travelFrustrations,
      List<String> destinationTags, Map<String, dynamic> destination) {
    if (travelFrustrations.isEmpty) return true; // No frustrations to avoid

    final frustrations =
        travelFrustrations.map((f) => f.toLowerCase()).toList();
    final tags = destinationTags.map((tag) => tag.toLowerCase()).toList();
    final rating = destination['rating'] as double? ?? 0.0;

    int frustrationsSolved = 0;

    // Check if destination helps avoid frustrations
    for (final frustration in frustrations) {
      // "Overwhelming options/info"
      if (frustration.contains('overwhelming') ||
          frustration.contains('options')) {
        // Destination should be simple/focused or well-organized
        if (tags.any((tag) => [
                  'simple',
                  'focused',
                  'curated',
                  'guided',
                  'organized',
                  'popular',
                  'established',
                  'luxury'
                ].contains(tag)) ||
            rating >= 4.5) {
          frustrationsSolved++;
        }
      }
      // "Unreliable pricing or availability"
      else if (frustration.contains('unreliable') ||
          frustration.contains('pricing')) {
        // Destination should be reliable/established with good infrastructure
        if (tags.any((tag) => [
                  'reliable',
                  'established',
                  'trusted',
                  'verified',
                  'popular',
                  'luxury',
                  'urban',
                  'tourist-friendly'
                ].contains(tag)) ||
            rating >= 4.5) {
          frustrationsSolved++;
        }
      }
      // "Difficulty coordinating with others"
      else if (frustration.contains('coordinating') ||
          frustration.contains('others')) {
        // Destination should be easy to coordinate and accessible
        if (tags.any((tag) => [
              'group-friendly',
              'easy access',
              'central',
              'convenient',
              'accessible',
              'popular',
              'urban',
              'established'
            ].contains(tag))) {
          frustrationsSolved++;
        }
      }
      // "Fear of missing hidden gems"
      else if (frustration.contains('hidden gems') ||
          frustration.contains('missing')) {
        // Destination should be a hidden gem or authentic local experience
        if (tags.any((tag) => [
              'hidden gem',
              'local',
              'authentic',
              'off-the-beaten-path',
              'unique',
              'remote',
              'traditional',
              'undiscovered'
            ].contains(tag))) {
          frustrationsSolved++;
        }
      }
      // "Language barriers"
      else if (frustration.contains('language')) {
        // Destination should be English-friendly or internationally accessible
        if (tags.any((tag) => [
              'english-friendly',
              'international',
              'tourist-friendly',
              'multilingual',
              'popular',
              'urban',
              'luxury'
            ].contains(tag))) {
          frustrationsSolved++;
        }
      }
    }

    // Match if at least 50% of frustrations are addressed
    return frustrationsSolved >= (frustrations.length * 0.5).ceil();
  }

  /// Check if destination matches preferred destination types
  static bool _matchesDestinationType(List<String> preferredDestinationTypes,
      List<String> destinationTags, Map<String, dynamic> destination) {
    if (preferredDestinationTypes.isEmpty) return false;

    final types =
        preferredDestinationTypes.map((t) => t.toLowerCase()).toList();
    final tags = destinationTags.map((tag) => tag.toLowerCase()).toList();
    final location = destination['location']?.toString().toLowerCase() ?? '';
    final name = destination['name']?.toString().toLowerCase() ?? '';

    for (final type in types) {
      // "Popular tourist hubs (e.g., Paris, NYC)"
      if (type.contains('popular') || type.contains('tourist hubs')) {
        if (tags.any((tag) => [
                  'popular',
                  'famous',
                  'iconic',
                  'tourist',
                  'must-see',
                  'luxury',
                  'urban',
                  'metropolitan'
                ].contains(tag)) ||
            location.contains('paris') ||
            location.contains('nyc') ||
            location.contains('london') ||
            location.contains('tokyo') ||
            location.contains('dubai') ||
            name.contains('santorini') ||
            name.contains('maldives')) {
          return true;
        }
      }
      // "Offbeat/local gems (e.g., small towns)"
      else if (type.contains('offbeat') || type.contains('local gems')) {
        if (tags.any((tag) => [
              'hidden gem',
              'local',
              'authentic',
              'offbeat',
              'undiscovered',
              'remote',
              'traditional',
              'eco-tourism'
            ].contains(tag))) {
          return true;
        }
      }
      // "Nature/outdoor adventures (e.g., national parks)"
      else if (type.contains('nature') || type.contains('outdoor')) {
        if (tags.any((tag) => [
              'nature',
              'outdoor',
              'hiking',
              'national park',
              'wilderness',
              'natural',
              'adventure',
              'mountains',
              'wildlife',
              'glaciers',
              'fjords',
              'rainforest',
              'volcanoes',
              'safari'
            ].contains(tag))) {
          return true;
        }
      }
      // "Urban/city experiences (e.g., museums, nightlife)"
      else if (type.contains('urban') || type.contains('city')) {
        if (tags.any((tag) => [
              'urban',
              'city',
              'metropolitan',
              'nightlife',
              'museums',
              'cultural',
              'shopping',
              'architecture',
              'technology',
              'modern',
              'food'
            ].contains(tag))) {
          return true;
        }
      }
      // "Mix of all the above"
      else if (type.contains('mix')) {
        // Mix of all types - more flexible matching, return true for any destination
        return true;
      }
    }

    return false;
  }

  /// Check if destination matches activity interests based on tags and survey data
  static bool _matchesActivityInterests(List<String> destinationTags,
      Map<String, dynamic> destination, SurveyResponse surveyResponse) {
    final tags = destinationTags.map((tag) => tag.toLowerCase()).toList();

    // Combine all user preferences to infer activity interests
    final allPreferences = [
      ...surveyResponse.travelPriorities,
      ...surveyResponse.preferredDestinationTypes,
      if (surveyResponse.travelCompanion != null)
        surveyResponse.travelCompanion!
    ].map((p) => p.toLowerCase()).toList();

    int activityMatches = 0;

    // Check for various activity categories
    final activityCategories = {
      'water_activities': [
        'diving',
        'snorkeling',
        'swimming',
        'beach',
        'marine life',
        'coral reef'
      ],
      'adventure_sports': [
        'hiking',
        'climbing',
        'extreme sports',
        'adventure',
        'outdoor'
      ],
      'cultural_activities': [
        'culture',
        'historical',
        'heritage',
        'museum',
        'temple',
        'architecture'
      ],
      'relaxation_activities': [
        'spa',
        'wellness',
        'relaxation',
        'meditation',
        'peaceful'
      ],
      'food_experiences': [
        'cuisine',
        'food',
        'local food',
        'street food',
        'culinary'
      ],
      'photography_spots': [
        'photography',
        'scenic',
        'landscape',
        'sunset',
        'viewpoint'
      ],
      'nightlife_entertainment': [
        'nightlife',
        'party',
        'entertainment',
        'festivals',
        'social'
      ],
      'shopping_experiences': [
        'shopping',
        'market',
        'bazaar',
        'boutique',
        'souvenir'
      ]
    };

    for (final category in activityCategories.entries) {
      final categoryKeywords = category.value;

      // Check if user preferences align with this category
      final userLikesCategory = allPreferences.any(
          (pref) => categoryKeywords.any((keyword) => pref.contains(keyword)));

      // Check if destination offers this category
      final destinationOffersCategory =
          tags.any((tag) => categoryKeywords.contains(tag));

      if (userLikesCategory && destinationOffersCategory) {
        activityMatches++;
      }
    }

    // Match if at least 2 activity categories align
    return activityMatches >= 2;
  }

  /// Clear match history (useful for testing or user preference reset)
  static Future<void> clearMatchHistory() async {
    _shownMatches.clear();
    await _saveMatchHistory();

    if (kDebugMode) {
      print('Match Service: Match history cleared');
    }
  }

  /// Get match statistics for debugging
  static Map<String, dynamic> getMatchStats() {
    return {
      'total_shown_matches': _shownMatches.length,
      'shown_matches': _shownMatches.toList(),
    };
  }
}

/// Result class for destination matching
class MatchResult {
  final bool isMatch;
  final List<String> matchedCategories;
  final Map<String, dynamic>? destination;

  MatchResult({
    required this.isMatch,
    required this.matchedCategories,
    this.destination,
  });
}
