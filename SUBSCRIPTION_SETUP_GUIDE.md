# TripWiseGo In-App Purchase Subscription Setup Guide

This guide provides step-by-step instructions for setting up the in-app purchase subscription system for TripWiseGo using RevenueCat.

## Overview

The subscription system includes:
- **1 Week subscription**: $1.00 USD
- **1 Month subscription**: $3.00 USD  
- **3 Months subscription**: $5.00 USD
- **Feature limitations**: 20 swipes/day for free users, 100 swipes/day for subscribers

## Prerequisites

1. Apple Developer Account (for iOS)
2. Google Play Console Account (for Android)
3. RevenueCat Account (free tier available)
4. Flutter development environment

## Step 1: RevenueCat Setup

### 1.1 Create RevenueCat Account
1. Go to [RevenueCat](https://www.revenuecat.com/)
2. Sign up for a free account
3. Create a new project named "TripWiseGo"

### 1.2 Configure RevenueCat Project
1. In RevenueCat dashboard, go to **Project Settings**
2. Note down your **Public API Keys**:
   - Apple App Store API Key
   - Google Play Store API Key
3. Update `lib/services/revenue_cat_service.dart`:
   ```dart
   static const String _appleApiKey = 'YOUR_APPLE_API_KEY_HERE';
   static const String _googleApiKey = 'YOUR_GOOGLE_API_KEY_HERE';
   ```

### 1.3 Create Products in RevenueCat
1. Go to **Products** section
2. Create three products with these identifiers:
   - `tripwisego_weekly_1_00` - Weekly subscription ($1.00)
   - `tripwisego_monthly_3_00` - Monthly subscription ($3.00)
   - `tripwisego_quarterly_5_00` - Quarterly subscription ($5.00)

### 1.4 Create Entitlements
1. Go to **Entitlements** section
2. Create an entitlement named "premium_features"
3. Attach all three products to this entitlement

## Step 2: iOS Setup (App Store Connect)

### 2.1 App Store Connect Configuration
1. Log in to [App Store Connect](https://appstoreconnect.apple.com/)
2. Go to **My Apps** and select your TripWiseGo app
3. Navigate to **Features** → **In-App Purchases**

### 2.2 Create In-App Purchase Products
Create three auto-renewable subscriptions:

**Weekly Subscription:**
- Product ID: `tripwisego_weekly_1_00`
- Reference Name: `TripWiseGo Weekly Premium`
- Price: $0.99 USD (Tier 1)
- Subscription Group: Create new group "TripWiseGo Premium"

**Monthly Subscription:**
- Product ID: `tripwisego_monthly_3_00`
- Reference Name: `TripWiseGo Monthly Premium`
- Price: $2.99 USD (Tier 3)
- Subscription Group: Same as above

**Quarterly Subscription:**
- Product ID: `tripwisego_quarterly_5_00`
- Reference Name: `TripWiseGo Quarterly Premium`
- Price: $4.99 USD (Tier 5)
- Subscription Group: Same as above

### 2.3 Configure Subscription Group
1. Set up subscription group details
2. Configure family sharing settings
3. Add localized descriptions for each market

### 2.4 App Store Review Information
1. Add screenshots showing subscription features
2. Provide review notes explaining the subscription functionality
3. Include test account credentials for App Store Review

### 2.5 Testing Setup
1. Create sandbox test accounts in **Users and Access** → **Sandbox Testers**
2. Use these accounts for testing purchases
3. Test on physical devices (simulators don't support purchases)

## Step 3: Android Setup (Google Play Console)

### 3.1 Google Play Console Configuration
1. Log in to [Google Play Console](https://play.google.com/console/)
2. Select your TripWiseGo app
3. Go to **Monetize** → **Products** → **Subscriptions**

### 3.2 Create Subscription Products
Create three subscription products:

**Weekly Subscription:**
- Product ID: `tripwisego_weekly_1_00`
- Name: `TripWiseGo Weekly Premium`
- Price: $0.99 USD
- Billing period: 1 week
- Free trial: None

**Monthly Subscription:**
- Product ID: `tripwisego_monthly_3_00`
- Name: `TripWiseGo Monthly Premium`
- Price: $2.99 USD
- Billing period: 1 month
- Free trial: None

**Quarterly Subscription:**
- Product ID: `tripwisego_quarterly_5_00`
- Name: `TripWiseGo Quarterly Premium`
- Price: $4.99 USD
- Billing period: 3 months
- Free trial: None

### 3.3 Configure Base Plans
1. For each subscription, create a base plan
2. Set pricing for all supported countries
3. Configure renewal settings

### 3.4 Testing Setup
1. Create test tracks in **Release** → **Testing**
2. Add test users to internal testing track
3. Upload APK/AAB with subscription functionality
4. Test purchases using test accounts

## Step 4: RevenueCat Integration

### 4.1 Connect Store Accounts
1. In RevenueCat dashboard, go to **Integrations**
2. Connect your Apple App Store account:
   - Upload App Store Connect API Key
   - Configure bundle ID
3. Connect your Google Play account:
   - Upload Google Play service account JSON
   - Configure package name

### 4.2 Configure Webhooks (Optional)
1. Set up webhooks for subscription events
2. Configure endpoints for your backend (if applicable)

## Step 5: Supabase Database Setup

### 5.1 Create Required Tables
Execute these SQL commands in Supabase SQL Editor:

```sql
-- User swipe data table
CREATE TABLE user_swipe_data (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  date DATE NOT NULL,
  swipe_count INTEGER DEFAULT 0,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, date)
);

-- Enable RLS
ALTER TABLE user_swipe_data ENABLE ROW LEVEL SECURITY;

-- RLS policies
CREATE POLICY "Users can view own swipe data" ON user_swipe_data
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own swipe data" ON user_swipe_data
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own swipe data" ON user_swipe_data
  FOR UPDATE USING (auth.uid() = user_id);
```

### 5.2 Update Existing Subscription Table
```sql
-- Add RevenueCat integration fields to user_subscriptions table
ALTER TABLE user_subscriptions 
ADD COLUMN IF NOT EXISTS payment_method TEXT DEFAULT 'RevenueCat',
ADD COLUMN IF NOT EXISTS revenue_cat_customer_id TEXT,
ADD COLUMN IF NOT EXISTS revenue_cat_entitlement_id TEXT;
```

## Step 6: Testing Procedures

### 6.1 Development Testing
1. Use RevenueCat sandbox environment
2. Test with sandbox accounts
3. Verify subscription status updates
4. Test swipe limit enforcement

### 6.2 iOS Testing
1. Build app with development provisioning profile
2. Install on physical device
3. Sign in with sandbox test account
4. Test purchase flows
5. Test subscription restoration

### 6.3 Android Testing
1. Upload to internal testing track
2. Install from Play Store
3. Test with test account
4. Verify purchase flows
5. Test subscription management

### 6.4 Production Testing
1. Submit app for review
2. Test with real accounts (small amounts)
3. Monitor RevenueCat dashboard
4. Verify analytics and metrics

## Step 7: Deployment Checklist

### Pre-Launch
- [ ] All subscription products created and approved
- [ ] RevenueCat properly configured
- [ ] Database tables created
- [ ] Testing completed on both platforms
- [ ] App Store/Play Store metadata updated
- [ ] Privacy policy updated with subscription terms

### Post-Launch
- [ ] Monitor subscription metrics
- [ ] Track conversion rates
- [ ] Monitor for subscription issues
- [ ] Update pricing as needed
- [ ] Analyze user feedback

## Troubleshooting

### Common Issues
1. **Products not loading**: Check product IDs match exactly
2. **Purchases failing**: Verify store account connections
3. **Subscription not recognized**: Check RevenueCat webhook configuration
4. **Testing issues**: Ensure using correct sandbox/test accounts

### Support Resources
- [RevenueCat Documentation](https://docs.revenuecat.com/)
- [Apple In-App Purchase Guide](https://developer.apple.com/in-app-purchase/)
- [Google Play Billing Guide](https://developer.android.com/google/play/billing)

## Security Considerations

1. **API Keys**: Never commit API keys to version control
2. **Server Validation**: Consider server-side receipt validation for production
3. **User Data**: Ensure GDPR/privacy compliance
4. **Fraud Prevention**: Monitor for unusual subscription patterns

## Maintenance

1. **Regular Updates**: Keep RevenueCat SDK updated
2. **Price Testing**: A/B test different price points
3. **Feature Updates**: Regularly update premium features
4. **Analytics**: Monitor subscription metrics and user behavior
