import 'dart:convert';

/// Configuration for AI learning system
class LearningConfiguration {
  bool isLearningEnabled;
  Duration analysisInterval;
  int minFeedbackThreshold;
  double negativeThreshold;
  bool enablePromptOptimization;
  bool enableABTesting;
  int maxPromptVersions;
  double learningRate;

  LearningConfiguration({
    required this.isLearningEnabled,
    required this.analysisInterval,
    required this.minFeedbackThreshold,
    required this.negativeThreshold,
    required this.enablePromptOptimization,
    required this.enableABTesting,
    required this.maxPromptVersions,
    required this.learningRate,
  });

  /// Create default configuration
  factory LearningConfiguration.defaultConfig() {
    return LearningConfiguration(
      isLearningEnabled: true,
      analysisInterval: const Duration(hours: 6),
      minFeedbackThreshold: 10,
      negativeThreshold: 0.3,
      enablePromptOptimization: true,
      enableABTesting: false,
      maxPromptVersions: 5,
      learningRate: 0.1,
    );
  }

  /// Create from map
  factory LearningConfiguration.fromMap(Map<String, dynamic> map) {
    return LearningConfiguration(
      isLearningEnabled: map['isLearningEnabled'] ?? true,
      analysisInterval:
          Duration(milliseconds: map['analysisIntervalMs'] ?? 21600000),
      minFeedbackThreshold: map['minFeedbackThreshold'] ?? 10,
      negativeThreshold: map['negativeThreshold'] ?? 0.3,
      enablePromptOptimization: map['enablePromptOptimization'] ?? true,
      enableABTesting: map['enableABTesting'] ?? false,
      maxPromptVersions: map['maxPromptVersions'] ?? 5,
      learningRate: map['learningRate'] ?? 0.1,
    );
  }

  /// Convert to map
  Map<String, dynamic> toMap() {
    return {
      'isLearningEnabled': isLearningEnabled,
      'analysisIntervalMs': analysisInterval.inMilliseconds,
      'minFeedbackThreshold': minFeedbackThreshold,
      'negativeThreshold': negativeThreshold,
      'enablePromptOptimization': enablePromptOptimization,
      'enableABTesting': enableABTesting,
      'maxPromptVersions': maxPromptVersions,
      'learningRate': learningRate,
    };
  }
}

/// Data structure for storing learning analysis results
class LearningData {
  List<FeedbackAnalysis> analyses;
  DateTime? lastAnalysisTime;
  int currentPromptVersion;
  List<PromptVersion> promptVersions;
  Map<String, dynamic> performanceMetrics;

  LearningData({
    required this.analyses,
    this.lastAnalysisTime,
    required this.currentPromptVersion,
    required this.promptVersions,
    required this.performanceMetrics,
  });

  /// Create empty learning data
  factory LearningData.empty() {
    return LearningData(
      analyses: [],
      currentPromptVersion: 1,
      promptVersions: [],
      performanceMetrics: {},
    );
  }

  /// Create from map
  factory LearningData.fromMap(Map<String, dynamic> map) {
    return LearningData(
      analyses: (map['analyses'] as List<dynamic>?)
              ?.map((item) => FeedbackAnalysis.fromMap(item))
              .toList() ??
          [],
      lastAnalysisTime: map['lastAnalysisTime'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['lastAnalysisTime'])
          : null,
      currentPromptVersion: map['currentPromptVersion'] ?? 1,
      promptVersions: (map['promptVersions'] as List<dynamic>?)
              ?.map((item) => PromptVersion.fromMap(item))
              .toList() ??
          [],
      performanceMetrics:
          Map<String, dynamic>.from(map['performanceMetrics'] ?? {}),
    );
  }

  /// Convert to map
  Map<String, dynamic> toMap() {
    return {
      'analyses': analyses.map((analysis) => analysis.toMap()).toList(),
      'lastAnalysisTime': lastAnalysisTime?.millisecondsSinceEpoch,
      'currentPromptVersion': currentPromptVersion,
      'promptVersions':
          promptVersions.map((version) => version.toMap()).toList(),
      'performanceMetrics': performanceMetrics,
    };
  }

  /// Add new analysis
  void addAnalysis(FeedbackAnalysis analysis) {
    analyses.add(analysis);
    lastAnalysisTime = DateTime.now();

    // Keep only last 50 analyses to prevent storage bloat
    if (analyses.length > 50) {
      analyses.removeRange(0, analyses.length - 50);
    }
  }

  /// Get average quality score across all analyses
  double getAverageQualityScore() {
    if (analyses.isEmpty) return 0.0;

    final totalScore = analyses.fold(
        0.0, (sum, analysis) => sum + analysis.overallQualityScore);
    return totalScore / analyses.length;
  }

  /// Get latest analysis
  FeedbackAnalysis? getLatestAnalysis() {
    return analyses.isNotEmpty ? analyses.last : null;
  }
}

/// Analysis results from feedback data
class FeedbackAnalysis {
  int totalFeedback = 0;
  int positiveFeedback = 0;
  int negativeFeedback = 0;
  double positiveRatio = 0.0;
  List<String> commonFailures = [];
  Map<String, double> negativeReasons = {};
  List<String> successPatterns = [];
  Map<String, int> queryTypeAnalysis = {};
  Map<String, dynamic> contextualPatterns = {};
  double overallQualityScore = 0.0;
  double travelSpecificScore = 0.0;
  DateTime analysisTime = DateTime.now();

  FeedbackAnalysis();

  /// Create from map
  factory FeedbackAnalysis.fromMap(Map<String, dynamic> map) {
    final analysis = FeedbackAnalysis();
    analysis.totalFeedback = map['totalFeedback'] ?? 0;
    analysis.positiveFeedback = map['positiveFeedback'] ?? 0;
    analysis.negativeFeedback = map['negativeFeedback'] ?? 0;
    analysis.positiveRatio = map['positiveRatio'] ?? 0.0;
    analysis.commonFailures = List<String>.from(map['commonFailures'] ?? []);
    analysis.negativeReasons =
        Map<String, double>.from(map['negativeReasons'] ?? {});
    analysis.successPatterns = List<String>.from(map['successPatterns'] ?? []);
    analysis.queryTypeAnalysis =
        Map<String, int>.from(map['queryTypeAnalysis'] ?? {});
    analysis.contextualPatterns =
        Map<String, dynamic>.from(map['contextualPatterns'] ?? {});
    analysis.overallQualityScore = map['overallQualityScore'] ?? 0.0;
    analysis.travelSpecificScore = map['travelSpecificScore'] ?? 0.0;
    analysis.analysisTime = map['analysisTime'] != null
        ? DateTime.fromMillisecondsSinceEpoch(map['analysisTime'])
        : DateTime.now();
    return analysis;
  }

  /// Convert to map
  Map<String, dynamic> toMap() {
    return {
      'totalFeedback': totalFeedback,
      'positiveFeedback': positiveFeedback,
      'negativeFeedback': negativeFeedback,
      'positiveRatio': positiveRatio,
      'commonFailures': commonFailures,
      'negativeReasons': negativeReasons,
      'successPatterns': successPatterns,
      'queryTypeAnalysis': queryTypeAnalysis,
      'contextualPatterns': contextualPatterns,
      'overallQualityScore': overallQualityScore,
      'travelSpecificScore': travelSpecificScore,
      'analysisTime': analysisTime.millisecondsSinceEpoch,
    };
  }
}

/// Prompt version for A/B testing and rollback
class PromptVersion {
  int version;
  String promptText;
  DateTime createdAt;
  double performanceScore;
  int usageCount;
  bool isActive;
  Map<String, dynamic> metadata;

  PromptVersion({
    required this.version,
    required this.promptText,
    required this.createdAt,
    required this.performanceScore,
    required this.usageCount,
    required this.isActive,
    required this.metadata,
  });

  /// Create from map
  factory PromptVersion.fromMap(Map<String, dynamic> map) {
    return PromptVersion(
      version: map['version'],
      promptText: map['promptText'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
      performanceScore: map['performanceScore'] ?? 0.0,
      usageCount: map['usageCount'] ?? 0,
      isActive: map['isActive'] ?? false,
      metadata: Map<String, dynamic>.from(map['metadata'] ?? {}),
    );
  }

  /// Convert to map
  Map<String, dynamic> toMap() {
    return {
      'version': version,
      'promptText': promptText,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'performanceScore': performanceScore,
      'usageCount': usageCount,
      'isActive': isActive,
      'metadata': metadata,
    };
  }
}

/// Learning statistics for monitoring
class LearningStatistics {
  int totalAnalyses;
  DateTime? lastAnalysisTime;
  int currentPromptVersion;
  double averageQualityScore;
  bool learningEnabled;

  LearningStatistics({
    required this.totalAnalyses,
    this.lastAnalysisTime,
    required this.currentPromptVersion,
    required this.averageQualityScore,
    required this.learningEnabled,
  });
}

/// Learning dashboard data model
class LearningDashboard {
  SystemHealth systemHealth = SystemHealth();
  LearningEffectiveness learningEffectiveness = LearningEffectiveness();
  PromptPerformanceAnalysis promptPerformance = PromptPerformanceAnalysis();
  QualityTrends qualityTrends = QualityTrends();
  ConfigurationStatus configurationStatus = ConfigurationStatus();
  List<ActivityItem> recentActivity = [];
  List<String> recommendations = [];
}

/// System health metrics
class SystemHealth {
  double overallScore = 0.0;
  double servicesOnline = 0.0;
  double dataIntegrity = 0.0;
  double performanceScore = 0.0;
  double errorRate = 0.0;
  String status = 'Unknown';
  DateTime? lastChecked;
}

/// Learning effectiveness metrics
class LearningEffectiveness {
  double overallScore = 0.0;
  double qualityImprovement = 0.0;
  double learningRate = 0.0;
  double adaptationSuccessRate = 0.0;
  double userSatisfactionTrend = 0.0;
}

/// Prompt performance analysis
class PromptPerformanceAnalysis {
  List<VersionPerformance> versionPerformance = [];
  int bestPerformingVersion = 1;
  double bestPerformanceScore = 0.0;
  double improvementTrend = 0.0;
  double averagePerformance = 0.0;
}

/// Version performance data
class VersionPerformance {
  int version;
  double performanceScore;
  int usageCount;
  bool isActive;
  DateTime createdAt;

  VersionPerformance({
    required this.version,
    required this.performanceScore,
    required this.usageCount,
    required this.isActive,
    required this.createdAt,
  });
}

/// Quality trends analysis
class QualityTrends {
  String direction = 'stable';
  double strength = 0.0;
  double currentQuality = 0.0;
  double changeFromPrevious = 0.0;
  List<Map<String, dynamic>> historicalData = [];
}

/// Configuration status
class ConfigurationStatus {
  bool isOptimal = false;
  bool learningEnabled = false;
  Duration analysisInterval = Duration(hours: 6);
  bool optimizationEnabled = false;
  bool abTestingEnabled = false;
  List<String> recommendations = [];
}

/// Activity item for recent activity tracking
class ActivityItem {
  String type;
  String description;
  DateTime timestamp;
  String severity;

  ActivityItem({
    required this.type,
    required this.description,
    required this.timestamp,
    required this.severity,
  });
}
