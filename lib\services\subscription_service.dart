import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/subscription.dart';
import '../services/auth_service.dart';
import '../services/revenue_cat_service.dart';

class SubscriptionService {
  static const String _subscriptionKey = 'user_subscription';
  static const String _subscriptionTableName = 'user_subscriptions';

  static UserSubscription? _currentSubscription;
  static bool _isInitialized = false;

  /// Initialize the subscription service
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialize RevenueCat first
      await RevenueCatService.initialize();

      await _loadSubscriptionFromStorage();

      // If user is authenticated, sync with Supabase and RevenueCat
      if (AuthService.currentUser != null) {
        await _syncSubscriptionFromSupabase();
        await _syncWithRevenueCat();
      }

      _isInitialized = true;

      if (kDebugMode) {
        print('Subscription Service: Initialized');
        print(
            'Current subscription: ${_currentSubscription?.status ?? 'none'}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Subscription Service: Initialization failed - $e');
      }
    }
  }

  /// Ensure service is initialized
  static Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  /// Get current subscription status
  static Future<UserSubscription?> getCurrentSubscription() async {
    await _ensureInitialized();
    return _currentSubscription;
  }

  /// Check if user has an active subscription
  static Future<bool> hasActiveSubscription() async {
    await _ensureInitialized();

    // First check RevenueCat for real-time status
    try {
      final hasRevenueCatSubscription =
          await RevenueCatService.hasActiveSubscription();
      if (hasRevenueCatSubscription) {
        return true;
      }
    } catch (e) {
      if (kDebugMode) {
        print('Subscription Service: Error checking RevenueCat status - $e');
      }
    }

    // Fallback to local subscription data
    return _currentSubscription?.hasActiveSubscription ?? false;
  }

  /// Check if user is in trial period
  static Future<bool> isInTrial() async {
    await _ensureInitialized();
    return _currentSubscription?.isInTrial ?? false;
  }

  /// Get subscription plan
  static Future<SubscriptionPlan> getSubscriptionPlan() async {
    await _ensureInitialized();
    return _currentSubscription?.plan ?? SubscriptionPlan.none;
  }

  /// Subscribe to a plan using RevenueCat
  static Future<bool> subscribeToPlan(SubscriptionTier tier) async {
    try {
      await _ensureInitialized();

      final userId = AuthService.currentUser?.id;
      if (userId == null) {
        if (kDebugMode) {
          print('Subscription Service: User not authenticated');
        }
        return false;
      }

      // Make the purchase through RevenueCat
      final customerInfo = await RevenueCatService.purchaseSubscription(
          tier.revenueCatProductId);
      if (customerInfo == null) {
        if (kDebugMode) {
          print('Subscription Service: Purchase failed');
        }
        return false;
      }

      // Create subscription record from RevenueCat data
      final now = DateTime.now();
      final endDate = _calculateEndDate(now, tier.duration);
      final activeEntitlement =
          customerInfo.entitlements.active.values.isNotEmpty
              ? customerInfo.entitlements.active.values.first
              : null;

      final subscription = UserSubscription(
        id: activeEntitlement?.identifier ??
            DateTime.now().millisecondsSinceEpoch.toString(),
        userId: userId,
        plan: tier.plan,
        tier: tier,
        startDate: now,
        endDate: activeEntitlement?.expirationDate != null
            ? DateTime.parse(activeEntitlement!.expirationDate!)
            : endDate,
        isActive: true,
        isInTrial:
            activeEntitlement?.periodType.toString() == 'PeriodType.trial',
        trialEndDate:
            activeEntitlement?.periodType.toString() == 'PeriodType.trial'
                ? (activeEntitlement?.expirationDate != null
                    ? DateTime.parse(activeEntitlement!.expirationDate!)
                    : null)
                : null,
        status: 'active',
        nextBillingDate: activeEntitlement?.expirationDate != null
            ? DateTime.parse(activeEntitlement!.expirationDate!)
            : null,
        paymentMethod: 'RevenueCat',
      );

      // Save to Supabase
      final success = await _saveSubscriptionToSupabase(subscription);
      if (success) {
        _currentSubscription = subscription;
        await _saveSubscriptionToStorage();

        if (kDebugMode) {
          print(
              'Subscription Service: Successfully subscribed to ${tier.name}');
        }
        return true;
      }

      return false;
    } catch (e) {
      if (kDebugMode) {
        print('Subscription Service: Failed to subscribe - $e');
      }
      return false;
    }
  }

  /// Cancel subscription
  static Future<bool> cancelSubscription() async {
    try {
      await _ensureInitialized();

      if (_currentSubscription == null) return false;

      final cancelledSubscription = _currentSubscription!.copyWith(
        isActive: false,
        status: 'cancelled',
      );

      // Save to Supabase
      final success = await _saveSubscriptionToSupabase(cancelledSubscription);
      if (success) {
        _currentSubscription = cancelledSubscription;
        await _saveSubscriptionToStorage();

        if (kDebugMode) {
          print('Subscription Service: Successfully cancelled subscription');
        }
        return true;
      }

      return false;
    } catch (e) {
      if (kDebugMode) {
        print('Subscription Service: Failed to cancel subscription - $e');
      }
      return false;
    }
  }

  /// Load subscription from local storage
  static Future<void> _loadSubscriptionFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final subscriptionJson = prefs.getString(_subscriptionKey);

      if (subscriptionJson != null) {
        final subscriptionData = json.decode(subscriptionJson);
        _currentSubscription = UserSubscription.fromJson(subscriptionData);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Subscription Service: Failed to load from storage - $e');
      }
    }
  }

  /// Save subscription to local storage
  static Future<void> _saveSubscriptionToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      if (_currentSubscription != null) {
        final subscriptionJson = json.encode(_currentSubscription!.toJson());
        await prefs.setString(_subscriptionKey, subscriptionJson);
      } else {
        await prefs.remove(_subscriptionKey);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Subscription Service: Failed to save to storage - $e');
      }
    }
  }

  /// Sync subscription from Supabase
  static Future<void> _syncSubscriptionFromSupabase() async {
    try {
      final userId = AuthService.currentUser?.id;
      if (userId == null) return;

      final response = await Supabase.instance.client
          .from(_subscriptionTableName)
          .select()
          .eq('user_id', userId)
          .order('created_at', ascending: false)
          .limit(1);

      if (response.isNotEmpty) {
        final subscriptionData = response.first;
        _currentSubscription = UserSubscription.fromJson(subscriptionData);
        await _saveSubscriptionToStorage();

        if (kDebugMode) {
          print('Subscription Service: Synced from Supabase');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Subscription Service: Failed to sync from Supabase - $e');
      }
    }
  }

  /// Save subscription to Supabase
  static Future<bool> _saveSubscriptionToSupabase(
      UserSubscription subscription) async {
    try {
      final subscriptionData = subscription.toJson();
      subscriptionData['user_id'] = subscription.userId;
      subscriptionData['created_at'] = DateTime.now().toIso8601String();
      subscriptionData['updated_at'] = DateTime.now().toIso8601String();

      await Supabase.instance.client
          .from(_subscriptionTableName)
          .upsert(subscriptionData);

      if (kDebugMode) {
        print('Subscription Service: Saved to Supabase');
      }
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Subscription Service: Failed to save to Supabase - $e');
      }
      return false;
    }
  }

  /// Calculate end date based on duration
  static DateTime _calculateEndDate(
      DateTime startDate, SubscriptionDuration duration) {
    switch (duration) {
      case SubscriptionDuration.weekly:
        return startDate.add(const Duration(days: 7));
      case SubscriptionDuration.monthly:
        return startDate.add(const Duration(days: 30));
      case SubscriptionDuration.quarterly:
        return startDate.add(const Duration(days: 90));
      case SubscriptionDuration.biannual:
        return startDate.add(const Duration(days: 150));
    }
  }

  /// Clear subscription data (for logout)
  static Future<void> clearSubscriptionData() async {
    _currentSubscription = null;
    _isInitialized = false;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_subscriptionKey);
    } catch (e) {
      if (kDebugMode) {
        print('Subscription Service: Failed to clear data - $e');
      }
    }
  }

  /// Get available subscription tiers
  static List<SubscriptionTier> getAvailableTiers() {
    return SubscriptionTier.availableTiers;
  }

  /// Check if plan access is allowed
  static Future<bool> canAccessPlanFeatures() async {
    return await hasActiveSubscription();
  }

  /// Get days remaining in subscription
  static Future<int> getDaysRemaining() async {
    await _ensureInitialized();
    return _currentSubscription?.daysUntilExpiry ?? 0;
  }

  /// Update subscription status (for testing)
  static Future<void> updateSubscriptionStatus(String status) async {
    if (_currentSubscription != null) {
      _currentSubscription = _currentSubscription!.copyWith(status: status);
      await _saveSubscriptionToStorage();
    }
  }

  /// Sync subscription data with RevenueCat
  static Future<void> _syncWithRevenueCat() async {
    try {
      final userId = AuthService.currentUser?.id;
      if (userId == null) return;

      // Login user to RevenueCat
      await RevenueCatService.loginUser(userId);

      // Get customer info from RevenueCat
      final customerInfo = await RevenueCatService.getCustomerInfo();
      if (customerInfo == null) return;

      // Check if user has active subscription in RevenueCat
      if (customerInfo.entitlements.active.isNotEmpty) {
        final activeEntitlement = customerInfo.entitlements.active.values.first;

        // Find matching tier
        SubscriptionTier? matchingTier;
        for (final tier in SubscriptionTier.availableTiers) {
          if (tier.revenueCatProductId == activeEntitlement.productIdentifier) {
            matchingTier = tier;
            break;
          }
        }

        if (matchingTier != null) {
          final now = DateTime.now();
          final subscription = UserSubscription(
            id: activeEntitlement.identifier,
            userId: userId,
            plan: matchingTier.plan,
            tier: matchingTier,
            startDate: now,
            endDate: activeEntitlement.expirationDate != null
                ? DateTime.parse(activeEntitlement.expirationDate!)
                : _calculateEndDate(now, matchingTier.duration),
            isActive: true,
            isInTrial:
                activeEntitlement.periodType.toString() == 'PeriodType.trial',
            trialEndDate:
                activeEntitlement.periodType.toString() == 'PeriodType.trial'
                    ? (activeEntitlement.expirationDate != null
                        ? DateTime.parse(activeEntitlement.expirationDate!)
                        : null)
                    : null,
            status: 'active',
            nextBillingDate: activeEntitlement.expirationDate != null
                ? DateTime.parse(activeEntitlement.expirationDate!)
                : null,
            paymentMethod: 'RevenueCat',
          );

          _currentSubscription = subscription;
          await _saveSubscriptionToStorage();
          await _saveSubscriptionToSupabase(subscription);

          if (kDebugMode) {
            print('Subscription Service: Synced with RevenueCat');
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Subscription Service: Failed to sync with RevenueCat - $e');
      }
    }
  }

  /// Restore purchases from RevenueCat
  static Future<bool> restorePurchases() async {
    try {
      await _ensureInitialized();

      final customerInfo = await RevenueCatService.restorePurchases();
      if (customerInfo != null) {
        await _syncWithRevenueCat();
        return true;
      }

      return false;
    } catch (e) {
      if (kDebugMode) {
        print('Subscription Service: Failed to restore purchases - $e');
      }
      return false;
    }
  }
}
