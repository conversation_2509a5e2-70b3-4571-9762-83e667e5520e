import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:googleapis/sheets/v4.dart';
import 'package:googleapis_auth/auth_io.dart';
import '../models/feedback_models.dart';

class GoogleSheetsService {
  static const String _spreadsheetId =
      '18_dYxomtS1cjaKi6hWTiNHuELOcjnunf-2qOotPML4s';
  static const String _sheetName =
      'AI-Training'; // Using default sheet name first
  static const List<String> _scopes = [SheetsApi.spreadsheetsScope];

  static SheetsApi? _sheetsApi;
  static bool _isInitialized = false;

  /// Initialize the Google Sheets service
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Load service account credentials from assets
      final credentialsJson = await rootBundle
          .loadString('assets/credentials/service_account.json');
      final credentials =
          ServiceAccountCredentials.fromJson(json.decode(credentialsJson));

      // Create authenticated client
      final client = await clientViaServiceAccount(credentials, _scopes);

      // Initialize Sheets API
      _sheetsApi = SheetsApi(client);
      _isInitialized = true;

      if (kDebugMode) {
        print('Google Sheets Service: Initialized successfully');
      }

      // Ensure the sheet exists and has proper headers
      await _ensureSheetSetup();
    } catch (e) {
      if (kDebugMode) {
        print('Google Sheets Service: Initialization failed: $e');
      }
      // Don't set _isInitialized to true if initialization fails
      rethrow;
    }
  }

  /// Ensure the sheet exists and has proper headers
  static Future<void> _ensureSheetSetup() async {
    if (_sheetsApi == null) return;

    try {
      // Get spreadsheet metadata
      final spreadsheet = await _sheetsApi!.spreadsheets.get(_spreadsheetId);

      if (kDebugMode) {
        print(
            'Google Sheets Service: Found spreadsheet with ${spreadsheet.sheets?.length ?? 0} sheets');
        for (final sheet in spreadsheet.sheets ?? []) {
          print(
              '  - Sheet: "${sheet.properties?.title}" (ID: ${sheet.properties?.sheetId})');
        }
      }

      // Check if our target sheet exists
      final targetSheet = spreadsheet.sheets?.firstWhere(
        (sheet) => sheet.properties?.title == _sheetName,
        orElse: () {
          if (spreadsheet.sheets?.isNotEmpty == true) {
            return spreadsheet.sheets!.first;
          }
          throw StateError('No sheets found');
        },
      );

      if (targetSheet?.properties?.title != _sheetName) {
        if (kDebugMode) {
          print(
              'Google Sheets Service: Target sheet "$_sheetName" not found, using "${targetSheet?.properties?.title}"');
        }
        // Use the first available sheet and add headers
        await _ensureHeaders();
      } else {
        // Check if headers exist, if not add them
        await _ensureHeaders();
      }
    } catch (e) {
      if (kDebugMode) {
        print('Google Sheets Service: Sheet setup failed: $e');
      }
      // Try to add headers to the first sheet anyway
      await _ensureHeaders();
    }
  }

  /// Create the feedback sheet with headers
  static Future<void> _createFeedbackSheet() async {
    if (_sheetsApi == null) return;

    try {
      // Add new sheet
      final addSheetRequest = AddSheetRequest(
        properties: SheetProperties(
          title: _sheetName,
        ),
      );

      final batchUpdateRequest = BatchUpdateSpreadsheetRequest(
        requests: [
          Request(addSheet: addSheetRequest),
        ],
      );

      await _sheetsApi!.spreadsheets
          .batchUpdate(batchUpdateRequest, _spreadsheetId);

      // Add headers
      await _addHeaders();

      if (kDebugMode) {
        print('Google Sheets Service: Created feedback sheet with headers');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Google Sheets Service: Failed to create sheet: $e');
      }
    }
  }

  /// Add headers to the sheet
  static Future<void> _addHeaders() async {
    if (_sheetsApi == null) return;

    final headers = [
      'ID',
      'User ID',
      'User Message',
      'AI Response',
      'Feedback Type',
      'Feedback Reason',
      'Custom Feedback',
      'Timestamp',
      'Conversation Context',
      'Session ID',
      'Is Synced',
      'Message ID',
    ];

    final valueRange = ValueRange(
      values: [headers],
    );

    try {
      await _sheetsApi!.spreadsheets.values.update(
        valueRange,
        _spreadsheetId,
        '$_sheetName!A1:L1',
        valueInputOption: 'RAW',
      );

      if (kDebugMode) {
        print('Google Sheets Service: Added headers to sheet');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Google Sheets Service: Failed to add headers: $e');
      }
    }
  }

  /// Ensure headers exist in the sheet
  static Future<void> _ensureHeaders() async {
    if (_sheetsApi == null) return;

    try {
      // Check if first row has headers
      final response = await _sheetsApi!.spreadsheets.values.get(
        _spreadsheetId,
        '$_sheetName!A1:L1',
      );

      if (response.values == null || response.values!.isEmpty) {
        await _addHeaders();
      }
    } catch (e) {
      if (kDebugMode) {
        print('Google Sheets Service: Failed to check headers: $e');
      }
      // Try to add headers anyway
      await _addHeaders();
    }
  }

  /// Submit feedback to Google Sheets
  static Future<bool> submitFeedback(AIResponseFeedback feedback) async {
    if (!_isInitialized || _sheetsApi == null) {
      if (kDebugMode) {
        print('Google Sheets Service: Not initialized');
      }
      return false;
    }

    try {
      final row = feedback.toCsvRow();

      if (kDebugMode) {
        print(
            'Google Sheets Service: Submitting feedback with data: ${row.take(3)}...');
        print('Google Sheets Service: Target range: $_sheetName!A:L');
      }

      final valueRange = ValueRange(
        values: [row],
      );

      final response = await _sheetsApi!.spreadsheets.values.append(
        valueRange,
        _spreadsheetId,
        '$_sheetName!A:L',
        valueInputOption: 'RAW',
        insertDataOption: 'INSERT_ROWS',
      );

      if (kDebugMode) {
        print(
            'Google Sheets Service: Feedback submitted successfully - ${feedback.id}');
        print(
            'Google Sheets Service: Response: ${response.updates?.updatedRows} rows updated');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Google Sheets Service: Failed to submit feedback: $e');
        if (e.toString().contains('PERMISSION_DENIED')) {
          print(
              'Google Sheets Service: Permission denied - check service account access to spreadsheet');
        }
        if (e.toString().contains('NOT_FOUND')) {
          print(
              'Google Sheets Service: Sheet not found - check sheet name "$_sheetName"');
        }
      }
      return false;
    }
  }

  /// Submit multiple feedback entries to Google Sheets
  static Future<bool> submitMultipleFeedback(
      List<AIResponseFeedback> feedbackList) async {
    if (!_isInitialized || _sheetsApi == null) {
      if (kDebugMode) {
        print('Google Sheets Service: Not initialized');
      }
      return false;
    }

    if (feedbackList.isEmpty) return true;

    try {
      final rows = feedbackList.map((feedback) => feedback.toCsvRow()).toList();

      final valueRange = ValueRange(
        values: rows,
      );

      await _sheetsApi!.spreadsheets.values.append(
        valueRange,
        _spreadsheetId,
        '$_sheetName!A:L',
        valueInputOption: 'RAW',
        insertDataOption: 'INSERT_ROWS',
      );

      if (kDebugMode) {
        print(
            'Google Sheets Service: ${feedbackList.length} feedback entries submitted successfully');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Google Sheets Service: Failed to submit multiple feedback: $e');
      }
      return false;
    }
  }

  /// Read all feedback from Google Sheets
  static Future<List<AIResponseFeedback>> readAllFeedback() async {
    if (!_isInitialized || _sheetsApi == null) {
      if (kDebugMode) {
        print('Google Sheets Service: Not initialized');
      }
      return [];
    }

    try {
      final response = await _sheetsApi!.spreadsheets.values.get(
        _spreadsheetId,
        '$_sheetName!A2:L', // Skip header row
      );

      if (response.values == null || response.values!.isEmpty) {
        return [];
      }

      final feedbackList = <AIResponseFeedback>[];

      for (final row in response.values!) {
        try {
          // Ensure row has enough columns
          final paddedRow = List<String>.from(row.map((e) => e.toString()));
          while (paddedRow.length < 12) {
            paddedRow.add('');
          }

          final feedback = AIResponseFeedback.fromCsvRow(paddedRow);
          feedbackList.add(feedback);
        } catch (e) {
          if (kDebugMode) {
            print('Google Sheets Service: Failed to parse row: $e');
          }
        }
      }

      if (kDebugMode) {
        print(
            'Google Sheets Service: Read ${feedbackList.length} feedback entries');
      }

      return feedbackList;
    } catch (e) {
      if (kDebugMode) {
        print('Google Sheets Service: Failed to read feedback: $e');
      }
      return [];
    }
  }

  /// Check if service is initialized and ready
  static bool get isReady => _isInitialized && _sheetsApi != null;

  /// Get the sheets API instance (for use by other services)
  static SheetsApi? get sheetsApi => _sheetsApi;

  /// Get the spreadsheet URL for viewing
  static String get spreadsheetUrl =>
      'https://docs.google.com/spreadsheets/d/$_spreadsheetId';
}
