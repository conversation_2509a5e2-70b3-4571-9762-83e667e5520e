import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../services/enhanced_content_service.dart';
import '../widgets/enhanced_content_display.dart';
import '../generated/l10n/app_localizations.dart';

/// Screen to display all enhanced content items
class AllEnhancedContentScreen extends StatefulWidget {
  final List<EnhancedContentItem> enhancedContent;

  const AllEnhancedContentScreen({
    super.key,
    required this.enhancedContent,
  });

  @override
  State<AllEnhancedContentScreen> createState() =>
      _AllEnhancedContentScreenState();
}

class _AllEnhancedContentScreenState extends State<AllEnhancedContentScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  List<EnhancedContentItem> _filteredContent = [];
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _filteredContent = widget.enhancedContent;

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _filterContent(String query) {
    setState(() {
      _searchQuery = query;
      if (query.isEmpty) {
        _filteredContent = widget.enhancedContent;
      } else {
        _filteredContent = widget.enhancedContent.where((item) {
          return item.content.title
                  .toLowerCase()
                  .contains(query.toLowerCase()) ||
              item.content.rawContent
                  .toLowerCase()
                  .contains(query.toLowerCase());
        }).toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F9FC),
      appBar: AppBar(
        backgroundColor: const Color(0xFF0D76FF),
        surfaceTintColor: const Color(0xFFF7F9FC),
        foregroundColor: Colors.white,
        title: Text(
          'AI-Generated Content',
          style: GoogleFonts.instrumentSans(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        elevation: 0,
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: Column(
            children: [
              // Search bar
              Container(
                padding: const EdgeInsets.all(16),
                color: const Color(0xFF0D76FF),
                child: TextField(
                  controller: _searchController,
                  onChanged: _filterContent,
                  style: GoogleFonts.instrumentSans(
                    fontSize: 14,
                    color: Colors.white,
                  ),
                  decoration: InputDecoration(
                    hintText: 'Search content...',
                    hintStyle: GoogleFonts.instrumentSans(
                      fontSize: 14,
                      color: Colors.white.withOpacity(0.7),
                    ),
                    prefixIcon: const Icon(
                      Icons.search,
                      color: Colors.white,
                    ),
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            icon: const Icon(
                              Icons.clear,
                              color: Colors.white,
                            ),
                            onPressed: () {
                              _searchController.clear();
                              _filterContent('');
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide.none,
                    ),
                    filled: true,
                    fillColor: Colors.white.withOpacity(0.2),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                ),
              ),

              // Content list
              Expanded(
                child: _filteredContent.isEmpty
                    ? _buildEmptyState()
                    : ListView.builder(
                        padding: const EdgeInsets.all(16),
                        itemCount: _filteredContent.length,
                        itemBuilder: (context, index) {
                          final contentItem = _filteredContent[index];
                          return Padding(
                            padding: const EdgeInsets.only(bottom: 16),
                            child: EnhancedContentDisplay(
                              content: contentItem.content,
                              onTap: () => _showContentDetail(contentItem),
                            ),
                          );
                        },
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            _searchQuery.isEmpty ? 'No Content Available' : 'No Results Found',
            style: GoogleFonts.instrumentSans(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF2D3748),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _searchQuery.isEmpty
                ? 'Start chatting with Wanderly AI to generate content!'
                : 'Try adjusting your search terms',
            textAlign: TextAlign.center,
            style: GoogleFonts.instrumentSans(
              fontSize: 14,
              color: const Color(0xFF718096),
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  void _showContentDetail(EnhancedContentItem contentItem) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        surfaceTintColor: const Color(0xFFF7F9FC),
        child: Container(
          constraints: const BoxConstraints(maxHeight: 600),
          child: EnhancedContentDisplay(
            content: contentItem.content,
            isCompact: false,
          ),
        ),
      ),
    );
  }
}
