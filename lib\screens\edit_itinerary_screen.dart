import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import '../models/itinerary.dart';
import '../services/itinerary_service.dart';

class EditItineraryScreen extends StatefulWidget {
  final Itinerary itinerary;

  const EditItineraryScreen({
    super.key,
    required this.itinerary,
  });

  @override
  State<EditItineraryScreen> createState() => _EditItineraryScreenState();
}

class _EditItineraryScreenState extends State<EditItineraryScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  // Form controllers
  late TextEditingController _titleController;
  late TextEditingController _accommodationController;
  late TextEditingController _notesController;

  // Form state
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;
  bool _hasChanges = false;

  // Date state
  DateTime? _startDate;
  DateTime? _endDate;

  // Activities state - using day-based structure
  late Map<int, Map<String, List<String>>> _editableDayActivities;
  int _totalDays = 1;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _initializeDates();
    _initializeActivities();
    _initializeAnimations();
  }

  void _initializeControllers() {
    _titleController = TextEditingController(text: widget.itinerary.title);
    _accommodationController =
        TextEditingController(text: widget.itinerary.accommodation ?? '');
    _notesController =
        TextEditingController(text: widget.itinerary.additionalNotes ?? '');

    // Add listeners to detect changes
    _titleController.addListener(_onFormChanged);
    _accommodationController.addListener(_onFormChanged);
    _notesController.addListener(_onFormChanged);
  }

  void _initializeDates() {
    try {
      // Parse DD/MM/YYYY format
      final startParts = widget.itinerary.startDate.split('/');
      final endParts = widget.itinerary.endDate.split('/');

      if (startParts.length == 3 && endParts.length == 3) {
        _startDate = DateTime(
          int.parse(startParts[2]), // year
          int.parse(startParts[1]), // month
          int.parse(startParts[0]), // day
        );
        _endDate = DateTime(
          int.parse(endParts[2]), // year
          int.parse(endParts[1]), // month
          int.parse(endParts[0]), // day
        );
      }
    } catch (e) {
      // If parsing fails, use current date
      _startDate = DateTime.now();
      _endDate = DateTime.now().add(const Duration(days: 1));
    }
  }

  void _initializeActivities() {
    // Calculate total days
    _totalDays = _calculateTotalDays();

    // Initialize day-based activities structure
    _editableDayActivities = <int, Map<String, List<String>>>{};

    // Check if the itinerary uses day-specific activities
    if (widget.itinerary.usesDaySpecificActivities &&
        widget.itinerary.daySpecificActivities != null) {
      // Use existing day-specific structure
      for (int day = 1; day <= _totalDays; day++) {
        _editableDayActivities[day] = Map<String, List<String>>.from(
            widget.itinerary.getActivitiesForDay(day));
      }
    } else {
      // Convert legacy format to day-based structure
      // For backward compatibility, put all activities in Day 1
      _editableDayActivities[1] =
          Map<String, List<String>>.from(widget.itinerary.dailyActivities);

      // Initialize empty maps for other days
      for (int day = 2; day <= _totalDays; day++) {
        _editableDayActivities[day] = <String, List<String>>{};
        // Initialize empty lists for each destination
        for (final destination in widget.itinerary.destinations) {
          _editableDayActivities[day]![destination] = <String>[];
        }
      }
    }

    // Ensure all days have entries for all destinations
    for (int day = 1; day <= _totalDays; day++) {
      for (final destination in widget.itinerary.destinations) {
        _editableDayActivities[day]!.putIfAbsent(destination, () => <String>[]);
      }
    }
  }

  int _calculateTotalDays() {
    if (_startDate != null && _endDate != null) {
      return _endDate!.difference(_startDate!).inDays + 1;
    }
    return 1;
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _fadeController.forward();
  }

  void _onFormChanged() {
    if (!_hasChanges) {
      setState(() {
        _hasChanges = true;
      });
    }
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _titleController.dispose();
    _accommodationController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F9FC),
      appBar: AppBar(
        backgroundColor: const Color(0xFFF7F9FC),
        elevation: 0,
        leading: IconButton(
          onPressed: _onBackPressed,
          icon: const Icon(
            Icons.arrow_back_ios,
            color: Color(0xFF2D3748),
          ),
        ),
        title: Text(
          'Edit Itinerary',
          style: GoogleFonts.instrumentSans(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: const Color(0xFF2D3748),
          ),
        ),
        centerTitle: true,
        actions: [
          if (_hasChanges)
            TextButton(
              onPressed: _isLoading ? null : _saveChanges,
              child: Text(
                'Save',
                style: GoogleFonts.instrumentSans(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: _isLoading
                      ? const Color(0xFF718096)
                      : const Color(0xFF0D76FF),
                ),
              ),
            ),
        ],
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: _isLoading
            ? const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF0D76FF)),
                ),
              )
            : Form(
                key: _formKey,
                child: SingleChildScrollView(
                  physics: const BouncingScrollPhysics(),
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildTitleSection(),
                      const SizedBox(height: 24),
                      _buildDatesSection(),
                      const SizedBox(height: 24),
                      _buildActivitiesSection(),
                      const SizedBox(height: 24),
                      _buildAccommodationSection(),
                      const SizedBox(height: 24),
                      _buildNotesSection(),
                      const SizedBox(height: 32),
                    ],
                  ),
                ),
              ),
      ),
    );
  }

  Widget _buildTitleSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.edit,
                color: Color(0xFF0D76FF),
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Itinerary Title',
                style: GoogleFonts.instrumentSans(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF2D3748),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _titleController,
            style: GoogleFonts.instrumentSans(
              fontSize: 16,
              color: const Color(0xFF2D3748),
            ),
            decoration: InputDecoration(
              hintText: 'Enter itinerary title',
              hintStyle: GoogleFonts.instrumentSans(
                fontSize: 16,
                color: const Color(0xFF718096),
              ),
              filled: true,
              fillColor: const Color(0xFFF7F9FC),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: Color(0xFFE2E8F0)),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: Color(0xFFE2E8F0)),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide:
                    const BorderSide(color: Color(0xFF0D76FF), width: 2),
              ),
              contentPadding: const EdgeInsets.all(16),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Please enter an itinerary title';
              }
              return null;
            },
            maxLength: 100,
            buildCounter: (context,
                {required currentLength, required isFocused, maxLength}) {
              return Text(
                '$currentLength/$maxLength',
                style: GoogleFonts.instrumentSans(
                  fontSize: 12,
                  color: const Color(0xFF718096),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDatesSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.calendar_today,
                color: Color(0xFF0D76FF),
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Trip Dates',
                style: GoogleFonts.instrumentSans(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF2D3748),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildDateField(
                  label: 'Start Date',
                  date: _startDate,
                  onTap: () => _selectStartDate(),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildDateField(
                  label: 'End Date',
                  date: _endDate,
                  onTap: () => _selectEndDate(),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDateField({
    required String label,
    required DateTime? date,
    required VoidCallback onTap,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: GoogleFonts.instrumentSans(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF2D3748),
          ),
        ),
        const SizedBox(height: 8),
        GestureDetector(
          onTap: onTap,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFFF7F9FC),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: const Color(0xFFE2E8F0)),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.calendar_month,
                  color: Color(0xFF718096),
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  date != null ? _formatDate(date) : 'Select date',
                  style: GoogleFonts.instrumentSans(
                    fontSize: 16,
                    color: date != null
                        ? const Color(0xFF2D3748)
                        : const Color(0xFF718096),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  Future<void> _selectStartDate() async {
    final picked = await showDatePicker(
      context: context,
      initialDate: _startDate ?? DateTime.now(),
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365 * 2)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: Color(0xFF0D76FF),
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Color(0xFF2D3748),
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _startDate = picked;
        // Ensure end date is not before start date
        if (_endDate != null && _endDate!.isBefore(picked)) {
          _endDate = picked.add(const Duration(days: 1));
        }
        // Recalculate total days and reinitialize activities
        final newTotalDays = _calculateTotalDays();
        if (newTotalDays != _totalDays) {
          _initializeActivities();
        }
        _onFormChanged();
      });
    }
  }

  Future<void> _selectEndDate() async {
    final picked = await showDatePicker(
      context: context,
      initialDate: _endDate ??
          (_startDate?.add(const Duration(days: 1)) ??
              DateTime.now().add(const Duration(days: 1))),
      firstDate: _startDate ?? DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365 * 2)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: Color(0xFF0D76FF),
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Color(0xFF2D3748),
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _endDate = picked;
        // Recalculate total days and reinitialize activities
        final newTotalDays = _calculateTotalDays();
        if (newTotalDays != _totalDays) {
          _initializeActivities();
        }
        _onFormChanged();
      });
    }
  }

  Widget _buildActivitiesSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.location_on,
                color: Color(0xFF0D76FF),
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Activities Management',
                style: GoogleFonts.instrumentSans(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF2D3748),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // Display activities organized by day
          ...List.generate(_totalDays, (index) {
            final day = index + 1;
            return _buildDayActivities(day);
          }),
        ],
      ),
    );
  }

  Widget _buildDayActivities(int day) {
    final dayActivities = _editableDayActivities[day] ?? {};
    final dayDate = _startDate?.add(Duration(days: day - 1));

    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFF7F9FC),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE2E8F0)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Day header
          Row(
            children: [
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: const Color(0xFF0D76FF),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  'Day $day',
                  style: GoogleFonts.instrumentSans(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
              if (dayDate != null) ...[
                const SizedBox(width: 12),
                Text(
                  _formatDate(dayDate),
                  style: GoogleFonts.instrumentSans(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: const Color(0xFF718096),
                  ),
                ),
              ],
            ],
          ),
          const SizedBox(height: 16),
          // Activities for each destination on this day
          ...widget.itinerary.destinations.map((destination) {
            final activities = dayActivities[destination] ?? [];
            return _buildDestinationActivities(day, destination, activities);
          }),
        ],
      ),
    );
  }

  Widget _buildDestinationActivities(
      int day, String destination, List<String> activities) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFFE2E8F0)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            destination,
            style: GoogleFonts.instrumentSans(
              fontSize: 15,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF2D3748),
            ),
          ),
          const SizedBox(height: 8),
          ...activities.asMap().entries.map((entry) {
            final index = entry.key;
            final activity = entry.value;
            return _buildActivityItem(day, destination, activity, index);
          }),
          const SizedBox(height: 8),
          GestureDetector(
            onTap: () => _addActivity(day, destination),
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: const Color(0xFF0D76FF).withOpacity(0.1),
                borderRadius: BorderRadius.circular(6),
                border: Border.all(
                  color: const Color(0xFF0D76FF).withOpacity(0.3),
                  style: BorderStyle.solid,
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.add,
                    color: Color(0xFF0D76FF),
                    size: 16,
                  ),
                  const SizedBox(width: 6),
                  Text(
                    'Add Activity',
                    style: GoogleFonts.instrumentSans(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: const Color(0xFF0D76FF),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActivityItem(
      int day, String destination, String activity, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 6),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: const Color(0xFFF7F9FC),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: const Color(0xFFE2E8F0)),
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              activity,
              style: GoogleFonts.instrumentSans(
                fontSize: 13,
                color: const Color(0xFF2D3748),
              ),
            ),
          ),
          IconButton(
            onPressed: () => _editActivity(day, destination, activity, index),
            icon: const Icon(
              Icons.edit,
              color: Color(0xFF0D76FF),
              size: 16,
            ),
            constraints: const BoxConstraints(),
            padding: const EdgeInsets.all(2),
          ),
          IconButton(
            onPressed: () => _removeActivity(day, destination, index),
            icon: const Icon(
              Icons.delete,
              color: Colors.red,
              size: 16,
            ),
            constraints: const BoxConstraints(),
            padding: const EdgeInsets.all(2),
          ),
        ],
      ),
    );
  }

  void _addActivity(int day, String destination) {
    showDialog(
      context: context,
      builder: (context) {
        String newActivity = '';
        return AlertDialog(
          title: Text(
            'Add Activity - Day $day',
            style: GoogleFonts.instrumentSans(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF2D3748),
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Destination: $destination',
                style: GoogleFonts.instrumentSans(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: const Color(0xFF718096),
                ),
              ),
              const SizedBox(height: 12),
              TextFormField(
                autofocus: true,
                style: GoogleFonts.instrumentSans(
                  fontSize: 16,
                  color: const Color(0xFF2D3748),
                ),
                decoration: InputDecoration(
                  hintText: 'Enter activity name',
                  hintStyle: GoogleFonts.instrumentSans(
                    fontSize: 16,
                    color: const Color(0xFF718096),
                  ),
                  filled: true,
                  fillColor: const Color(0xFFF7F9FC),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: Color(0xFFE2E8F0)),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: Color(0xFFE2E8F0)),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide:
                        const BorderSide(color: Color(0xFF0D76FF), width: 2),
                  ),
                  contentPadding: const EdgeInsets.all(16),
                ),
                onChanged: (value) => newActivity = value,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Cancel',
                style: GoogleFonts.instrumentSans(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF718096),
                ),
              ),
            ),
            TextButton(
              onPressed: () {
                if (newActivity.trim().isNotEmpty) {
                  setState(() {
                    _editableDayActivities[day]?[destination]
                        ?.add(newActivity.trim());
                    _onFormChanged();
                  });
                  Navigator.of(context).pop();
                }
              },
              child: Text(
                'Add',
                style: GoogleFonts.instrumentSans(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF0D76FF),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void _editActivity(
      int day, String destination, String currentActivity, int index) {
    showDialog(
      context: context,
      builder: (context) {
        String editedActivity = currentActivity;
        return AlertDialog(
          title: Text(
            'Edit Activity - Day $day',
            style: GoogleFonts.instrumentSans(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF2D3748),
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Destination: $destination',
                style: GoogleFonts.instrumentSans(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: const Color(0xFF718096),
                ),
              ),
              const SizedBox(height: 12),
              TextFormField(
                initialValue: currentActivity,
                autofocus: true,
                style: GoogleFonts.instrumentSans(
                  fontSize: 16,
                  color: const Color(0xFF2D3748),
                ),
                decoration: InputDecoration(
                  hintText: 'Enter activity name',
                  hintStyle: GoogleFonts.instrumentSans(
                    fontSize: 16,
                    color: const Color(0xFF718096),
                  ),
                  filled: true,
                  fillColor: const Color(0xFFF7F9FC),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: Color(0xFFE2E8F0)),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: Color(0xFFE2E8F0)),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide:
                        const BorderSide(color: Color(0xFF0D76FF), width: 2),
                  ),
                  contentPadding: const EdgeInsets.all(16),
                ),
                onChanged: (value) => editedActivity = value,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Cancel',
                style: GoogleFonts.instrumentSans(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF718096),
                ),
              ),
            ),
            TextButton(
              onPressed: () {
                if (editedActivity.trim().isNotEmpty) {
                  setState(() {
                    _editableDayActivities[day]![destination]![index] =
                        editedActivity.trim();
                    _onFormChanged();
                  });
                  Navigator.of(context).pop();
                }
              },
              child: Text(
                'Save',
                style: GoogleFonts.instrumentSans(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF0D76FF),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void _removeActivity(int day, String destination, int index) {
    setState(() {
      _editableDayActivities[day]?[destination]?.removeAt(index);
      _onFormChanged();
    });
  }

  Widget _buildAccommodationSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.hotel,
                color: Color(0xFF0D76FF),
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Accommodation',
                style: GoogleFonts.instrumentSans(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF2D3748),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _accommodationController,
            style: GoogleFonts.instrumentSans(
              fontSize: 16,
              color: const Color(0xFF2D3748),
            ),
            decoration: InputDecoration(
              hintText: 'Enter accommodation details (optional)',
              hintStyle: GoogleFonts.instrumentSans(
                fontSize: 16,
                color: const Color(0xFF718096),
              ),
              filled: true,
              fillColor: const Color(0xFFF7F9FC),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: Color(0xFFE2E8F0)),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: Color(0xFFE2E8F0)),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide:
                    const BorderSide(color: Color(0xFF0D76FF), width: 2),
              ),
              contentPadding: const EdgeInsets.all(16),
            ),
            maxLines: 3,
            maxLength: 500,
            buildCounter: (context,
                {required currentLength, required isFocused, maxLength}) {
              return Text(
                '$currentLength/$maxLength',
                style: GoogleFonts.instrumentSans(
                  fontSize: 12,
                  color: const Color(0xFF718096),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildNotesSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.notes,
                color: Color(0xFF0D76FF),
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Additional Notes',
                style: GoogleFonts.instrumentSans(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF2D3748),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _notesController,
            style: GoogleFonts.instrumentSans(
              fontSize: 16,
              color: const Color(0xFF2D3748),
            ),
            decoration: InputDecoration(
              hintText: 'Enter additional notes (optional)',
              hintStyle: GoogleFonts.instrumentSans(
                fontSize: 16,
                color: const Color(0xFF718096),
              ),
              filled: true,
              fillColor: const Color(0xFFF7F9FC),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: Color(0xFFE2E8F0)),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: Color(0xFFE2E8F0)),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide:
                    const BorderSide(color: Color(0xFF0D76FF), width: 2),
              ),
              contentPadding: const EdgeInsets.all(16),
            ),
            maxLines: 4,
            maxLength: 1000,
            buildCounter: (context,
                {required currentLength, required isFocused, maxLength}) {
              return Text(
                '$currentLength/$maxLength',
                style: GoogleFonts.instrumentSans(
                  fontSize: 12,
                  color: const Color(0xFF718096),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  void _onBackPressed() {
    if (_hasChanges) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text(
            'Unsaved Changes',
            style: GoogleFonts.instrumentSans(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF2D3748),
            ),
          ),
          content: Text(
            'You have unsaved changes. Do you want to save them before leaving?',
            style: GoogleFonts.instrumentSans(
              fontSize: 16,
              color: const Color(0xFF718096),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
                Navigator.of(context).pop(); // Go back without saving
              },
              child: Text(
                'Discard',
                style: GoogleFonts.instrumentSans(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.red,
                ),
              ),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(), // Just close dialog
              child: Text(
                'Cancel',
                style: GoogleFonts.instrumentSans(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF718096),
                ),
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
                _saveChanges(); // Save and go back
              },
              child: Text(
                'Save',
                style: GoogleFonts.instrumentSans(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF0D76FF),
                ),
              ),
            ),
          ],
        ),
      );
    } else {
      Navigator.of(context).pop();
    }
  }

  Future<void> _saveChanges() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_startDate == null || _endDate == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Please select both start and end dates',
            style: GoogleFonts.instrumentSans(color: Colors.white),
          ),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Create updated itinerary with day-based activities
      final updatedItinerary = Itinerary(
        id: widget.itinerary.id,
        title: _titleController.text.trim(),
        startDate: _formatDate(_startDate!),
        endDate: _formatDate(_endDate!),
        destinations: widget.itinerary.destinations,
        hasPhoto: widget.itinerary.hasPhoto,
        imagePath: widget.itinerary.imagePath,
        dailyActivities: {}, // Keep empty for backward compatibility
        daySpecificActivities: _editableDayActivities,
        activityTimes: widget.itinerary.activityTimes,
        activityImages: widget.itinerary.activityImages,
        accommodation: _accommodationController.text.trim(),
        additionalNotes: _notesController.text.trim(),
        createdAt: widget.itinerary.createdAt,
      );

      // Save to storage
      final success = await ItineraryService.updateItinerary(updatedItinerary);

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Itinerary updated successfully',
                style: GoogleFonts.instrumentSans(color: Colors.white),
              ),
              backgroundColor: const Color(0xFF0D76FF),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          );
          Navigator.of(context)
              .pop(updatedItinerary); // Return updated itinerary
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Failed to update itinerary. Please try again.',
                style: GoogleFonts.instrumentSans(color: Colors.white),
              ),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'An error occurred. Please try again.',
              style: GoogleFonts.instrumentSans(color: Colors.white),
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    }
  }
}
