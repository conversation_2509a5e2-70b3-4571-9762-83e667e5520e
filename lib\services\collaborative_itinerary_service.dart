import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/collaborative_itinerary.dart';
import '../models/itinerary_comment.dart';
import '../models/itinerary.dart';
import '../config/supabase_config.dart';
import 'image_upload_service.dart';

class CollaborativeItineraryService {
  static final SupabaseClient _client = SupabaseConfig.client;

  // Real-time subscriptions
  static RealtimeChannel? _itineraryChannel;
  static RealtimeChannel? _commentsChannel;

  // Stream controllers for real-time updates
  static final StreamController<CollaborativeItinerary>
      _itineraryUpdatesController =
      StreamController<CollaborativeItinerary>.broadcast();
  static final StreamController<List<ItineraryComment>>
      _commentsUpdatesController =
      StreamController<List<ItineraryComment>>.broadcast();

  // Getters for streams
  static Stream<CollaborativeItinerary> get itineraryUpdates =>
      _itineraryUpdatesController.stream;
  static Stream<List<ItineraryComment>> get commentsUpdates =>
      _commentsUpdatesController.stream;

  /// Generate a unique 6-digit collaboration code
  static String _generateCollaborationCode() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = Random();
    return String.fromCharCodes(
      Iterable.generate(
          6, (_) => chars.codeUnitAt(random.nextInt(chars.length))),
    );
  }

  /// Upload local itinerary to Supabase as collaborative itinerary
  static Future<CollaborativeItinerary?> shareItinerary(
      Itinerary localItinerary) async {
    try {
      final user = _client.auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Generate unique collaboration code
      String collaborationCode;
      bool isUnique = false;
      int attempts = 0;

      do {
        collaborationCode = _generateCollaborationCode();
        final existing = await _client
            .from('collaborative_itineraries')
            .select('id')
            .eq('collaboration_code', collaborationCode)
            .maybeSingle();

        isUnique = existing == null;
        attempts++;

        if (attempts > 10) {
          throw Exception('Failed to generate unique collaboration code');
        }
      } while (!isUnique);

      // Create collaborative itinerary
      final collaborativeItinerary = CollaborativeItinerary.fromLocalItinerary(
        id: '', // Will be generated by Supabase
        ownerId: user.id,
        collaborationCode: collaborationCode,
        localItinerary: localItinerary,
      );

      // Insert into Supabase
      final response = await _client
          .from('collaborative_itineraries')
          .insert(collaborativeItinerary.toJson())
          .select()
          .single();

      final result = CollaborativeItinerary.fromJson(response);

      // Migrate local activity images to Supabase Storage
      if (localItinerary.activityImages != null &&
          localItinerary.activityImages!.isNotEmpty) {
        try {
          if (kDebugMode) {
            print('Starting image migration for itinerary: ${result.id}');
          }

          final migratedImages =
              await ImageUploadService.migrateLocalActivityImages(
            itineraryId: result.id,
            localActivityImages: localItinerary.activityImages,
          );

          if (migratedImages != null) {
            // Update the collaborative itinerary with migrated image URLs
            await _client.from('collaborative_itineraries').update({
              'activity_images': migratedImages,
              'updated_at': DateTime.now().toIso8601String(),
            }).eq('id', result.id);

            if (kDebugMode) {
              print(
                  'Successfully migrated ${migratedImages.length} image destinations');
            }

            // Update the result object with migrated images
            final updatedResult =
                result.copyWith(activityImages: migratedImages);
            return updatedResult;
          }
        } catch (imageError) {
          if (kDebugMode) {
            print(
                'Warning: Image migration failed, but itinerary was created: $imageError');
          }
          // Continue without failing the entire operation
        }
      }

      // Add owner as participant (with error handling)
      try {
        await _client.from('collaboration_participants').insert({
          'itinerary_id': result.id,
          'user_id': user.id,
          'role': 'owner',
        });
      } catch (participantError) {
        if (kDebugMode) {
          print(
              'Warning: Could not add owner as participant: $participantError');
        }
        // Don't fail the entire operation if participant insertion fails
      }

      // Log activity (with error handling)
      try {
        await _logActivity(result.id, user.id, 'created', {
          'itinerary_title': result.title,
          'collaboration_code': result.collaborationCode,
        });
      } catch (logError) {
        if (kDebugMode) {
          print('Warning: Could not log activity: $logError');
        }
        // Don't fail the entire operation if logging fails
      }

      if (kDebugMode) {
        print(
            'Collaborative itinerary created: ${result.id} with code: ${result.collaborationCode}');
      }

      return result;
    } catch (e) {
      if (kDebugMode) {
        print('Error sharing itinerary: $e');
      }
      return null;
    }
  }

  /// Join a collaborative itinerary using collaboration code
  static Future<CollaborativeItinerary?> joinItinerary(
      String collaborationCode) async {
    try {
      final user = _client.auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Find itinerary by collaboration code
      final itineraryResponse = await _client
          .from('collaborative_itineraries')
          .select()
          .eq('collaboration_code', collaborationCode.toUpperCase())
          .eq('is_public', true)
          .maybeSingle();

      if (itineraryResponse == null) {
        throw Exception('Itinerary not found or not public');
      }

      final itinerary = CollaborativeItinerary.fromJson(itineraryResponse);

      // Check if user is already a participant
      final existingParticipant = await _client
          .from('collaboration_participants')
          .select()
          .eq('itinerary_id', itinerary.id)
          .eq('user_id', user.id)
          .maybeSingle();

      if (existingParticipant == null) {
        // Check participant limit (with error handling for RLS issues)
        int participantCount = 0;
        try {
          final participantCountResponse = await _client
              .from('collaboration_participants')
              .select('id')
              .eq('itinerary_id', itinerary.id);
          participantCount = participantCountResponse.length;
        } catch (countError) {
          if (kDebugMode) {
            print('Warning: Could not check participant count: $countError');
          }
          // Continue anyway - assume we can join
        }

        if (participantCount >= itinerary.maxCollaborators &&
            participantCount > 0) {
          throw Exception('Maximum number of collaborators reached');
        }

        // Add user as participant (with enhanced error handling)
        try {
          await _client.from('collaboration_participants').insert({
            'itinerary_id': itinerary.id,
            'user_id': user.id,
            'role': 'viewer',
          });
        } catch (participantError) {
          if (kDebugMode) {
            print('Error adding participant: $participantError');
          }

          // Check if it's an RLS recursion error
          if (participantError.toString().contains('infinite recursion') ||
              participantError.toString().contains('42P17')) {
            throw Exception(
                'Database configuration issue. Please contact support or try again later.');
          }

          // Check if it's a table not found error
          if (participantError.toString().contains('404') ||
              participantError.toString().contains('Not Found')) {
            throw Exception(
                'Collaboration features are not fully set up yet. Please try again later.');
          }

          // Re-throw other errors
          rethrow;
        }

        // Log activity (with error handling)
        try {
          await _logActivity(itinerary.id, user.id, 'joined', {
            'collaboration_code': collaborationCode,
          });
        } catch (logError) {
          if (kDebugMode) {
            print('Warning: Could not log join activity: $logError');
          }
          // Don't fail the join operation if logging fails
        }
      }

      if (kDebugMode) {
        print('Successfully joined itinerary: ${itinerary.id}');
      }

      return itinerary;
    } catch (e) {
      if (kDebugMode) {
        print('Error joining itinerary: $e');
      }
      rethrow;
    }
  }

  /// Get collaborative itinerary by ID
  static Future<CollaborativeItinerary?> getCollaborativeItinerary(
      String itineraryId) async {
    try {
      final response = await _client
          .from('collaborative_itineraries')
          .select()
          .eq('id', itineraryId)
          .single();

      return CollaborativeItinerary.fromJson(response);
    } catch (e) {
      if (kDebugMode) {
        print('Error getting collaborative itinerary: $e');
      }
      return null;
    }
  }

  /// Get user's collaborative itineraries
  static Future<List<CollaborativeItinerary>>
      getUserCollaborativeItineraries() async {
    try {
      final user = _client.auth.currentUser;
      if (user == null) return [];

      // Try to get itineraries where user is a participant
      List<String> itineraryIds = [];
      try {
        final participantResponse = await _client
            .from('collaboration_participants')
            .select('itinerary_id')
            .eq('user_id', user.id);

        itineraryIds = participantResponse
            .map((p) => p['itinerary_id'] as String)
            .toList();
      } catch (participantError) {
        if (kDebugMode) {
          print('Warning: Could not get participant data: $participantError');
        }

        // If RLS recursion error, try alternative approach
        if (participantError.toString().contains('infinite recursion') ||
            participantError.toString().contains('42P17')) {
          // Fallback: get itineraries owned by user
          try {
            final ownedResponse = await _client
                .from('collaborative_itineraries')
                .select()
                .eq('owner_id', user.id)
                .order('created_at', ascending: false);

            return ownedResponse
                .map((json) => CollaborativeItinerary.fromJson(json))
                .toList();
          } catch (ownedError) {
            if (kDebugMode) {
              print('Error getting owned itineraries: $ownedError');
            }
            return [];
          }
        }

        return [];
      }

      if (itineraryIds.isEmpty) return [];

      final response = await _client
          .from('collaborative_itineraries')
          .select()
          .inFilter('id', itineraryIds)
          .order('created_at', ascending: false);

      return response
          .map((json) => CollaborativeItinerary.fromJson(json))
          .toList();
    } catch (e) {
      if (kDebugMode) {
        print('Error getting user collaborative itineraries: $e');
      }
      return [];
    }
  }

  /// Get participants of a collaborative itinerary
  static Future<List<CollaborationParticipant>> getParticipants(
      String itineraryId) async {
    try {
      final response = await _client
          .from('collaboration_participants')
          .select('''
            *,
            user:user_id (
              email,
              user_metadata
            )
          ''')
          .eq('itinerary_id', itineraryId)
          .order('joined_at', ascending: true);

      return response.map((json) {
        final userData = json['user'] as Map<String, dynamic>?;
        final userMetadata =
            userData?['user_metadata'] as Map<String, dynamic>?;

        return CollaborationParticipant.fromJson({
          ...json,
          'user_email': userData?['email'],
          'user_name': userMetadata?['full_name'] ?? userMetadata?['name'],
          'user_avatar_url': userMetadata?['avatar_url'],
        });
      }).toList();
    } catch (e) {
      if (kDebugMode) {
        print('Error getting participants: $e');

        // Check if it's an RLS recursion error
        if (e.toString().contains('infinite recursion') ||
            e.toString().contains('42P17')) {
          print(
              'RLS recursion detected in getParticipants. Returning empty list.');
        }
      }
      return [];
    }
  }

  /// Add comment to collaborative itinerary
  static Future<ItineraryComment?> addComment({
    required String itineraryId,
    required String content,
    String? parentCommentId,
    String? activityId,
    int? activityDay,
  }) async {
    try {
      final user = _client.auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      final commentType = activityId != null ? 'activity' : 'itinerary';
      final comment = ItineraryComment.create(
        itineraryId: itineraryId,
        userId: user.id,
        content: content,
        parentCommentId: parentCommentId,
        activityId: activityId,
        activityDay: activityDay,
        commentType: commentType,
      );

      if (kDebugMode) {
        print('Inserting comment with data: ${comment.toJson()}');
      }

      final response = await _client
          .from('itinerary_comments')
          .insert(comment.toJson())
          .select()
          .single();

      if (kDebugMode) {
        print('Comment insert response: $response');
      }

      final result = ItineraryComment.fromJson(response);

      // Log activity
      try {
        await _logActivity(itineraryId, user.id, 'commented', {
          'comment_id': result.id,
          'comment_type': commentType,
          'activity_id': activityId,
          'content_preview':
              content.length > 50 ? '${content.substring(0, 50)}...' : content,
        });
      } catch (logError) {
        if (kDebugMode) {
          print('Warning: Could not log comment activity: $logError');
        }
        // Don't fail the entire operation if logging fails
      }

      if (kDebugMode) {
        print('Comment added successfully: ${result.id}');
      }

      return result;
    } catch (e) {
      if (kDebugMode) {
        print('Error adding comment: $e');
      }
      return null;
    }
  }

  /// Helper method to process comments with user data
  static Future<List<ItineraryComment>> _processCommentsWithUserData(
      List<dynamic> commentsResponse) async {
    final List<ItineraryComment> comments = [];

    for (final commentJson in commentsResponse) {
      try {
        // For now, create comments without user data to avoid the foreign key issue
        // User data can be added later when the foreign key relationship is properly set up
        final comment = ItineraryComment.fromJson({
          ...commentJson,
          'user_email': null,
          'user_name': null,
          'user_avatar_url': null,
        });

        comments.add(comment);
      } catch (commentError) {
        if (kDebugMode) {
          print('Error processing comment: $commentError');
        }
        // Skip this comment and continue
      }
    }

    return comments;
  }

  /// Get comments for collaborative itinerary
  static Future<List<ItineraryComment>> getComments(String itineraryId) async {
    try {
      final response = await _client
          .from('itinerary_comments')
          .select('*')
          .eq('itinerary_id', itineraryId)
          .order('created_at', ascending: true);

      if (kDebugMode) {
        print('Raw comments response: $response');
        print('Successfully fetched ${response.length} comments');
      }

      return await _processCommentsWithUserData(response);
    } catch (e) {
      if (kDebugMode) {
        print('Error getting comments: $e');
      }
      return [];
    }
  }

  /// Get comments for a specific activity
  static Future<List<ItineraryComment>> getActivityComments(
    String itineraryId,
    String activityId,
  ) async {
    try {
      final response = await _client
          .from('itinerary_comments')
          .select('*')
          .eq('itinerary_id', itineraryId)
          .eq('activity_id', activityId)
          .eq('comment_type', 'activity')
          .order('created_at', ascending: true);

      return await _processCommentsWithUserData(response);
    } catch (e) {
      if (kDebugMode) {
        print('Error getting activity comments: $e');
      }
      return [];
    }
  }

  /// Get only itinerary-level comments (not activity-specific)
  static Future<List<ItineraryComment>> getItineraryComments(
      String itineraryId) async {
    try {
      final response = await _client
          .from('itinerary_comments')
          .select('*')
          .eq('itinerary_id', itineraryId)
          .eq('comment_type', 'itinerary')
          .order('created_at', ascending: true);

      return await _processCommentsWithUserData(response);
    } catch (e) {
      if (kDebugMode) {
        print('Error getting itinerary comments: $e');
      }
      return [];
    }
  }

  /// Delete comment from collaborative itinerary
  static Future<bool> deleteComment(String commentId) async {
    try {
      final user = _client.auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // First, verify the comment exists and belongs to the current user
      final commentResponse = await _client
          .from('itinerary_comments')
          .select('user_id, itinerary_id')
          .eq('id', commentId)
          .maybeSingle();

      if (commentResponse == null) {
        throw Exception('Comment not found');
      }

      if (commentResponse['user_id'] != user.id) {
        throw Exception('You can only delete your own comments');
      }

      // Delete the comment
      await _client.from('itinerary_comments').delete().eq('id', commentId);

      // Log activity (with error handling)
      try {
        await _logActivity(
          commentResponse['itinerary_id'],
          user.id,
          'deleted_comment',
          {'comment_id': commentId},
        );
      } catch (logError) {
        if (kDebugMode) {
          print('Warning: Could not log comment deletion: $logError');
        }
        // Don't fail the entire operation if logging fails
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting comment: $e');
      }
      rethrow;
    }
  }

  /// Subscribe to real-time updates for a collaborative itinerary
  static void subscribeToItinerary(String itineraryId) {
    _unsubscribeFromItinerary();

    _itineraryChannel = _client.channel('collaborative_itinerary_$itineraryId')
      ..onPostgresChanges(
        event: PostgresChangeEvent.all,
        schema: 'public',
        table: 'collaborative_itineraries',
        filter: PostgresChangeFilter(
          type: PostgresChangeFilterType.eq,
          column: 'id',
          value: itineraryId,
        ),
        callback: (payload) {
          final itinerary = CollaborativeItinerary.fromJson(payload.newRecord);
          _itineraryUpdatesController.add(itinerary);
        },
      )
      ..subscribe();
  }

  /// Subscribe to real-time comments updates
  static void subscribeToComments(String itineraryId) {
    _unsubscribeFromComments();

    if (kDebugMode) {
      print('Subscribing to comments for itinerary: $itineraryId');
    }

    _commentsChannel = _client.channel('comments_$itineraryId')
      ..onPostgresChanges(
        event: PostgresChangeEvent.all,
        schema: 'public',
        table: 'itinerary_comments',
        filter: PostgresChangeFilter(
          type: PostgresChangeFilterType.eq,
          column: 'itinerary_id',
          value: itineraryId,
        ),
        callback: (payload) async {
          if (kDebugMode) {
            print('Real-time comment change detected: ${payload.eventType}');
            print('Payload: ${payload.newRecord}');
          }

          try {
            // Refresh comments when any change occurs
            final comments = await getComments(itineraryId);
            if (kDebugMode) {
              print(
                  'Fetched ${comments.length} comments after real-time update');
            }
            _commentsUpdatesController.add(comments);
          } catch (e) {
            if (kDebugMode) {
              print('Error refreshing comments after real-time update: $e');
            }
          }
        },
      )
      ..subscribe((status, [error]) {
        if (kDebugMode) {
          print('Comments subscription status: $status');
          if (error != null) {
            print('Comments subscription error: $error');
          }
        }
      });
  }

  /// Unsubscribe from itinerary updates
  static void _unsubscribeFromItinerary() {
    _itineraryChannel?.unsubscribe();
    _itineraryChannel = null;
  }

  /// Unsubscribe from comments updates
  static void _unsubscribeFromComments() {
    _commentsChannel?.unsubscribe();
    _commentsChannel = null;
  }

  /// Update activity image for a collaborative itinerary
  static Future<void> updateActivityImage({
    required String itineraryId,
    required String destination,
    required String activityName,
    required String imageUrl,
  }) async {
    try {
      final user = _client.auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Get current itinerary
      final response = await _client
          .from('collaborative_itineraries')
          .select('activity_images, owner_id')
          .eq('id', itineraryId)
          .single();

      // Check if user has permission to edit
      final ownerId = response['owner_id'] as String;
      final hasPermission =
          ownerId == user.id || await _hasEditPermission(itineraryId, user.id);

      if (!hasPermission) {
        throw Exception('You do not have permission to edit this itinerary');
      }

      // Update activity images
      Map<String, dynamic> activityImages = Map<String, dynamic>.from(
        response['activity_images'] as Map<String, dynamic>? ?? {},
      );

      if (!activityImages.containsKey(destination)) {
        activityImages[destination] = <String, dynamic>{};
      }

      activityImages[destination][activityName] = imageUrl;

      // Update in database
      await _client.from('collaborative_itineraries').update({
        'activity_images': activityImages,
        'updated_at': DateTime.now().toIso8601String(),
      }).eq('id', itineraryId);

      // Log activity
      await _logActivity(itineraryId, user.id, 'updated_activity_image', {
        'destination': destination,
        'activity': activityName,
        'image_url': imageUrl,
      });

      if (kDebugMode) {
        print('Activity image updated successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error updating activity image: $e');
      }
      rethrow;
    }
  }

  /// Remove activity image from a collaborative itinerary
  static Future<void> removeActivityImage({
    required String itineraryId,
    required String destination,
    required String activityName,
  }) async {
    try {
      final user = _client.auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Get current itinerary
      final response = await _client
          .from('collaborative_itineraries')
          .select('activity_images, owner_id')
          .eq('id', itineraryId)
          .single();

      // Check if user has permission to edit
      final ownerId = response['owner_id'] as String;
      final hasPermission =
          ownerId == user.id || await _hasEditPermission(itineraryId, user.id);

      if (!hasPermission) {
        throw Exception('You do not have permission to edit this itinerary');
      }

      // Update activity images
      Map<String, dynamic> activityImages = Map<String, dynamic>.from(
        response['activity_images'] as Map<String, dynamic>? ?? {},
      );

      if (activityImages.containsKey(destination)) {
        activityImages[destination]?.remove(activityName);

        // Remove destination if no activities have images
        if (activityImages[destination]?.isEmpty == true) {
          activityImages.remove(destination);
        }
      }

      // Update in database
      await _client.from('collaborative_itineraries').update({
        'activity_images': activityImages,
        'updated_at': DateTime.now().toIso8601String(),
      }).eq('id', itineraryId);

      // Log activity
      await _logActivity(itineraryId, user.id, 'removed_activity_image', {
        'destination': destination,
        'activity': activityName,
      });

      if (kDebugMode) {
        print('Activity image removed successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error removing activity image: $e');
      }
      rethrow;
    }
  }

  /// Update activity duration for a collaborative itinerary
  static Future<void> updateActivityDuration({
    required String itineraryId,
    required String destination,
    required String activityName,
    required String startTime,
    required String endTime,
    String? day,
  }) async {
    try {
      final user = _client.auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Get current itinerary
      final response = await _client
          .from('collaborative_itineraries')
          .select('activity_times, owner_id')
          .eq('id', itineraryId)
          .single();

      // Check if user has permission to edit
      final ownerId = response['owner_id'] as String;
      final hasPermission =
          ownerId == user.id || await _hasEditPermission(itineraryId, user.id);

      if (!hasPermission) {
        throw Exception('You do not have permission to edit this itinerary');
      }

      // Update activity times
      Map<String, dynamic> activityTimes = Map<String, dynamic>.from(
        response['activity_times'] as Map<String, dynamic>? ?? {},
      );

      if (!activityTimes.containsKey(destination)) {
        activityTimes[destination] = <String, dynamic>{};
      }

      // Update the specific activity's time
      activityTimes[destination][activityName] = {
        'startTime': startTime,
        'endTime': endTime,
        'day': day ?? '1',
      };

      // Update in database
      await _client.from('collaborative_itineraries').update({
        'activity_times': activityTimes,
        'updated_at': DateTime.now().toIso8601String(),
      }).eq('id', itineraryId);

      // Log activity
      await _logActivity(itineraryId, user.id, 'updated_activity_duration', {
        'destination': destination,
        'activity': activityName,
        'start_time': startTime,
        'end_time': endTime,
        'day': day ?? '1',
      });

      if (kDebugMode) {
        print('Activity duration updated successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error updating activity duration: $e');
      }
      rethrow;
    }
  }

  /// Check if user has edit permission for an itinerary
  static Future<bool> _hasEditPermission(
      String itineraryId, String userId) async {
    try {
      final participant = await _client
          .from('collaboration_participants')
          .select('role')
          .eq('itinerary_id', itineraryId)
          .eq('user_id', userId)
          .maybeSingle();

      return participant != null &&
          (participant['role'] == 'owner' || participant['role'] == 'editor');
    } catch (e) {
      return false;
    }
  }

  /// Delete a collaborative itinerary (owner only)
  static Future<void> deleteItinerary(String itineraryId) async {
    try {
      final user = _client.auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Verify ownership before deletion
      final itinerary = await _client
          .from('collaborative_itineraries')
          .select('owner_id, activity_images')
          .eq('id', itineraryId)
          .eq('owner_id', user.id)
          .maybeSingle();

      if (itinerary == null) {
        throw Exception('Itinerary not found or you are not the owner');
      }

      // Delete associated images from storage
      final activityImages =
          itinerary['activity_images'] as Map<String, dynamic>?;
      if (activityImages != null) {
        for (final destination in activityImages.values) {
          if (destination is Map<String, dynamic>) {
            for (final imageUrl in destination.values) {
              if (imageUrl is String &&
                  ImageUploadService.isSupabaseStorageUrl(imageUrl)) {
                await ImageUploadService.deleteActivityImage(imageUrl);
              }
            }
          }
        }
      }

      // Delete the itinerary (cascade delete will handle related data)
      await _client
          .from('collaborative_itineraries')
          .delete()
          .eq('id', itineraryId)
          .eq('owner_id', user.id);

      if (kDebugMode) {
        print('Collaborative itinerary deleted successfully: $itineraryId');
      }
    } catch (error) {
      if (kDebugMode) {
        print('Error deleting collaborative itinerary: $error');
      }
      throw Exception('Failed to delete itinerary: $error');
    }
  }

  /// Unsubscribe from all real-time updates
  static void unsubscribeAll() {
    _unsubscribeFromItinerary();
    _unsubscribeFromComments();
  }

  /// Log activity for audit trail
  static Future<void> _logActivity(
    String itineraryId,
    String userId,
    String actionType,
    Map<String, dynamic> actionDetails,
  ) async {
    try {
      await _client.from('itinerary_activity_logs').insert({
        'itinerary_id': itineraryId,
        'user_id': userId,
        'action_type': actionType,
        'action_details': actionDetails,
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error logging activity: $e');
        // Check if it's a table not found error
        if (e.toString().contains('404') ||
            e.toString().contains('Not Found')) {
          print(
              'Note: itinerary_activity_logs table may not exist yet. This is optional.');
        }
      }
      // Don't rethrow - logging is optional
    }
  }

  /// Dispose resources
  static void dispose() {
    unsubscribeAll();
    _itineraryUpdatesController.close();
    _commentsUpdatesController.close();
  }
}
