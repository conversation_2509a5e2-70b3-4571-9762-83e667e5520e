import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:google_fonts/google_fonts.dart';

class CustomMarkdown extends StatelessWidget {
  final String data;
  final bool isUserMessage;

  const CustomMarkdown({
    super.key,
    required this.data,
    this.isUserMessage = false,
  });

  @override
  Widget build(BuildContext context) {
    return MarkdownBody(
      data: data,
      selectable: true,
      styleSheet: _buildMarkdownStyleSheet(),
      shrinkWrap: true,
      fitContent: true,
      onTapLink: (text, href, title) {
        // Handle link taps if needed
        if (href != null) {
          // You can implement URL launching here if needed
          debugPrint('Link tapped: $href');
        }
      },
    );
  }

  MarkdownStyleSheet _buildMarkdownStyleSheet() {
    final baseColor = isUserMessage ? Colors.white : const Color(0xFF2D3748);
    final codeBackgroundColor =
        isUserMessage ? Colors.white.withOpacity(0.2) : const Color(0xFFF7F9FC);

    return MarkdownStyleSheet(
      // Paragraph styling
      p: GoogleFonts.instrumentSans(
        fontSize: 14,
        color: baseColor,
        height: 1.5,
      ),

      // Headers
      h1: GoogleFonts.instrumentSans(
        fontSize: 20,
        fontWeight: FontWeight.bold,
        color: baseColor,
        height: 1.3,
      ),
      h2: GoogleFonts.instrumentSans(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: baseColor,
        height: 1.3,
      ),
      h3: GoogleFonts.instrumentSans(
        fontSize: 16,
        fontWeight: FontWeight.w600,
        color: baseColor,
        height: 1.3,
      ),
      h4: GoogleFonts.instrumentSans(
        fontSize: 15,
        fontWeight: FontWeight.w600,
        color: baseColor,
        height: 1.3,
      ),
      h5: GoogleFonts.instrumentSans(
        fontSize: 14,
        fontWeight: FontWeight.w600,
        color: baseColor,
        height: 1.3,
      ),
      h6: GoogleFonts.instrumentSans(
        fontSize: 13,
        fontWeight: FontWeight.w600,
        color: baseColor,
        height: 1.3,
      ),

      // Text styling
      strong: GoogleFonts.instrumentSans(
        fontSize: 14,
        fontWeight: FontWeight.bold,
        color: baseColor,
      ),
      em: GoogleFonts.instrumentSans(
        fontSize: 14,
        fontStyle: FontStyle.italic,
        color: baseColor,
      ),

      // Code styling
      code: GoogleFonts.jetBrainsMono(
        fontSize: 13,
        color: isUserMessage ? Colors.white : const Color(0xFF0D76FF),
        backgroundColor: codeBackgroundColor,
      ),
      codeblockDecoration: BoxDecoration(
        color: codeBackgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isUserMessage
              ? Colors.white.withOpacity(0.3)
              : const Color(0xFFE2E8F0),
          width: 1,
        ),
      ),
      codeblockPadding: const EdgeInsets.all(12),

      // List styling
      listBullet: GoogleFonts.instrumentSans(
        fontSize: 14,
        color: baseColor,
        height: 1.5,
      ),

      // Link styling
      a: GoogleFonts.instrumentSans(
        fontSize: 14,
        color: isUserMessage ? Colors.white : const Color(0xFF0D76FF),
        decoration: TextDecoration.underline,
      ),

      // Blockquote styling
      blockquote: GoogleFonts.instrumentSans(
        fontSize: 14,
        color: baseColor.withOpacity(0.8),
        fontStyle: FontStyle.italic,
        height: 1.5,
      ),
      blockquoteDecoration: BoxDecoration(
        border: Border(
          left: BorderSide(
            color: isUserMessage
                ? Colors.white.withOpacity(0.5)
                : const Color(0xFF0D76FF),
            width: 3,
          ),
        ),
      ),
      blockquotePadding: const EdgeInsets.only(left: 16, top: 8, bottom: 8),

      // Table styling
      tableHead: GoogleFonts.instrumentSans(
        fontSize: 14,
        fontWeight: FontWeight.w600,
        color: baseColor,
      ),
      tableBody: GoogleFonts.instrumentSans(
        fontSize: 14,
        color: baseColor,
      ),

      // Horizontal rule
      horizontalRuleDecoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: isUserMessage
                ? Colors.white.withOpacity(0.3)
                : const Color(0xFFE2E8F0),
            width: 1,
          ),
        ),
      ),

      // Spacing
      h1Padding: const EdgeInsets.only(top: 16, bottom: 8),
      h2Padding: const EdgeInsets.only(top: 14, bottom: 6),
      h3Padding: const EdgeInsets.only(top: 12, bottom: 4),
      h4Padding: const EdgeInsets.only(top: 10, bottom: 4),
      h5Padding: const EdgeInsets.only(top: 8, bottom: 2),
      h6Padding: const EdgeInsets.only(top: 8, bottom: 2),
      pPadding: const EdgeInsets.only(bottom: 8),
      listIndent: 20,
      listBulletPadding: const EdgeInsets.only(right: 8),

      // Additional spacing for better readability
      blockSpacing: 8.0,
    );
  }
}
