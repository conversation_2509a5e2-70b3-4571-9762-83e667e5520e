/// Generated file. Do not edit.
///
/// This file contains the localized strings for the TripWiseGo app.
/// To add a new localized string, add it to the appropriate .arb file
/// in the lib/l10n directory and run `flutter gen-l10n`.

import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_ar.dart';
import 'app_localizations_en.dart';
import 'app_localizations_es.dart';
import 'app_localizations_fr.dart';
import 'app_localizations_hi.dart';
import 'app_localizations_id.dart';
import 'app_localizations_it.dart';
import 'app_localizations_ja.dart';
import 'app_localizations_ko.dart';
import 'app_localizations_pt.dart';
import 'app_localizations_ru.dart';
import 'app_localizations_th.dart';
import 'app_localizations_tl.dart';
import 'app_localizations_zh.dart';

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale) : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('ar'),
    Locale('en'),
    Locale('es'),
    Locale('fr'),
    Locale('hi'),
    Locale('id'),
    Locale('it'),
    Locale('ja'),
    Locale('ko'),
    Locale('pt'),
    Locale('ru'),
    Locale('th'),
    Locale('tl'),
    Locale('zh')
  ];

  /// The title of the application
  ///
  /// In en, this message translates to:
  /// **'TripWiseGo'**
  String get appTitle;

  /// Welcome message
  ///
  /// In en, this message translates to:
  /// **'Welcome'**
  String get welcome;

  /// Label for guest users
  ///
  /// In en, this message translates to:
  /// **'Guest User'**
  String get guestUser;

  /// Subtitle for authenticated users
  ///
  /// In en, this message translates to:
  /// **'Ready for your next adventure'**
  String get readyForAdventure;

  /// Subtitle for guest users
  ///
  /// In en, this message translates to:
  /// **'Exploring the world as a guest'**
  String get exploringAsGuest;

  /// Button to edit user profile
  ///
  /// In en, this message translates to:
  /// **'Edit Profile'**
  String get editProfile;

  /// Button to save profile changes
  ///
  /// In en, this message translates to:
  /// **'Save Changes'**
  String get saveChanges;

  /// Cancel button
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// Username field label
  ///
  /// In en, this message translates to:
  /// **'Username'**
  String get username;

  /// Email field label
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get email;

  /// Success message when profile is updated
  ///
  /// In en, this message translates to:
  /// **'Profile updated successfully!'**
  String get profileUpdatedSuccessfully;

  /// Error message when profile update fails
  ///
  /// In en, this message translates to:
  /// **'Failed to update profile: {error}'**
  String failedToUpdateProfile(String error);

  /// Success message when profile picture is updated
  ///
  /// In en, this message translates to:
  /// **'Profile picture updated successfully!'**
  String get profilePictureUpdatedSuccessfully;

  /// Error message when image upload fails
  ///
  /// In en, this message translates to:
  /// **'Failed to upload image: {error}'**
  String failedToUploadImage(String error);

  /// Message shown when guest users try to edit profile
  ///
  /// In en, this message translates to:
  /// **'Profile editing is not available for guest users'**
  String get profileEditingNotAvailableForGuests;

  /// Message shown when guest users try to edit profile picture
  ///
  /// In en, this message translates to:
  /// **'Profile picture editing is not available for guest users'**
  String get profilePictureEditingNotAvailableForGuests;

  /// Validation message for empty username
  ///
  /// In en, this message translates to:
  /// **'Username cannot be empty'**
  String get usernameCannotBeEmpty;

  /// Validation message for username length
  ///
  /// In en, this message translates to:
  /// **'Username must be between 2 and 30 characters'**
  String get usernameMustBeBetween2And30Characters;

  /// Plan menu item
  ///
  /// In en, this message translates to:
  /// **'Plan'**
  String get plan;

  /// Terms of Service page title
  ///
  /// In en, this message translates to:
  /// **'Terms of Service'**
  String get termsOfService;

  /// Language menu item
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get language;

  /// Privacy Policy page title
  ///
  /// In en, this message translates to:
  /// **'Privacy Policy'**
  String get privacyPolicy;

  /// Support section title
  ///
  /// In en, this message translates to:
  /// **'Support'**
  String get support;

  /// Help Center menu item
  ///
  /// In en, this message translates to:
  /// **'Help Center'**
  String get helpCenter;

  /// Contact Us menu item
  ///
  /// In en, this message translates to:
  /// **'Contact Us'**
  String get contactUs;

  /// Help & Support screen title
  ///
  /// In en, this message translates to:
  /// **'Help & Support'**
  String get helpSupport;

  /// Help support screen header
  ///
  /// In en, this message translates to:
  /// **'How can we help you?'**
  String get howCanWeHelpYou;

  /// Initial greeting message in help support chatbot
  ///
  /// In en, this message translates to:
  /// **'Hi! I\'m here to help you with TripwiseGO. You can ask me about app features, troubleshooting, or report any issues you\'re experiencing.'**
  String get helpSupportGreeting;

  /// Welcome message in help support chatbot
  ///
  /// In en, this message translates to:
  /// **'Welcome to TripwiseGO Support! Here are some things I can help you with:'**
  String get helpSupportWelcome;

  /// List of help support features
  ///
  /// In en, this message translates to:
  /// **'• App features and how to use them\n• Navigation and account help\n• Troubleshooting common issues\n• Reporting bugs or problems'**
  String get helpSupportFeatures;

  /// Prompt to ask questions in help support
  ///
  /// In en, this message translates to:
  /// **'Feel free to ask me anything or describe any issues you\'re having!'**
  String get helpSupportAskQuestion;

  /// Report issue button text
  ///
  /// In en, this message translates to:
  /// **'Report Issue'**
  String get reportIssue;

  /// Report bug option
  ///
  /// In en, this message translates to:
  /// **'Report Bug'**
  String get reportBug;

  /// Report problem option
  ///
  /// In en, this message translates to:
  /// **'Report Problem'**
  String get reportProblem;

  /// Issue category field label
  ///
  /// In en, this message translates to:
  /// **'Issue Category'**
  String get issueCategory;

  /// Bug report category
  ///
  /// In en, this message translates to:
  /// **'Bug Report'**
  String get bugReport;

  /// Feature request category
  ///
  /// In en, this message translates to:
  /// **'Feature Request'**
  String get featureRequest;

  /// General feedback category
  ///
  /// In en, this message translates to:
  /// **'General Feedback'**
  String get generalFeedback;

  /// Account issue category
  ///
  /// In en, this message translates to:
  /// **'Account Issue'**
  String get accountIssue;

  /// Technical problem category
  ///
  /// In en, this message translates to:
  /// **'Technical Problem'**
  String get technicalProblem;

  /// Name field label
  ///
  /// In en, this message translates to:
  /// **'Your Name'**
  String get yourName;

  /// Email field label
  ///
  /// In en, this message translates to:
  /// **'Your Email'**
  String get yourEmail;

  /// Issue description field label
  ///
  /// In en, this message translates to:
  /// **'Issue Description'**
  String get issueDescription;

  /// Issue description placeholder text
  ///
  /// In en, this message translates to:
  /// **'Please describe the issue in detail'**
  String get describeIssueDetail;

  /// Screenshot upload field label
  ///
  /// In en, this message translates to:
  /// **'Screenshot (Optional)'**
  String get optionalScreenshot;

  /// Submit report button text
  ///
  /// In en, this message translates to:
  /// **'Submit Report'**
  String get submitReport;

  /// Report submitted dialog title
  ///
  /// In en, this message translates to:
  /// **'Report Submitted'**
  String get reportSubmitted;

  /// Report submitted success message
  ///
  /// In en, this message translates to:
  /// **'Thank you! Your report has been submitted successfully. We\'ll look into it and get back to you if needed.'**
  String get reportSubmittedSuccess;

  /// Report submission failed message
  ///
  /// In en, this message translates to:
  /// **'Failed to submit report. Please try again later.'**
  String get reportSubmissionFailed;

  /// Name field validation error
  ///
  /// In en, this message translates to:
  /// **'Name is required'**
  String get nameRequired;

  /// Email field validation error
  ///
  /// In en, this message translates to:
  /// **'Email is required'**
  String get emailRequired;

  /// Valid email validation error
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid email address'**
  String get validEmailRequired;

  /// Description field validation error
  ///
  /// In en, this message translates to:
  /// **'Issue description is required'**
  String get descriptionRequired;

  /// Category selection validation error
  ///
  /// In en, this message translates to:
  /// **'Please select a category'**
  String get selectCategory;

  /// Subscription screen title
  ///
  /// In en, this message translates to:
  /// **'Subscription'**
  String get subscription;

  /// Subscription active screen title
  ///
  /// In en, this message translates to:
  /// **'Subscription Active!'**
  String get subscriptionActive;

  /// Welcome message for premium users
  ///
  /// In en, this message translates to:
  /// **'Welcome to TripwiseGO Premium! Enjoy unlimited access to all features.'**
  String get welcomeToPremium;

  /// Current subscription plan label
  ///
  /// In en, this message translates to:
  /// **'Current Plan'**
  String get currentPlan;

  /// Trial subscription status
  ///
  /// In en, this message translates to:
  /// **'Trial'**
  String get trial;

  /// Active subscription status
  ///
  /// In en, this message translates to:
  /// **'Active'**
  String get active;

  /// Trial expiration message
  ///
  /// In en, this message translates to:
  /// **'Trial ends in {days} days'**
  String trialEndsIn(int days);

  /// Subscription renewal message
  ///
  /// In en, this message translates to:
  /// **'Renews in {days} days'**
  String renewsIn(int days);

  /// Benefits section title
  ///
  /// In en, this message translates to:
  /// **'Your Benefits'**
  String get yourBenefits;

  /// Button to start planning trip
  ///
  /// In en, this message translates to:
  /// **'Start Planning Your Trip'**
  String get startPlanningTrip;

  /// Button to manage subscription
  ///
  /// In en, this message translates to:
  /// **'Manage Subscription'**
  String get manageSubscription;

  /// Basic subscription plan
  ///
  /// In en, this message translates to:
  /// **'Basic'**
  String get basic;

  /// Premium subscription plan
  ///
  /// In en, this message translates to:
  /// **'Premium'**
  String get premium;

  /// Basic plan feature
  ///
  /// In en, this message translates to:
  /// **'Basic Place Recommendations'**
  String get basicPlaceRecommendations;

  /// Basic plan feature
  ///
  /// In en, this message translates to:
  /// **'Location Recommendation Swiping (20 swipes/day)'**
  String get locationRecommendationSwiping;

  /// Basic plan feature
  ///
  /// In en, this message translates to:
  /// **'AI-Powered Travel Planner'**
  String get aiPoweredTravelPlanner;

  /// Basic plan feature
  ///
  /// In en, this message translates to:
  /// **'Unlimited Quizzes & Fun Facts'**
  String get unlimitedQuizzes;

  /// Basic plan feature
  ///
  /// In en, this message translates to:
  /// **'No-Ads'**
  String get noAds;

  /// One month subscription duration
  ///
  /// In en, this message translates to:
  /// **'1 Month'**
  String get oneMonth;

  /// Three months subscription duration
  ///
  /// In en, this message translates to:
  /// **'3 Months'**
  String get threeMonths;

  /// Five months subscription duration
  ///
  /// In en, this message translates to:
  /// **'5 Months'**
  String get fiveMonths;

  /// Free trial duration
  ///
  /// In en, this message translates to:
  /// **'{days}-day free trial'**
  String dayFreeTrial(int days);

  /// Monthly billing cycle
  ///
  /// In en, this message translates to:
  /// **'Billed monthly'**
  String get billedMonthly;

  /// Twice annually billing cycle
  ///
  /// In en, this message translates to:
  /// **'Billed twice annually'**
  String get billedTwiceAnnually;

  /// Annual billing cycle
  ///
  /// In en, this message translates to:
  /// **'Billed annually'**
  String get billedAnnually;

  /// Discount text for popular plan
  ///
  /// In en, this message translates to:
  /// **'SAVE 45%'**
  String get save45Percent;

  /// Continue button text
  ///
  /// In en, this message translates to:
  /// **'Continue'**
  String get continueButton;

  /// Terms and conditions link
  ///
  /// In en, this message translates to:
  /// **'Terms and Conditions'**
  String get termsAndConditions;

  /// Subscription failure message
  ///
  /// In en, this message translates to:
  /// **'Failed to subscribe. Please try again.'**
  String get failedToSubscribe;

  /// Sign out button
  ///
  /// In en, this message translates to:
  /// **'Sign Out'**
  String get signOut;

  /// Title for language selection screen
  ///
  /// In en, this message translates to:
  /// **'Select Language'**
  String get selectLanguage;

  /// Subtitle for language selection
  ///
  /// In en, this message translates to:
  /// **'Choose your preferred language'**
  String get chooseYourPreferredLanguage;

  /// Success message when language is changed
  ///
  /// In en, this message translates to:
  /// **'Language updated successfully!'**
  String get languageUpdatedSuccessfully;

  /// Home navigation tab
  ///
  /// In en, this message translates to:
  /// **'Home'**
  String get home;

  /// Match navigation tab
  ///
  /// In en, this message translates to:
  /// **'Match'**
  String get match;

  /// Chat navigation tab
  ///
  /// In en, this message translates to:
  /// **'Chat'**
  String get chat;

  /// Profile navigation tab
  ///
  /// In en, this message translates to:
  /// **'Profile'**
  String get profile;

  /// Loading indicator text
  ///
  /// In en, this message translates to:
  /// **'Loading...'**
  String get loading;

  /// Generic error title
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get error;

  /// Retry button
  ///
  /// In en, this message translates to:
  /// **'Retry'**
  String get retry;

  /// OK button
  ///
  /// In en, this message translates to:
  /// **'OK'**
  String get ok;

  /// Yes button
  ///
  /// In en, this message translates to:
  /// **'Yes'**
  String get yes;

  /// No button
  ///
  /// In en, this message translates to:
  /// **'No'**
  String get no;

  /// Save button
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get save;

  /// Delete button
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get delete;

  /// Edit button
  ///
  /// In en, this message translates to:
  /// **'Edit'**
  String get edit;

  /// Add button
  ///
  /// In en, this message translates to:
  /// **'Add'**
  String get add;

  /// Remove button
  ///
  /// In en, this message translates to:
  /// **'Remove'**
  String get remove;

  /// Close button
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get close;

  /// Back button
  ///
  /// In en, this message translates to:
  /// **'Back'**
  String get back;

  /// Next button
  ///
  /// In en, this message translates to:
  /// **'Next'**
  String get next;

  /// Previous button
  ///
  /// In en, this message translates to:
  /// **'Previous'**
  String get previous;

  /// Done button
  ///
  /// In en, this message translates to:
  /// **'Done'**
  String get done;

  /// Search placeholder
  ///
  /// In en, this message translates to:
  /// **'Search'**
  String get search;

  /// Message when search returns no results
  ///
  /// In en, this message translates to:
  /// **'No results found'**
  String get noResultsFound;

  /// Try again message
  ///
  /// In en, this message translates to:
  /// **'Try again'**
  String get tryAgain;

  /// Generic error message
  ///
  /// In en, this message translates to:
  /// **'Something went wrong'**
  String get somethingWentWrong;

  /// Network error message
  ///
  /// In en, this message translates to:
  /// **'Network error. Please check your connection.'**
  String get networkError;

  /// Server error message
  ///
  /// In en, this message translates to:
  /// **'Server error. Please try again later.'**
  String get serverError;

  /// Invalid input error message
  ///
  /// In en, this message translates to:
  /// **'Invalid input'**
  String get invalidInput;

  /// Required field indicator
  ///
  /// In en, this message translates to:
  /// **'Required'**
  String get required;

  /// Optional field indicator
  ///
  /// In en, this message translates to:
  /// **'Optional'**
  String get optional;

  /// Itinerary navigation tab
  ///
  /// In en, this message translates to:
  /// **'Itinerary'**
  String get itinerary;

  /// Title for itinerary list
  ///
  /// In en, this message translates to:
  /// **'Your Itineraries'**
  String get yourItineraries;

  /// Call to action for trip planning
  ///
  /// In en, this message translates to:
  /// **'Start Planning Your Trip'**
  String get startPlanningYourTrip;

  /// Placeholder text for trip planning features
  ///
  /// In en, this message translates to:
  /// **'Your trip planning features will appear here. The persistent authentication is now working!'**
  String get tripPlanningFeaturesWillAppearHere;

  /// For You tab in match screen
  ///
  /// In en, this message translates to:
  /// **'For You'**
  String get forYou;

  /// Liked tab in match screen
  ///
  /// In en, this message translates to:
  /// **'Liked'**
  String get liked;

  /// Collaboration tab
  ///
  /// In en, this message translates to:
  /// **'Collab'**
  String get collab;

  /// Collaborative Itinerary navigation tab
  ///
  /// In en, this message translates to:
  /// **'Collaborative'**
  String get collaborativeItinerary;

  /// End session button for guest users
  ///
  /// In en, this message translates to:
  /// **'End Session'**
  String get endSession;

  /// Logout button for authenticated users
  ///
  /// In en, this message translates to:
  /// **'Logout'**
  String get logout;

  /// Error message when logout fails
  ///
  /// In en, this message translates to:
  /// **'Logout failed: {error}'**
  String logoutFailed(String error);

  /// Message when no itinerary is available
  ///
  /// In en, this message translates to:
  /// **'No Itinerary Found'**
  String get noItineraryFound;

  /// Suggestion to use AI for travel planning
  ///
  /// In en, this message translates to:
  /// **'Ask our AI to create a travel plan for you!'**
  String get askAiToCreateTravelPlan;

  /// Saturday day name
  ///
  /// In en, this message translates to:
  /// **'Saturday'**
  String get saturday;

  /// Tuesday day name
  ///
  /// In en, this message translates to:
  /// **'Tuesday'**
  String get tuesday;

  /// Day number label
  ///
  /// In en, this message translates to:
  /// **'Day {number}'**
  String dayNumber(int number);

  /// Header for itinerary overview section
  ///
  /// In en, this message translates to:
  /// **'Itinerary Overview'**
  String get itineraryOverview;

  /// Days and nights format
  ///
  /// In en, this message translates to:
  /// **'{days}d {nights}n'**
  String daysAndNights(int days, int nights);

  /// AI greeting message
  ///
  /// In en, this message translates to:
  /// **'Hi, I\'m Wanderly AI 🌏'**
  String get hiImWanderlyAi;

  /// AI assistant introduction
  ///
  /// In en, this message translates to:
  /// **'Your Travel AI Assistant, how can I help you today?'**
  String get yourTravelAiAssistant;

  /// Instruction for using chat interface
  ///
  /// In en, this message translates to:
  /// **'Use this bubble chat'**
  String get useThisBubbleChat;

  /// AI Assistant label
  ///
  /// In en, this message translates to:
  /// **'AI Assistant'**
  String get aiAssistant;

  /// Chat history button tooltip
  ///
  /// In en, this message translates to:
  /// **'Chat History'**
  String get chatHistory;

  /// New chat button tooltip
  ///
  /// In en, this message translates to:
  /// **'New Chat'**
  String get newChat;

  /// Add image dialog title
  ///
  /// In en, this message translates to:
  /// **'Add Image'**
  String get addImage;

  /// Camera option in image picker
  ///
  /// In en, this message translates to:
  /// **'Camera'**
  String get camera;

  /// Gallery option in image picker
  ///
  /// In en, this message translates to:
  /// **'Gallery'**
  String get gallery;

  /// Error message for microphone permission
  ///
  /// In en, this message translates to:
  /// **'Microphone permission is required for voice input'**
  String get microphonePermissionRequired;

  /// Error message when speech recognition is unavailable
  ///
  /// In en, this message translates to:
  /// **'Speech recognition is not available on this device'**
  String get speechRecognitionNotAvailable;

  /// Status when listening for voice input
  ///
  /// In en, this message translates to:
  /// **'Listening...'**
  String get listening;

  /// Delete chat dialog title
  ///
  /// In en, this message translates to:
  /// **'Delete Chat'**
  String get deleteChat;

  /// Delete chat confirmation message
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete this chat? This action cannot be undone.'**
  String get deleteChatConfirmation;

  /// Success message when chat is deleted
  ///
  /// In en, this message translates to:
  /// **'Chat deleted successfully'**
  String get chatDeletedSuccessfully;

  /// Error when search query is empty
  ///
  /// In en, this message translates to:
  /// **'Please enter a search query'**
  String get pleaseEnterSearchQuery;

  /// Error when daily search limit is reached
  ///
  /// In en, this message translates to:
  /// **'Daily search limit reached. You can perform 5 searches per day.'**
  String get dailySearchLimitReached;

  /// Status when searching the web
  ///
  /// In en, this message translates to:
  /// **'Searching the Web...'**
  String get searchingTheWeb;

  /// Status when web search mode is active
  ///
  /// In en, this message translates to:
  /// **'Web Search Mode Active'**
  String get webSearchModeActive;

  /// Message while searching
  ///
  /// In en, this message translates to:
  /// **'Please wait while I search for information'**
  String get pleaseWaitWhileSearching;

  /// Info about next message searching
  ///
  /// In en, this message translates to:
  /// **'Your next message will search the web'**
  String get yourNextMessageWillSearch;

  /// Option to disable web search
  ///
  /// In en, this message translates to:
  /// **'Disable Web Search'**
  String get disableWebSearch;

  /// Option to enable web search
  ///
  /// In en, this message translates to:
  /// **'Enable Web Search'**
  String get enableWebSearch;

  /// Subtitle for disabling web search
  ///
  /// In en, this message translates to:
  /// **'Switch back to AI chat mode'**
  String get switchBackToAiChatMode;

  /// Subtitle for enabling web search
  ///
  /// In en, this message translates to:
  /// **'Search the web for current information'**
  String get searchWebForCurrentInfo;

  /// Menu option to pick image from gallery
  ///
  /// In en, this message translates to:
  /// **'Pick Image from Gallery'**
  String get pickImageFromGallery;

  /// Subtitle for image upload option
  ///
  /// In en, this message translates to:
  /// **'Upload an image for AI analysis'**
  String get uploadImageForAiAnalysis;

  /// Label for user messages
  ///
  /// In en, this message translates to:
  /// **'Your Message'**
  String get yourMessage;

  /// Label for AI messages
  ///
  /// In en, this message translates to:
  /// **'Wanderly AI'**
  String get wanderlyAi;

  /// Label for web search messages
  ///
  /// In en, this message translates to:
  /// **'Web Search:'**
  String get webSearch;

  /// Like button label
  ///
  /// In en, this message translates to:
  /// **'Like'**
  String get like;

  /// Dislike button label
  ///
  /// In en, this message translates to:
  /// **'Dislike'**
  String get dislike;

  /// Copy button label
  ///
  /// In en, this message translates to:
  /// **'Copy'**
  String get copy;

  /// Regenerate button label
  ///
  /// In en, this message translates to:
  /// **'Regenerate'**
  String get regenerate;

  /// Error message when feedback submission fails
  ///
  /// In en, this message translates to:
  /// **'Failed to submit feedback. Please try again.'**
  String get failedToSubmitFeedback;

  /// Thank you message for positive feedback
  ///
  /// In en, this message translates to:
  /// **'Thank you for your feedback! 🙏'**
  String get thankYouForFeedback;

  /// Thank you message for negative feedback
  ///
  /// In en, this message translates to:
  /// **'Feedback received. Thank you for helping us improve! 🚀'**
  String get feedbackReceivedThanks;

  /// Message when response is copied
  ///
  /// In en, this message translates to:
  /// **'Response copied to clipboard'**
  String get responseCopiedToClipboard;

  /// Typing indicator message
  ///
  /// In en, this message translates to:
  /// **'Wanderly AI is typing'**
  String get wanderlyAiIsTyping;

  /// Button to stop AI response generation
  ///
  /// In en, this message translates to:
  /// **'Stop Generation'**
  String get stopGeneration;

  /// Chat limit indicator
  ///
  /// In en, this message translates to:
  /// **'You have 10 chats left'**
  String get youHaveChatsLeft;

  /// Placeholder for search input
  ///
  /// In en, this message translates to:
  /// **'Enter your search query...'**
  String get enterSearchQuery;

  /// Placeholder for chat input
  ///
  /// In en, this message translates to:
  /// **'Ask me anything or long press to speak...'**
  String get askMeAnythingOrLongPress;

  /// Error message when image picking fails
  ///
  /// In en, this message translates to:
  /// **'Failed to pick image: {error}'**
  String failedToPickImage(String error);

  /// Error message when image analysis fails
  ///
  /// In en, this message translates to:
  /// **'Failed to analyze image. Please try again.'**
  String get failedToAnalyzeImage;

  /// Message when response generation is stopped
  ///
  /// In en, this message translates to:
  /// **'Response generation was stopped.'**
  String get responseGenerationStopped;

  /// Fallback text for unknown destinations
  ///
  /// In en, this message translates to:
  /// **'Unknown Destination'**
  String get unknownDestination;

  /// Congratulations message for matches
  ///
  /// In en, this message translates to:
  /// **'Congratulations'**
  String get congratulations;

  /// Match confirmation message
  ///
  /// In en, this message translates to:
  /// **'It\'s a match'**
  String get itsAMatch;

  /// Match subtitle message
  ///
  /// In en, this message translates to:
  /// **'Travel vibes aligned pack\nyour bags, you\'ve got a match!'**
  String get travelVibesAligned;

  /// Header for matched preferences section
  ///
  /// In en, this message translates to:
  /// **'Matched Preferences:'**
  String get matchedPreferences;

  /// Button to add destination to itinerary
  ///
  /// In en, this message translates to:
  /// **'Add to Itinerary'**
  String get addToItinerary;

  /// Button to continue swiping
  ///
  /// In en, this message translates to:
  /// **'Keep swiping'**
  String get keepSwiping;

  /// Version information menu item
  ///
  /// In en, this message translates to:
  /// **'Version Info'**
  String get versionInfo;

  /// App version label
  ///
  /// In en, this message translates to:
  /// **'App Version'**
  String get appVersion;

  /// Build number label
  ///
  /// In en, this message translates to:
  /// **'Build Number'**
  String get buildNumber;

  /// Package name label
  ///
  /// In en, this message translates to:
  /// **'Package Name'**
  String get packageName;

  /// Application name label
  ///
  /// In en, this message translates to:
  /// **'App Name'**
  String get appName;

  /// Build signature label
  ///
  /// In en, this message translates to:
  /// **'Build Signature'**
  String get buildSignature;

  /// Installer store label
  ///
  /// In en, this message translates to:
  /// **'Installer Store'**
  String get installerStore;

  /// Device information section title
  ///
  /// In en, this message translates to:
  /// **'Device Information'**
  String get deviceInfo;

  /// Operating system label
  ///
  /// In en, this message translates to:
  /// **'Operating System'**
  String get operatingSystem;

  /// Flutter version label
  ///
  /// In en, this message translates to:
  /// **'Flutter Version'**
  String get flutterVersion;

  /// Dart version label
  ///
  /// In en, this message translates to:
  /// **'Dart Version'**
  String get dartVersion;

  /// Leave a review button text
  ///
  /// In en, this message translates to:
  /// **'Leave a Review'**
  String get leaveAReview;

  /// Rate our app dialog title
  ///
  /// In en, this message translates to:
  /// **'Rate Our App'**
  String get rateOurApp;

  /// Review dialog subtitle
  ///
  /// In en, this message translates to:
  /// **'Enjoying TripWiseGo?'**
  String get enjoyingTripWiseGo;

  /// Review dialog description
  ///
  /// In en, this message translates to:
  /// **'Help us improve by leaving a review on the app store. Your feedback means a lot to us!'**
  String get helpUsImproveByLeavingReview;

  /// Rate now button text
  ///
  /// In en, this message translates to:
  /// **'Rate Now'**
  String get rateNow;

  /// Maybe later button text
  ///
  /// In en, this message translates to:
  /// **'Maybe Later'**
  String get maybeLater;

  /// Message for development builds
  ///
  /// In en, this message translates to:
  /// **'Review feature is only available in production builds'**
  String get reviewFeatureNotAvailable;

  /// Error message when store cannot be opened
  ///
  /// In en, this message translates to:
  /// **'Unable to open app store. Please try again later.'**
  String get unableToOpenStore;

  /// Thank you message after review
  ///
  /// In en, this message translates to:
  /// **'Thank you for taking the time to review our app!'**
  String get thankYouForReview;
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>['ar', 'en', 'es', 'fr', 'hi', 'id', 'it', 'ja', 'ko', 'pt', 'ru', 'th', 'tl', 'zh'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {


  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'ar': return AppLocalizationsAr();
    case 'en': return AppLocalizationsEn();
    case 'es': return AppLocalizationsEs();
    case 'fr': return AppLocalizationsFr();
    case 'hi': return AppLocalizationsHi();
    case 'id': return AppLocalizationsId();
    case 'it': return AppLocalizationsIt();
    case 'ja': return AppLocalizationsJa();
    case 'ko': return AppLocalizationsKo();
    case 'pt': return AppLocalizationsPt();
    case 'ru': return AppLocalizationsRu();
    case 'th': return AppLocalizationsTh();
    case 'tl': return AppLocalizationsTl();
    case 'zh': return AppLocalizationsZh();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.'
  );
}
