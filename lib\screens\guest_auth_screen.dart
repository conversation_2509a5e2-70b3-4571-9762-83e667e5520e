import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../widgets/simple_captcha.dart';
import '../services/auth_service.dart';
import 'homepage_screen.dart';

class GuestAuthScreen extends StatefulWidget {
  const GuestAuthScreen({super.key});

  @override
  State<GuestAuthScreen> createState() => _GuestAuthScreenState();
}

class _GuestAuthScreenState extends State<GuestAuthScreen> {
  bool _isCaptchaVerified = false;
  bool _isLoading = false;

  void _onCaptchaVerified(bool isVerified) {
    setState(() {
      _isCaptchaVerified = isVerified;
    });
  }

  Future<void> _continueAsGuest() async {
    if (!_isCaptchaVerified) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Please complete the captcha verification',
            style: GoogleFonts.instrumentSans(color: Colors.white),
          ),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final response = await AuthService.signInAsGuest();

      if (mounted) {
        if (response.user != null) {
          // Guest users don't need to complete survey, navigate directly to homepage
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(
              builder: (context) => const HomepageScreen(),
            ),
          );
        } else {
          // Guest authentication failed but no exception was thrown
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Failed to continue as guest: Authentication failed',
                style: GoogleFonts.instrumentSans(color: Colors.white),
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Failed to continue as guest: ${error.toString()}',
              style: GoogleFonts.instrumentSans(color: Colors.white),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFFFFFF),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(horizontal: 32.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 20),

              // Back button
              Align(
                alignment: Alignment.centerLeft,
                child: Container(
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      border:
                          Border.all(color: const Color(0xffD9D9D9), width: 2)),
                  child: IconButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    icon: const Icon(
                      Icons.arrow_back_ios_new,
                      color: Color(0xFF2D3748),
                      size: 20,
                    ),
                  ),
                ),
              ),

              // Logo with blue circle background
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    width: 125,
                    height: 125,
                    padding: const EdgeInsets.all(20),
                    decoration: const BoxDecoration(
                      color: Color(0xFF0D76FF),
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: Image.asset(
                        'assets/images/icon_trip.png',
                        height: 80,
                        width: 80,
                        fit: BoxFit.contain,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 40),

              // Guest Access title
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Guest Access',
                    style: GoogleFonts.instrumentSans(
                      fontSize: 38,
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF2D3748),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
              const SizedBox(height: 8),

              Text(
                'Please verify that you\'re not a robot to continue as a guest.',
                style: GoogleFonts.instrumentSans(
                  fontSize: 16,
                  color: const Color(0xFF718096),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 40),

              // Captcha widget
              SimpleCaptcha(
                onCaptchaVerified: _onCaptchaVerified,
              ),
              const SizedBox(height: 40),

              // Continue as Guest button
              SizedBox(
                width: double.infinity,
                height: 52,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _continueAsGuest,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _isCaptchaVerified
                        ? const Color(0xFF0D76FF)
                        : const Color(0xFFE2E8F0),
                    foregroundColor: _isCaptchaVerified
                        ? Colors.white
                        : const Color(0xFF718096),
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(26),
                    ),
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : Text(
                          'Continue as Guest',
                          style: GoogleFonts.instrumentSans(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                ),
              ),
              const SizedBox(height: 65),

              // Want to create an account?
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    "Want to create an account? ",
                    style: GoogleFonts.instrumentSans(
                      color: const Color(0xFF718096),
                      fontSize: 14,
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    style: TextButton.styleFrom(
                      padding: EdgeInsets.zero,
                      minimumSize: Size.zero,
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                    child: const Text(
                      'Go Back',
                      style: TextStyle(
                        color: Color(0xFF0D76FF),
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 30),
            ],
          ),
        ),
      ),
    );
  }
}
