import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../services/ai_itinerary_parser.dart';

/// Widget to display itineraries with enhanced timeline-style formatting
class EnhancedItineraryDisplay extends StatefulWidget {
  final ParsedItinerary itinerary;
  final bool isCompact;

  const EnhancedItineraryDisplay({
    super.key,
    required this.itinerary,
    this.isCompact = false,
  });

  @override
  State<EnhancedItineraryDisplay> createState() => _EnhancedItineraryDisplayState();
}

class _EnhancedItineraryDisplayState extends State<EnhancedItineraryDisplay>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Container(
          margin: const EdgeInsets.symmetric(vertical: 16),
          decoration: BoxDecoration(
            color: const Color(0xFFF7F9FC),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(),
              _buildItineraryContent(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          colors: [Color(0xFF0D76FF), Color(0xFF1E88E5)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.itinerary.title ?? 'Travel Itinerary',
            style: GoogleFonts.instrumentSans(
              fontSize: widget.isCompact ? 16 : 20,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          if (widget.itinerary.startDate != null) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(
                  Icons.calendar_today,
                  size: 16,
                  color: Colors.white,
                ),
                const SizedBox(width: 8),
                Text(
                  '${widget.itinerary.startDate}${widget.itinerary.endDate != null && widget.itinerary.endDate != widget.itinerary.startDate ? ' - ${widget.itinerary.endDate}' : ''}',
                  style: GoogleFonts.instrumentSans(
                    fontSize: widget.isCompact ? 12 : 14,
                    color: Colors.white.withOpacity(0.9),
                  ),
                ),
              ],
            ),
          ],
          if (widget.itinerary.destinations.isNotEmpty) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(
                  Icons.location_on,
                  size: 16,
                  color: Colors.white,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    widget.itinerary.destinations.join(', '),
                    style: GoogleFonts.instrumentSans(
                      fontSize: widget.isCompact ? 12 : 14,
                      color: Colors.white.withOpacity(0.9),
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildItineraryContent() {
    if (widget.itinerary.daySpecificActivities?.isNotEmpty == true) {
      return _buildDayByDayItinerary();
    } else {
      return _buildDestinationBasedItinerary();
    }
  }

  Widget _buildDayByDayItinerary() {
    final dayActivities = widget.itinerary.daySpecificActivities!;
    final sortedDays = dayActivities.keys.toList()..sort();

    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: sortedDays.map((day) {
          final activities = dayActivities[day]!;
          return _buildDayCard(day, activities);
        }).toList(),
      ),
    );
  }

  Widget _buildDayCard(int day, Map<String, List<String>> activities) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Day indicator
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: const Color(0xFF0D76FF),
              borderRadius: BorderRadius.circular(30),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF0D76FF).withOpacity(0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Center(
              child: Text(
                'Day\n$day',
                textAlign: TextAlign.center,
                style: GoogleFonts.instrumentSans(
                  fontSize: widget.isCompact ? 10 : 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  height: 1.2,
                ),
              ),
            ),
          ),
          
          const SizedBox(width: 16),
          
          // Activities
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: activities.entries.map((entry) {
                final destination = entry.key;
                final activityList = entry.value;
                
                return _buildActivityGroup(destination, activityList);
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActivityGroup(String destination, List<String> activities) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFFE2E8F0),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Destination header
          Text(
            destination,
            style: GoogleFonts.instrumentSans(
              fontSize: widget.isCompact ? 14 : 16,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF0D76FF),
            ),
          ),
          
          const SizedBox(height: 8),
          
          // Activities list
          ...activities.map((activity) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 6,
                    height: 6,
                    margin: const EdgeInsets.only(top: 6, right: 8),
                    decoration: const BoxDecoration(
                      color: Color(0xFF0D76FF),
                      shape: BoxShape.circle,
                    ),
                  ),
                  Expanded(
                    child: Text(
                      activity,
                      style: GoogleFonts.instrumentSans(
                        fontSize: widget.isCompact ? 12 : 14,
                        color: const Color(0xFF2D3748),
                        height: 1.4,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildDestinationBasedItinerary() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: widget.itinerary.activities.entries.map((entry) {
          final destination = entry.key;
          final activities = entry.value;
          
          return _buildDestinationCard(destination, activities);
        }).toList(),
      ),
    );
  }

  Widget _buildDestinationCard(String destination, List<String> activities) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFFE2E8F0),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Destination header
          Row(
            children: [
              const Icon(
                Icons.location_on,
                size: 20,
                color: Color(0xFF0D76FF),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  destination,
                  style: GoogleFonts.instrumentSans(
                    fontSize: widget.isCompact ? 14 : 16,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF0D76FF),
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // Activities
          ...activities.map((activity) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 6,
                    height: 6,
                    margin: const EdgeInsets.only(top: 6, right: 12),
                    decoration: const BoxDecoration(
                      color: Color(0xFF0D76FF),
                      shape: BoxShape.circle,
                    ),
                  ),
                  Expanded(
                    child: Text(
                      activity,
                      style: GoogleFonts.instrumentSans(
                        fontSize: widget.isCompact ? 12 : 14,
                        color: const Color(0xFF2D3748),
                        height: 1.4,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ],
      ),
    );
  }
}
