import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../services/auth_service.dart';
import '../services/subscription_service.dart';

/// Service for managing daily chat limits and tracking usage
class ChatLimitService {
  static const String _chatCountKey = 'daily_chat_count';
  static const String _lastResetDateKey = 'last_reset_date';

  // Chat limits per user tier
  static const int _freeTierDailyLimit = 35;
  static const int _premiumTierDailyLimit = 1000;

  static int _currentChatCount = 0;
  static DateTime? _lastResetDate;
  static bool _isInitialized = false;

  /// Initialize the chat limit service
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      if (kDebugMode) {
        print('ChatLimitService: Initializing...');
      }

      // Load local data first for immediate availability
      await _loadLocalData();

      // Check if we need to reset daily count
      await _checkAndResetDailyCount();

      // Sync with Supabase if user is authenticated
      await _syncWithSupabase();

      _isInitialized = true;

      if (kDebugMode) {
        print(
            'ChatLimitService: Initialized with ${_currentChatCount} chats used today');
      }
    } catch (error) {
      if (kDebugMode) {
        print('ChatLimitService: Initialization failed: $error');
      }

      // Fallback to safe defaults
      _currentChatCount = 0;
      _lastResetDate = DateTime.now();
      _isInitialized = true;
    }
  }

  /// Check if user can send a chat message
  static Future<bool> canSendChat() async {
    if (!_isInitialized) {
      await initialize();
    }

    await _checkAndResetDailyCount();

    final dailyLimit = await _getDailyLimit();
    final canSend = _currentChatCount < dailyLimit;

    if (kDebugMode) {
      print(
          'ChatLimitService: Can send chat: $canSend ($_currentChatCount/$dailyLimit)');
    }

    return canSend;
  }

  /// Increment chat count after successful message
  static Future<void> incrementChatCount() async {
    if (!_isInitialized) {
      await initialize();
    }

    await _checkAndResetDailyCount();

    _currentChatCount++;

    // Save locally immediately
    await _saveLocalData();

    // Sync with Supabase in background
    _syncWithSupabase();

    if (kDebugMode) {
      print('ChatLimitService: Incremented chat count to $_currentChatCount');
    }
  }

  /// Get current chat usage stats
  static Future<Map<String, int>> getChatUsageStats() async {
    if (!_isInitialized) {
      await initialize();
    }

    await _checkAndResetDailyCount();

    final dailyLimit = await _getDailyLimit();
    final remaining = (dailyLimit - _currentChatCount).clamp(0, dailyLimit);

    return {
      'used': _currentChatCount,
      'limit': dailyLimit,
      'remaining': remaining,
    };
  }

  /// Get the daily limit based on user subscription status
  static Future<int> _getDailyLimit() async {
    try {
      final isSubscribed = await SubscriptionService.hasActiveSubscription();
      return isSubscribed ? _premiumTierDailyLimit : _freeTierDailyLimit;
    } catch (error) {
      if (kDebugMode) {
        print('ChatLimitService: Error checking subscription status: $error');
      }
      // Default to free tier limit if we can't determine subscription status
      return _freeTierDailyLimit;
    }
  }

  /// Check if user is subscribed (premium)
  static Future<bool> isSubscriptionActive() async {
    try {
      return await SubscriptionService.hasActiveSubscription();
    } catch (error) {
      if (kDebugMode) {
        print('ChatLimitService: Error checking subscription status: $error');
      }
      return false;
    }
  }

  /// Check if daily count needs to be reset (at midnight)
  static Future<void> _checkAndResetDailyCount() async {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    if (_lastResetDate == null) {
      _lastResetDate = today;
      return;
    }

    final lastReset = DateTime(
        _lastResetDate!.year, _lastResetDate!.month, _lastResetDate!.day);

    if (today.isAfter(lastReset)) {
      // Reset daily count
      _currentChatCount = 0;
      _lastResetDate = today;

      await _saveLocalData();

      if (kDebugMode) {
        print('ChatLimitService: Daily chat count reset for new day');
      }
    }
  }

  /// Load data from SharedPreferences
  static Future<void> _loadLocalData() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      _currentChatCount = prefs.getInt(_chatCountKey) ?? 0;

      final lastResetString = prefs.getString(_lastResetDateKey);
      if (lastResetString != null) {
        _lastResetDate = DateTime.tryParse(lastResetString);
      }

      if (kDebugMode) {
        print(
            'ChatLimitService: Loaded local data - count: $_currentChatCount, last reset: $_lastResetDate');
      }
    } catch (error) {
      if (kDebugMode) {
        print('ChatLimitService: Error loading local data: $error');
      }
    }
  }

  /// Save data to SharedPreferences
  static Future<void> _saveLocalData() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      await prefs.setInt(_chatCountKey, _currentChatCount);

      if (_lastResetDate != null) {
        await prefs.setString(
            _lastResetDateKey, _lastResetDate!.toIso8601String());
      }

      if (kDebugMode) {
        print('ChatLimitService: Saved local data');
      }
    } catch (error) {
      if (kDebugMode) {
        print('ChatLimitService: Error saving local data: $error');
      }
    }
  }

  /// Sync chat count with Supabase user metadata
  static Future<void> _syncWithSupabase() async {
    try {
      final user = Supabase.instance.client.auth.currentUser;
      if (user == null) {
        if (kDebugMode) {
          print('ChatLimitService: No authenticated user for Supabase sync');
        }
        return;
      }

      // Get current metadata
      final currentMetadata = user.userMetadata ?? {};

      // Prepare chat limit data
      final chatLimitData = {
        'daily_chat_count': _currentChatCount,
        'last_reset_date': _lastResetDate?.toIso8601String(),
        'last_updated': DateTime.now().toIso8601String(),
      };

      // Update user metadata
      await Supabase.instance.client.auth.updateUser(
        UserAttributes(
          data: {
            ...currentMetadata,
            'chat_limits': chatLimitData,
          },
        ),
      );

      if (kDebugMode) {
        print('ChatLimitService: Synced with Supabase successfully');
      }
    } catch (error) {
      if (kDebugMode) {
        print('ChatLimitService: Error syncing with Supabase: $error');
      }
      // Don't throw error - local data is still valid
    }
  }

  /// Load chat count from Supabase user metadata
  static Future<void> _loadFromSupabase() async {
    try {
      final user = Supabase.instance.client.auth.currentUser;
      if (user == null) return;

      final metadata = user.userMetadata;
      final chatLimitData = metadata?['chat_limits'] as Map<String, dynamic>?;

      if (chatLimitData != null) {
        final supabaseChatCount =
            chatLimitData['daily_chat_count'] as int? ?? 0;
        final supabaseLastReset = chatLimitData['last_reset_date'] as String?;

        // Use Supabase data if it's more recent or if local data is missing
        if (supabaseLastReset != null) {
          final supabaseResetDate = DateTime.tryParse(supabaseLastReset);

          if (_lastResetDate == null ||
              (supabaseResetDate != null &&
                  supabaseResetDate.isAfter(_lastResetDate!))) {
            _currentChatCount = supabaseChatCount;
            _lastResetDate = supabaseResetDate;

            // Save updated data locally
            await _saveLocalData();

            if (kDebugMode) {
              print('ChatLimitService: Updated from Supabase data');
            }
          }
        }
      }
    } catch (error) {
      if (kDebugMode) {
        print('ChatLimitService: Error loading from Supabase: $error');
      }
    }
  }

  /// Force refresh from Supabase
  static Future<void> refreshFromSupabase() async {
    await _loadFromSupabase();
  }

  /// Get debug information
  static Map<String, dynamic> getDebugInfo() {
    return {
      'isInitialized': _isInitialized,
      'currentChatCount': _currentChatCount,
      'lastResetDate': _lastResetDate?.toIso8601String(),
      'freeTierLimit': _freeTierDailyLimit,
      'premiumTierLimit': _premiumTierDailyLimit,
    };
  }

  /// Reset chat count (for testing purposes)
  static Future<void> resetChatCount() async {
    _currentChatCount = 0;
    _lastResetDate = DateTime.now();
    await _saveLocalData();
    await _syncWithSupabase();

    if (kDebugMode) {
      print('ChatLimitService: Chat count manually reset');
    }
  }
}
