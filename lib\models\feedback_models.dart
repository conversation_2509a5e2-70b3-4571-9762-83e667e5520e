// Removed Hive dependency - now using SharedPreferences with JSON serialization

/// Enum for feedback types
enum FeedbackType {
  like,
  dislike,
}

/// Enum for predefined feedback reasons
enum FeedbackReason {
  inaccurateInformation,
  notHelpful,
  tooLong,
  tooShort,
  offTopic,
  inappropriate,
  other,
}

/// Extension to get display text for feedback reasons
extension FeedbackReasonExtension on FeedbackReason {
  String get displayText {
    switch (this) {
      case FeedbackReason.inaccurateInformation:
        return 'Inaccurate information';
      case FeedbackReason.notHelpful:
        return 'Not helpful';
      case FeedbackReason.tooLong:
        return 'Too long';
      case FeedbackReason.tooShort:
        return 'Too short';
      case FeedbackReason.offTopic:
        return 'Off-topic';
      case FeedbackReason.inappropriate:
        return 'Inappropriate content';
      case FeedbackReason.other:
        return 'Other';
    }
  }
}

/// Model for storing AI response feedback
class AIResponseFeedback {
  String id;
  String userId;
  String userMessage;
  String aiResponse;
  FeedbackType feedbackType;
  FeedbackReason? feedbackReason;
  String? customFeedbackText;
  DateTime timestamp;
  List<String> conversationContext;
  String sessionId;
  bool isSynced;
  String? messageId; // To track which specific message was rated

  AIResponseFeedback({
    required this.id,
    required this.userId,
    required this.userMessage,
    required this.aiResponse,
    required this.feedbackType,
    this.feedbackReason,
    this.customFeedbackText,
    required this.timestamp,
    required this.conversationContext,
    required this.sessionId,
    this.isSynced = false,
    this.messageId,
  });

  /// Convert to CSV row format for Google Sheets
  List<String> toCsvRow() {
    return [
      id,
      userId,
      userMessage,
      aiResponse,
      feedbackType.name,
      feedbackReason?.name ?? '',
      customFeedbackText ?? '',
      timestamp.toIso8601String(),
      conversationContext.join('|'),
      sessionId,
      isSynced.toString(),
      messageId ?? '',
    ];
  }

  /// Create from CSV row
  static AIResponseFeedback fromCsvRow(List<String> row) {
    return AIResponseFeedback(
      id: row[0],
      userId: row[1],
      userMessage: row[2],
      aiResponse: row[3],
      feedbackType: FeedbackType.values.firstWhere(
        (e) => e.name == row[4],
        orElse: () => FeedbackType.like,
      ),
      feedbackReason: row[5].isNotEmpty
          ? FeedbackReason.values.firstWhere(
              (e) => e.name == row[5],
              orElse: () => FeedbackReason.other,
            )
          : null,
      customFeedbackText: row[6].isNotEmpty ? row[6] : null,
      timestamp: DateTime.parse(row[7]),
      conversationContext: row[8].split('|'),
      sessionId: row[9],
      isSynced: row[10] == 'true',
      messageId: row[11].isNotEmpty ? row[11] : null,
    );
  }

  /// Convert to JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'userMessage': userMessage,
      'aiResponse': aiResponse,
      'feedbackType': feedbackType.name,
      'feedbackReason': feedbackReason?.name,
      'customFeedbackText': customFeedbackText,
      'timestamp': timestamp.toIso8601String(),
      'conversationContext': conversationContext,
      'sessionId': sessionId,
      'isSynced': isSynced,
      'messageId': messageId,
    };
  }

  /// Create from JSON map
  factory AIResponseFeedback.fromJson(Map<String, dynamic> json) {
    return AIResponseFeedback(
      id: json['id'] as String,
      userId: json['userId'] as String,
      userMessage: json['userMessage'] as String,
      aiResponse: json['aiResponse'] as String,
      feedbackType: FeedbackType.values.firstWhere(
        (e) => e.name == json['feedbackType'],
        orElse: () => FeedbackType.like,
      ),
      feedbackReason: json['feedbackReason'] != null
          ? FeedbackReason.values.firstWhere(
              (e) => e.name == json['feedbackReason'],
              orElse: () => FeedbackReason.other,
            )
          : null,
      customFeedbackText: json['customFeedbackText'] as String?,
      timestamp: DateTime.parse(json['timestamp'] as String),
      conversationContext:
          List<String>.from(json['conversationContext'] as List),
      sessionId: json['sessionId'] as String,
      isSynced: json['isSynced'] as bool? ?? false,
      messageId: json['messageId'] as String?,
    );
  }

  /// Convert to map for JSON serialization (legacy method)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'userId': userId,
      'userMessage': userMessage,
      'aiResponse': aiResponse,
      'feedbackType': feedbackType.name,
      'feedbackReason': feedbackReason?.name,
      'customFeedbackText': customFeedbackText,
      'timestamp': timestamp.toIso8601String(),
      'conversationContext': conversationContext,
      'sessionId': sessionId,
      'isSynced': isSynced,
      'messageId': messageId,
    };
  }

  /// Create from map
  static AIResponseFeedback fromMap(Map<String, dynamic> map) {
    return AIResponseFeedback(
      id: map['id'],
      userId: map['userId'],
      userMessage: map['userMessage'],
      aiResponse: map['aiResponse'],
      feedbackType: FeedbackType.values.firstWhere(
        (e) => e.name == map['feedbackType'],
        orElse: () => FeedbackType.like,
      ),
      feedbackReason: map['feedbackReason'] != null
          ? FeedbackReason.values.firstWhere(
              (e) => e.name == map['feedbackReason'],
              orElse: () => FeedbackReason.other,
            )
          : null,
      customFeedbackText: map['customFeedbackText'],
      timestamp: DateTime.parse(map['timestamp']),
      conversationContext: List<String>.from(map['conversationContext']),
      sessionId: map['sessionId'],
      isSynced: map['isSynced'] ?? false,
      messageId: map['messageId'],
    );
  }
}

/// Model for feedback analytics and learning patterns
class FeedbackAnalytics {
  final int totalFeedbacks;
  final int likesCount;
  final int dislikesCount;
  final double likeRatio;
  final Map<FeedbackReason, int> reasonCounts;
  final List<String> commonIssues;
  final List<String> successPatterns;

  FeedbackAnalytics({
    required this.totalFeedbacks,
    required this.likesCount,
    required this.dislikesCount,
    required this.likeRatio,
    required this.reasonCounts,
    required this.commonIssues,
    required this.successPatterns,
  });
}

/// Model for AI learning insights
class AILearningInsight {
  final String pattern;
  final String recommendation;
  final double confidence;
  final int occurrences;

  AILearningInsight({
    required this.pattern,
    required this.recommendation,
    required this.confidence,
    required this.occurrences,
  });
}
