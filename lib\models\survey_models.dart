// Survey data models for TripWiseGo post-registration survey

class SurveyResponse {
  final String? id;
  final String userId;
  final String? hearAboutUs;
  final String? travelCompanion;
  final List<String> travelPriorities;
  final List<String> travelFrustrations;
  final List<String> preferredDestinationTypes;
  final DateTime completedAt;
  final bool isCompleted;
  final DateTime createdAt;
  final DateTime updatedAt;

  SurveyResponse({
    this.id,
    required this.userId,
    this.hearAboutUs,
    this.travelCompanion,
    this.travelPriorities = const [],
    this.travelFrustrations = const [],
    this.preferredDestinationTypes = const [],
    required this.completedAt,
    this.isCompleted = true,
    required this.createdAt,
    required this.updatedAt,
  });

  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      'user_id': userId,
      'hear_about_us': hearAboutUs,
      'travel_companion': travelCompanion,
      'travel_priorities': travelPriorities,
      'travel_frustrations': travelFrustrations,
      'preferred_destination_types': preferredDestinationTypes,
      'completed_at': completedAt.toIso8601String(),
      'is_completed': isCompleted,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  factory SurveyResponse.fromJson(Map<String, dynamic> json) {
    return SurveyResponse(
      id: json['id'],
      userId: json['user_id'] ?? '',
      hearAboutUs: json['hear_about_us'],
      travelCompanion: json['travel_companion'],
      travelPriorities: List<String>.from(json['travel_priorities'] ?? []),
      travelFrustrations: List<String>.from(json['travel_frustrations'] ?? []),
      preferredDestinationTypes:
          List<String>.from(json['preferred_destination_types'] ?? []),
      completedAt: DateTime.parse(
          json['completed_at'] ?? DateTime.now().toIso8601String()),
      isCompleted: json['is_completed'] ?? false,
      createdAt: DateTime.parse(
          json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(
          json['updated_at'] ?? DateTime.now().toIso8601String()),
    );
  }
}

// Survey question options and constants
class SurveyOptions {
  // Q1: How did you hear about us?
  static const List<String> hearAboutUsOptions = [
    'Facebook',
    'Google',
    'TikTok',
    'Instagram',
    'Others',
  ];

  // Q2: Travel frustrations (multi-select)
  static const List<String> travelFrustrations = [
    'Overwhelming options/info',
    'Unreliable pricing or availability',
    'Difficulty coordinating with others',
    'Fear of missing hidden gems',
    'Language barriers',
    'Other...',
  ];

  // Q3: Travel companion (single select)
  static const List<String> travelCompanions = [
    'Solo traveler',
    'Partner/Spouse',
    'Friends',
    'Family with kids',
    'Business colleagues',
    'Other...',
  ];

  // Q4: Destination types (multi-select)
  static const List<String> destinationTypes = [
    'Popular tourist hubs (e.g., Paris, NYC)',
    'Offbeat/local gems (e.g., small towns)',
    'Nature/outdoor adventures (e.g., national parks)',
    'Urban/city experiences (e.g., museums, nightlife)',
    'Mix of all the above',
    'Other...',
  ];

  // Q5: Travel priorities (multi-select)
  static const List<String> travelPriorities = [
    'Trying local food',
    'Adventure activities',
    'Relaxation & wellness',
    'Cultural/historical sites',
    'Photography/scenic spots',
    'Shopping',
  ];
}
