import 'package:flutter_test/flutter_test.dart';
import 'package:tripwisego/models/chat_history_models.dart';
import 'package:tripwisego/services/google_search_service.dart';

void main() {
  group('Chat Persistence Logic Tests', () {
    test('should serialize and deserialize GoogleSearchResult correctly', () {
      // Create a mock GoogleSearchResult
      final searchResult = GoogleSearchResult(
        items: [
          GoogleSearchItem(
            title: 'Tokyo Travel Guide',
            link: 'https://example.com/tokyo',
            snippet: 'Tokyo is an amazing destination with rich culture...',
            displayLink: 'example.com',
          ),
          GoogleSearchItem(
            title: 'Best Restaurants in Tokyo',
            link: 'https://example.com/restaurants',
            snippet: 'Discover the best dining experiences in Tokyo...',
            displayLink: 'example.com',
          ),
        ],
        searchInformation: '0.45 seconds',
        totalResults: 2,
      );

      // Serialize to JSON
      final jsonString = ChatMessageModel.serializeSearchResult(searchResult);
      expect(jsonString, isNotNull);
      expect(jsonString, contains('Tokyo Travel Guide'));
      expect(jsonString, contains('Best Restaurants in Tokyo'));

      // Deserialize back to GoogleSearchResult
      final deserializedResult =
          ChatMessageModel.deserializeSearchResult(jsonString);
      expect(deserializedResult, isNotNull);
      expect(deserializedResult!.items.length, equals(2));
      expect(deserializedResult.items[0].title, equals('Tokyo Travel Guide'));
      expect(deserializedResult.items[1].title,
          equals('Best Restaurants in Tokyo'));
      expect(deserializedResult.totalResults, equals(2));
      expect(deserializedResult.searchInformation, equals('0.45 seconds'));
    });

    test('should handle null search results gracefully', () {
      final jsonString = ChatMessageModel.serializeSearchResult(null);
      expect(jsonString, isNull);

      final deserializedResult = ChatMessageModel.deserializeSearchResult(null);
      expect(deserializedResult, isNull);

      final deserializedFromEmpty =
          ChatMessageModel.deserializeSearchResult('');
      expect(deserializedFromEmpty, isNull);
    });

    test('should handle malformed JSON gracefully', () {
      final deserializedResult =
          ChatMessageModel.deserializeSearchResult('invalid json');
      expect(deserializedResult, isNull);
    });

    test('should create ChatMessageModel from map correctly', () {
      final messageMap = {
        'text': 'Hello, how can I help you?',
        'isUser': false,
        'timestamp': DateTime.now(),
        'imagePath': '/path/to/image.jpg',
        'isImageMessage': true,
        'isWebSearchMessage': false,
        'searchQuery': null,
        'searchResultData': null,
      };

      final messageModel = ChatMessageModel.fromMap(messageMap);
      expect(messageModel.text, equals('Hello, how can I help you?'));
      expect(messageModel.isUser, isFalse);
      expect(messageModel.imagePath, equals('/path/to/image.jpg'));
      expect(messageModel.isImageMessage, isTrue);
      expect(messageModel.isWebSearchMessage, isFalse);
      expect(messageModel.searchQuery, isNull);
      expect(messageModel.searchResultData, isNull);
    });

    test('should convert ChatMessageModel to map correctly', () {
      final messageModel = ChatMessageModel(
        id: '123',
        text: 'Test message',
        isUser: true,
        timestamp: DateTime.now(),
        imagePath: null,
        isImageMessage: false,
        isWebSearchMessage: true,
        searchQuery: 'test query',
        searchResultData: '{"test": "data"}',
      );

      final map = messageModel.toMap();
      expect(map['text'], equals('Test message'));
      expect(map['isUser'], isTrue);
      expect(map['isImageMessage'], isFalse);
      expect(map['isWebSearchMessage'], isTrue);
      expect(map['searchQuery'], equals('test query'));
      expect(map['searchResultData'], equals('{"test": "data"}'));
    });
  });
}
