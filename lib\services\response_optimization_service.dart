import 'dart:async';
import 'dart:convert';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/feedback_models.dart';
import '../models/learning_models.dart';

/// Service for optimizing AI response quality based on feedback patterns
class ResponseOptimizationService {
  static const String _optimizationDataKey = 'response_optimization_data';
  static const String _qualityMetricsKey = 'response_quality_metrics';

  // Singleton pattern
  static ResponseOptimizationService? _instance;
  static ResponseOptimizationService get instance =>
      _instance ??= ResponseOptimizationService._();
  ResponseOptimizationService._();

  // Internal state
  bool _isInitialized = false;
  Map<String, dynamic> _optimizationData = {};
  Map<String, dynamic> _qualityMetrics = {};

  /// Initialize the response optimization service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadOptimizationData();
      await _loadQualityMetrics();
      _isInitialized = true;

      if (kDebugMode) {
        print('Response Optimization Service: Initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Response Optimization Service: Initialization failed - $e');
      }
    }
  }

  /// Analyze response quality and generate optimization recommendations
  Future<ResponseOptimizationResult> analyzeResponseQuality(
    String userQuery,
    String aiResponse,
    List<AIResponseFeedback> historicalFeedback,
  ) async {
    final result = ResponseOptimizationResult();

    // Calculate base quality score
    result.qualityScore = _calculateResponseQualityScore(
        userQuery, aiResponse, historicalFeedback);

    // Identify potential improvements
    result.improvements =
        _identifyImprovements(userQuery, aiResponse, historicalFeedback);

    // Analyze response characteristics
    result.characteristics = _analyzeResponseCharacteristics(aiResponse);

    // Generate context-aware recommendations
    result.contextRecommendations =
        _generateContextRecommendations(userQuery, aiResponse);

    // Calculate travel-specific score
    result.travelSpecificScore =
        _calculateTravelSpecificScore(userQuery, aiResponse);

    // Update optimization data
    await _updateOptimizationData(userQuery, aiResponse, result);

    return result;
  }

  /// Calculate comprehensive response quality score
  double _calculateResponseQualityScore(
    String userQuery,
    String aiResponse,
    List<AIResponseFeedback> historicalFeedback,
  ) {
    double score = 0.0;
    int factors = 0;

    // Length appropriateness (20% weight)
    final lengthScore = _calculateLengthScore(userQuery, aiResponse);
    score += lengthScore * 0.2;
    factors++;

    // Relevance to query (25% weight)
    final relevanceScore = _calculateRelevanceScore(userQuery, aiResponse);
    score += relevanceScore * 0.25;
    factors++;

    // Travel-specific quality (20% weight)
    final travelScore = _calculateTravelSpecificScore(userQuery, aiResponse);
    score += travelScore * 0.2;
    factors++;

    // Historical performance (15% weight)
    final historicalScore =
        _calculateHistoricalScore(aiResponse, historicalFeedback);
    score += historicalScore * 0.15;
    factors++;

    // Structure and clarity (10% weight)
    final structureScore = _calculateStructureScore(aiResponse);
    score += structureScore * 0.1;
    factors++;

    // Actionability (10% weight)
    final actionabilityScore = _calculateActionabilityScore(aiResponse);
    score += actionabilityScore * 0.1;
    factors++;

    return factors > 0 ? score : 0.0;
  }

  /// Calculate length appropriateness score
  double _calculateLengthScore(String userQuery, String aiResponse) {
    final queryLength = userQuery.length;
    final responseLength = aiResponse.length;

    // Determine optimal response length based on query complexity
    int optimalLength;
    if (queryLength < 50) {
      optimalLength = 200; // Short queries need concise answers
    } else if (queryLength < 150) {
      optimalLength = 400; // Medium queries need moderate detail
    } else {
      optimalLength = 600; // Complex queries need detailed responses
    }

    // Calculate score based on deviation from optimal length
    final deviation = (responseLength - optimalLength).abs() / optimalLength;
    return math.max(0.0, 1.0 - deviation);
  }

  /// Calculate relevance score based on keyword matching
  double _calculateRelevanceScore(String userQuery, String aiResponse) {
    final queryWords = _extractKeywords(userQuery.toLowerCase());
    final responseWords = _extractKeywords(aiResponse.toLowerCase());

    if (queryWords.isEmpty) return 0.5; // Neutral score for empty queries

    int matchCount = 0;
    for (final queryWord in queryWords) {
      if (responseWords.contains(queryWord)) {
        matchCount++;
      }
    }

    return matchCount / queryWords.length;
  }

  /// Calculate travel-specific quality score
  double _calculateTravelSpecificScore(String userQuery, String aiResponse) {
    final travelKeywords = [
      'destination',
      'travel',
      'trip',
      'vacation',
      'hotel',
      'flight',
      'itinerary',
      'attractions',
      'restaurant',
      'budget',
      'visa',
      'passport',
      'currency',
      'weather',
      'culture',
      'local',
      'guide'
    ];

    final queryLower = userQuery.toLowerCase();
    final responseLower = aiResponse.toLowerCase();

    // Check if query is travel-related
    final isTravelQuery =
        travelKeywords.any((keyword) => queryLower.contains(keyword));
    if (!isTravelQuery) return 0.5; // Neutral score for non-travel queries

    // Count travel-specific elements in response
    int travelElements = 0;

    // Check for specific travel advice patterns
    if (responseLower.contains('recommend') ||
        responseLower.contains('suggest')) {
      travelElements++;
    }
    if (responseLower.contains('budget') || responseLower.contains('cost')) {
      travelElements++;
    }
    if (responseLower.contains('local') || responseLower.contains('insider')) {
      travelElements++;
    }
    if (responseLower.contains('tip') || responseLower.contains('advice')) {
      travelElements++;
    }
    if (responseLower.contains('experience') ||
        responseLower.contains('enjoy')) {
      travelElements++;
    }

    // Check for travel-specific information
    final travelInfoCount = travelKeywords
        .where((keyword) => responseLower.contains(keyword))
        .length;

    return math.min(1.0, (travelElements + travelInfoCount * 0.1) / 5.0);
  }

  /// Calculate historical performance score
  double _calculateHistoricalScore(
      String aiResponse, List<AIResponseFeedback> historicalFeedback) {
    if (historicalFeedback.isEmpty) return 0.5; // Neutral score for no history

    // Find similar responses in history
    final similarResponses = historicalFeedback.where((feedback) {
      return _calculateSimilarity(aiResponse, feedback.aiResponse) > 0.7;
    }).toList();

    if (similarResponses.isEmpty) return 0.5; // No similar responses

    // Calculate average feedback score for similar responses
    final positiveCount = similarResponses
        .where((f) => f.feedbackType == FeedbackType.like)
        .length;
    return positiveCount / similarResponses.length;
  }

  /// Calculate structure and clarity score
  double _calculateStructureScore(String aiResponse) {
    double score = 0.0;
    int factors = 0;

    // Check for proper paragraph structure
    final paragraphs =
        aiResponse.split('\n\n').where((p) => p.trim().isNotEmpty).length;
    if (paragraphs > 1) {
      score += 0.3;
    }
    factors++;

    // Check for bullet points or lists
    if (aiResponse.contains('•') ||
        aiResponse.contains('-') ||
        aiResponse.contains('1.')) {
      score += 0.3;
    }
    factors++;

    // Check for clear sections or headers
    if (aiResponse.contains('**') || aiResponse.contains('#')) {
      score += 0.2;
    }
    factors++;

    // Check sentence length variety
    final sentences = aiResponse
        .split(RegExp(r'[.!?]'))
        .where((s) => s.trim().isNotEmpty)
        .toList();
    if (sentences.isNotEmpty) {
      final avgLength =
          sentences.fold(0, (sum, s) => sum + s.length) / sentences.length;
      if (avgLength > 20 && avgLength < 100) {
        // Good sentence length range
        score += 0.2;
      }
    }
    factors++;

    return factors > 0 ? score / factors : 0.0;
  }

  /// Calculate actionability score
  double _calculateActionabilityScore(String aiResponse) {
    final actionWords = [
      'visit',
      'book',
      'check',
      'try',
      'explore',
      'consider',
      'plan',
      'prepare',
      'pack',
      'research',
      'contact',
      'download'
    ];

    final responseLower = aiResponse.toLowerCase();
    final actionCount =
        actionWords.where((word) => responseLower.contains(word)).length;

    // Check for specific recommendations
    int specificCount = 0;
    if (responseLower.contains('i recommend') ||
        responseLower.contains('i suggest')) {
      specificCount++;
    }
    if (responseLower.contains('you should') ||
        responseLower.contains('you could')) {
      specificCount++;
    }
    if (responseLower.contains('consider') || responseLower.contains('try')) {
      specificCount++;
    }

    return math.min(1.0, (actionCount * 0.1 + specificCount * 0.3));
  }

  /// Extract keywords from text
  List<String> _extractKeywords(String text) {
    final stopWords = {
      'the',
      'a',
      'an',
      'and',
      'or',
      'but',
      'in',
      'on',
      'at',
      'to',
      'for',
      'of',
      'with',
      'by',
      'is',
      'was',
      'are',
      'were',
      'be',
      'been',
      'have',
      'has',
      'had',
      'do',
      'does',
      'did',
      'will',
      'would',
      'could',
      'should',
      'may',
      'might',
      'can',
      'this',
      'that',
      'these',
      'those',
      'i',
      'you',
      'he',
      'she',
      'it',
      'we',
      'they',
      'me',
      'him',
      'her',
      'us',
      'them'
    };

    return text
        .split(RegExp(r'\W+'))
        .where((word) =>
            word.length > 2 && !stopWords.contains(word.toLowerCase()))
        .map((word) => word.toLowerCase())
        .toList();
  }

  /// Calculate similarity between two texts
  double _calculateSimilarity(String text1, String text2) {
    final words1 = _extractKeywords(text1).toSet();
    final words2 = _extractKeywords(text2).toSet();

    if (words1.isEmpty && words2.isEmpty) return 1.0;
    if (words1.isEmpty || words2.isEmpty) return 0.0;

    final intersection = words1.intersection(words2).length;
    final union = words1.union(words2).length;

    return intersection / union; // Jaccard similarity
  }

  /// Identify specific improvements for the response
  List<String> _identifyImprovements(
    String userQuery,
    String aiResponse,
    List<AIResponseFeedback> historicalFeedback,
  ) {
    final improvements = <String>[];

    // Check response length
    final lengthScore = _calculateLengthScore(userQuery, aiResponse);
    if (lengthScore < 0.7) {
      if (aiResponse.length < 100) {
        improvements.add('Response could be more detailed and informative');
      } else {
        improvements.add('Response could be more concise and focused');
      }
    }

    // Check travel-specific content
    final travelScore = _calculateTravelSpecificScore(userQuery, aiResponse);
    if (travelScore < 0.6 && _isTravelQuery(userQuery)) {
      improvements
          .add('Include more travel-specific advice and local insights');
    }

    // Check actionability
    final actionScore = _calculateActionabilityScore(aiResponse);
    if (actionScore < 0.5) {
      improvements
          .add('Provide more actionable recommendations and specific steps');
    }

    // Check structure
    final structureScore = _calculateStructureScore(aiResponse);
    if (structureScore < 0.6) {
      improvements.add(
          'Improve response structure with clear sections or bullet points');
    }

    // Analyze historical patterns
    final negativePatterns = _analyzeNegativePatterns(historicalFeedback);
    for (final pattern in negativePatterns) {
      if (aiResponse.toLowerCase().contains(pattern.toLowerCase())) {
        improvements.add(
            'Avoid pattern that historically receives negative feedback: $pattern');
      }
    }

    return improvements;
  }

  /// Analyze response characteristics
  Map<String, dynamic> _analyzeResponseCharacteristics(String aiResponse) {
    return {
      'length': aiResponse.length,
      'word_count': aiResponse.split(RegExp(r'\s+')).length,
      'paragraph_count':
          aiResponse.split('\n\n').where((p) => p.trim().isNotEmpty).length,
      'sentence_count': aiResponse
          .split(RegExp(r'[.!?]'))
          .where((s) => s.trim().isNotEmpty)
          .length,
      'has_bullet_points': aiResponse.contains('•') || aiResponse.contains('-'),
      'has_formatting': aiResponse.contains('**') || aiResponse.contains('#'),
      'question_count': RegExp(r'\?').allMatches(aiResponse).length,
      'exclamation_count': RegExp(r'!').allMatches(aiResponse).length,
    };
  }

  /// Generate context-aware recommendations
  List<String> _generateContextRecommendations(
      String userQuery, String aiResponse) {
    final recommendations = <String>[];
    final queryLower = userQuery.toLowerCase();

    // Query type specific recommendations
    if (queryLower.contains('budget') || queryLower.contains('cheap')) {
      recommendations
          .add('Include specific budget ranges and cost-saving tips');
    }

    if (queryLower.contains('first time') || queryLower.contains('beginner')) {
      recommendations
          .add('Provide beginner-friendly advice and basic information');
    }

    if (queryLower.contains('luxury') || queryLower.contains('premium')) {
      recommendations.add('Focus on high-end options and premium experiences');
    }

    if (queryLower.contains('family') || queryLower.contains('kids')) {
      recommendations
          .add('Include family-friendly options and child considerations');
    }

    if (queryLower.contains('solo') || queryLower.contains('alone')) {
      recommendations.add('Address solo travel safety and social aspects');
    }

    // Urgency indicators
    if (queryLower.contains('urgent') ||
        queryLower.contains('asap') ||
        queryLower.contains('tomorrow')) {
      recommendations
          .add('Prioritize immediate actionable steps and quick solutions');
    }

    return recommendations;
  }

  /// Analyze negative feedback patterns
  List<String> _analyzeNegativePatterns(
      List<AIResponseFeedback> historicalFeedback) {
    final negativeResponses = historicalFeedback
        .where((f) => f.feedbackType == FeedbackType.dislike)
        .map((f) => f.aiResponse.toLowerCase())
        .toList();

    if (negativeResponses.isEmpty) return [];

    // Extract common phrases from negative responses
    final commonPhrases = <String>[];
    final phraseMap = <String, int>{};

    for (final response in negativeResponses) {
      final words = response.split(RegExp(r'\W+'));
      for (int i = 0; i < words.length - 1; i++) {
        final phrase = '${words[i]} ${words[i + 1]}';
        if (phrase.length > 5) {
          phraseMap[phrase] = (phraseMap[phrase] ?? 0) + 1;
        }
      }
    }

    // Return phrases that appear in multiple negative responses
    final threshold = math.max(2, negativeResponses.length ~/ 3);
    for (final entry in phraseMap.entries) {
      if (entry.value >= threshold) {
        commonPhrases.add(entry.key);
      }
    }

    return commonPhrases.take(5).toList();
  }

  /// Check if query is travel-related
  bool _isTravelQuery(String query) {
    final travelKeywords = [
      'travel',
      'trip',
      'vacation',
      'holiday',
      'destination',
      'visit',
      'flight',
      'hotel',
      'itinerary',
      'places',
      'attractions',
      'tourism'
    ];

    final queryLower = query.toLowerCase();
    return travelKeywords.any((keyword) => queryLower.contains(keyword));
  }

  /// Update optimization data with new analysis
  Future<void> _updateOptimizationData(
    String userQuery,
    String aiResponse,
    ResponseOptimizationResult result,
  ) async {
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    // Update quality metrics
    _qualityMetrics['total_analyses'] =
        (_qualityMetrics['total_analyses'] ?? 0) + 1;
    _qualityMetrics['average_quality_score'] = _updateRunningAverage(
      _qualityMetrics['average_quality_score'] ?? 0.0,
      result.qualityScore,
      _qualityMetrics['total_analyses'],
    );

    // Store recent analysis for trend tracking
    final recentAnalyses =
        _optimizationData['recent_analyses'] as List<dynamic>? ?? [];
    recentAnalyses.add({
      'timestamp': timestamp,
      'quality_score': result.qualityScore,
      'travel_score': result.travelSpecificScore,
      'query_type': _categorizeQuery(userQuery),
    });

    // Keep only last 100 analyses
    if (recentAnalyses.length > 100) {
      recentAnalyses.removeRange(0, recentAnalyses.length - 100);
    }

    _optimizationData['recent_analyses'] = recentAnalyses;
    _optimizationData['last_updated'] = timestamp;

    await _saveOptimizationData();
    await _saveQualityMetrics();
  }

  /// Update running average
  double _updateRunningAverage(
      double currentAverage, double newValue, int count) {
    return ((currentAverage * (count - 1)) + newValue) / count;
  }

  /// Categorize query type
  String _categorizeQuery(String query) {
    final queryLower = query.toLowerCase();

    if (queryLower.contains('destination') || queryLower.contains('where')) {
      return 'destination_planning';
    }
    if (queryLower.contains('itinerary') || queryLower.contains('plan')) {
      return 'itinerary_help';
    }
    if (queryLower.contains('hotel') || queryLower.contains('accommodation')) {
      return 'accommodation';
    }
    if (queryLower.contains('flight') || queryLower.contains('transport')) {
      return 'transportation';
    }
    if (queryLower.contains('budget') || queryLower.contains('cost')) {
      return 'budget_planning';
    }
    if (queryLower.contains('activity') ||
        queryLower.contains('things to do')) {
      return 'activities';
    }

    return 'general_travel';
  }

  /// Get optimization statistics
  Map<String, dynamic> getOptimizationStatistics() {
    return {
      'total_analyses': _qualityMetrics['total_analyses'] ?? 0,
      'average_quality_score': _qualityMetrics['average_quality_score'] ?? 0.0,
      'recent_trend': _calculateRecentTrend(),
      'last_updated': _optimizationData['last_updated'],
    };
  }

  /// Calculate recent quality trend
  double _calculateRecentTrend() {
    final recentAnalyses =
        _optimizationData['recent_analyses'] as List<dynamic>? ?? [];
    if (recentAnalyses.length < 10) return 0.0;

    final recent = recentAnalyses
        .skip(math.max(0, recentAnalyses.length - 10))
        .map((a) => a['quality_score'] as double)
        .toList();
    final older = recentAnalyses
        .skip(math.max(0, recentAnalyses.length - 20))
        .take(10)
        .map((a) => a['quality_score'] as double)
        .toList();

    final recentAvg =
        recent.fold(0.0, (sum, score) => sum + score) / recent.length;
    final olderAvg =
        older.fold(0.0, (sum, score) => sum + score) / older.length;

    return recentAvg - olderAvg;
  }

  /// Load optimization data from storage
  Future<void> _loadOptimizationData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final dataJson = prefs.getString(_optimizationDataKey);

      if (dataJson != null) {
        _optimizationData = Map<String, dynamic>.from(json.decode(dataJson));
      }
    } catch (e) {
      if (kDebugMode) {
        print(
            'Response Optimization Service: Failed to load optimization data - $e');
      }
      _optimizationData = {};
    }
  }

  /// Save optimization data to storage
  Future<void> _saveOptimizationData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
          _optimizationDataKey, json.encode(_optimizationData));
    } catch (e) {
      if (kDebugMode) {
        print(
            'Response Optimization Service: Failed to save optimization data - $e');
      }
    }
  }

  /// Load quality metrics from storage
  Future<void> _loadQualityMetrics() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final metricsJson = prefs.getString(_qualityMetricsKey);

      if (metricsJson != null) {
        _qualityMetrics = Map<String, dynamic>.from(json.decode(metricsJson));
      }
    } catch (e) {
      if (kDebugMode) {
        print(
            'Response Optimization Service: Failed to load quality metrics - $e');
      }
      _qualityMetrics = {};
    }
  }

  /// Save quality metrics to storage
  Future<void> _saveQualityMetrics() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_qualityMetricsKey, json.encode(_qualityMetrics));
    } catch (e) {
      if (kDebugMode) {
        print(
            'Response Optimization Service: Failed to save quality metrics - $e');
      }
    }
  }
}

/// Result of response optimization analysis
class ResponseOptimizationResult {
  double qualityScore = 0.0;
  double travelSpecificScore = 0.0;
  List<String> improvements = [];
  Map<String, dynamic> characteristics = {};
  List<String> contextRecommendations = [];
}
