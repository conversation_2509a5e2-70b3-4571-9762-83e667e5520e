/// Generated file. Do not edit.
///
/// This file contains the localized strings for the TripWiseGo app.
/// To add a new localized string, add it to the appropriate .arb file
/// in the lib/l10n directory and run `flutter gen-l10n`.


import 'app_localizations.dart';

/// The translations for Portuguese (`pt`).
class AppLocalizationsPt extends AppLocalizations {
  AppLocalizationsPt([String locale = 'pt']) : super(locale);

  @override
  String get appTitle => 'TripWiseGo';

  @override
  String get welcome => 'Bem-vindo';

  @override
  String get guestUser => 'Usuário convidado';

  @override
  String get readyForAdventure => 'Pronto para sua próxima aventura';

  @override
  String get exploringAsGuest => 'Explorando o mundo como convidado';

  @override
  String get editProfile => 'Editar perfil';

  @override
  String get saveChanges => 'Salvar alterações';

  @override
  String get cancel => 'Cancelar';

  @override
  String get username => 'Nome de usuário';

  @override
  String get email => 'E-mail';

  @override
  String get profileUpdatedSuccessfully => 'Perfil atualizado com sucesso!';

  @override
  String failedToUpdateProfile(String error) {
    return 'Falha ao atualizar perfil: $error';
  }

  @override
  String get profilePictureUpdatedSuccessfully => 'Foto do perfil atualizada com sucesso!';

  @override
  String failedToUploadImage(String error) {
    return 'Falha ao carregar imagem: $error';
  }

  @override
  String get profileEditingNotAvailableForGuests => 'Edição de perfil não disponível para usuários convidados';

  @override
  String get profilePictureEditingNotAvailableForGuests => 'Edição de foto do perfil não disponível para usuários convidados';

  @override
  String get usernameCannotBeEmpty => 'Nome de usuário não pode estar vazio';

  @override
  String get usernameMustBeBetween2And30Characters => 'Nome de usuário deve ter entre 2 e 30 caracteres';

  @override
  String get plan => 'Plano';

  @override
  String get termsOfService => 'Termos de serviço';

  @override
  String get language => 'Idioma';

  @override
  String get privacyPolicy => 'Política de privacidade';

  @override
  String get support => 'Suporte';

  @override
  String get helpCenter => 'Central de ajuda';

  @override
  String get contactUs => 'Entre em contato';

  @override
  String get helpSupport => 'Help & Support';

  @override
  String get howCanWeHelpYou => 'How can we help you?';

  @override
  String get helpSupportGreeting => 'Hi! I\'m here to help you with TripwiseGO. You can ask me about app features, troubleshooting, or report any issues you\'re experiencing.';

  @override
  String get helpSupportWelcome => 'Welcome to TripwiseGO Support! Here are some things I can help you with:';

  @override
  String get helpSupportFeatures => '• App features and how to use them\n• Navigation and account help\n• Troubleshooting common issues\n• Reporting bugs or problems';

  @override
  String get helpSupportAskQuestion => 'Feel free to ask me anything or describe any issues you\'re having!';

  @override
  String get reportIssue => 'Report Issue';

  @override
  String get reportBug => 'Report Bug';

  @override
  String get reportProblem => 'Report Problem';

  @override
  String get issueCategory => 'Issue Category';

  @override
  String get bugReport => 'Bug Report';

  @override
  String get featureRequest => 'Feature Request';

  @override
  String get generalFeedback => 'General Feedback';

  @override
  String get accountIssue => 'Account Issue';

  @override
  String get technicalProblem => 'Technical Problem';

  @override
  String get yourName => 'Your Name';

  @override
  String get yourEmail => 'Your Email';

  @override
  String get issueDescription => 'Issue Description';

  @override
  String get describeIssueDetail => 'Please describe the issue in detail';

  @override
  String get optionalScreenshot => 'Screenshot (Optional)';

  @override
  String get submitReport => 'Submit Report';

  @override
  String get reportSubmitted => 'Report Submitted';

  @override
  String get reportSubmittedSuccess => 'Thank you! Your report has been submitted successfully. We\'ll look into it and get back to you if needed.';

  @override
  String get reportSubmissionFailed => 'Failed to submit report. Please try again later.';

  @override
  String get nameRequired => 'Name is required';

  @override
  String get emailRequired => 'Email is required';

  @override
  String get validEmailRequired => 'Please enter a valid email address';

  @override
  String get descriptionRequired => 'Issue description is required';

  @override
  String get selectCategory => 'Please select a category';

  @override
  String get subscription => 'Subscription';

  @override
  String get subscriptionActive => 'Subscription Active!';

  @override
  String get welcomeToPremium => 'Welcome to TripwiseGO Premium! Enjoy unlimited access to all features.';

  @override
  String get currentPlan => 'Current Plan';

  @override
  String get trial => 'Trial';

  @override
  String get active => 'Active';

  @override
  String trialEndsIn(int days) {
    return 'Trial ends in $days days';
  }

  @override
  String renewsIn(int days) {
    return 'Renews in $days days';
  }

  @override
  String get yourBenefits => 'Your Benefits';

  @override
  String get startPlanningTrip => 'Start Planning Your Trip';

  @override
  String get manageSubscription => 'Manage Subscription';

  @override
  String get basic => 'Basic';

  @override
  String get premium => 'Premium';

  @override
  String get basicPlaceRecommendations => 'Basic Place Recommendations';

  @override
  String get locationRecommendationSwiping => 'Location Recommendation Swiping (20 swipes/day)';

  @override
  String get aiPoweredTravelPlanner => 'AI-Powered Travel Planner';

  @override
  String get unlimitedQuizzes => 'Unlimited Quizzes & Fun Facts';

  @override
  String get noAds => 'No-Ads';

  @override
  String get oneMonth => '1 Month';

  @override
  String get threeMonths => '3 Months';

  @override
  String get fiveMonths => '5 Months';

  @override
  String dayFreeTrial(int days) {
    return '$days-day free trial';
  }

  @override
  String get billedMonthly => 'Billed monthly';

  @override
  String get billedTwiceAnnually => 'Billed twice annually';

  @override
  String get billedAnnually => 'Billed annually';

  @override
  String get save45Percent => 'SAVE 45%';

  @override
  String get continueButton => 'Continue';

  @override
  String get termsAndConditions => 'Terms and Conditions';

  @override
  String get failedToSubscribe => 'Failed to subscribe. Please try again.';

  @override
  String get signOut => 'Sair';

  @override
  String get selectLanguage => 'Selecionar idioma';

  @override
  String get chooseYourPreferredLanguage => 'Escolha seu idioma preferido';

  @override
  String get languageUpdatedSuccessfully => 'Idioma atualizado com sucesso!';

  @override
  String get home => 'Início';

  @override
  String get match => 'Combinação';

  @override
  String get chat => 'Chat';

  @override
  String get profile => 'Perfil';

  @override
  String get loading => 'Carregando...';

  @override
  String get error => 'Erro';

  @override
  String get retry => 'Tentar novamente';

  @override
  String get ok => 'OK';

  @override
  String get yes => 'Sim';

  @override
  String get no => 'Não';

  @override
  String get save => 'Salvar';

  @override
  String get delete => 'Excluir';

  @override
  String get edit => 'Editar';

  @override
  String get add => 'Adicionar';

  @override
  String get remove => 'Remover';

  @override
  String get close => 'Fechar';

  @override
  String get back => 'Voltar';

  @override
  String get next => 'Próximo';

  @override
  String get previous => 'Anterior';

  @override
  String get done => 'Concluído';

  @override
  String get search => 'Pesquisar';

  @override
  String get noResultsFound => 'Nenhum resultado encontrado';

  @override
  String get tryAgain => 'Tentar novamente';

  @override
  String get somethingWentWrong => 'Algo deu errado';

  @override
  String get networkError => 'Erro de rede. Verifique sua conexão.';

  @override
  String get serverError => 'Erro do servidor. Tente novamente mais tarde.';

  @override
  String get invalidInput => 'Entrada inválida';

  @override
  String get required => 'Obrigatório';

  @override
  String get optional => 'Opcional';

  @override
  String get itinerary => 'Itinerary';

  @override
  String get yourItineraries => 'Your Itineraries';

  @override
  String get startPlanningYourTrip => 'Start Planning Your Trip';

  @override
  String get tripPlanningFeaturesWillAppearHere => 'Your trip planning features will appear here. The persistent authentication is now working!';

  @override
  String get forYou => 'For You';

  @override
  String get liked => 'Liked';

  @override
  String get collab => 'Collab';

  @override
  String get collaborativeItinerary => 'Collaborative';

  @override
  String get endSession => 'End Session';

  @override
  String get logout => 'Logout';

  @override
  String logoutFailed(String error) {
    return 'Logout failed: $error';
  }

  @override
  String get noItineraryFound => 'Nenhum Itinerário Encontrado';

  @override
  String get askAiToCreateTravelPlan => 'Peça à nossa IA para criar um plano de viagem para você!';

  @override
  String get saturday => 'Sábado';

  @override
  String get tuesday => 'Terça-feira';

  @override
  String dayNumber(int number) {
    return 'Dia $number';
  }

  @override
  String get itineraryOverview => 'Visão Geral do Itinerário';

  @override
  String daysAndNights(int days, int nights) {
    return '${days}d ${nights}n';
  }

  @override
  String get hiImWanderlyAi => 'Olá, eu sou Wanderly AI 🌏';

  @override
  String get yourTravelAiAssistant => 'Seu assistente de viagem IA, como posso ajudá-lo hoje?';

  @override
  String get useThisBubbleChat => 'Use este chat de bolhas';

  @override
  String get aiAssistant => 'Assistente IA';

  @override
  String get chatHistory => 'Histórico do Chat';

  @override
  String get newChat => 'Novo Chat';

  @override
  String get addImage => 'Adicionar Imagem';

  @override
  String get camera => 'Câmera';

  @override
  String get gallery => 'Galeria';

  @override
  String get microphonePermissionRequired => 'Permissão do microfone é necessária para entrada de voz';

  @override
  String get speechRecognitionNotAvailable => 'Reconhecimento de fala não está disponível neste dispositivo';

  @override
  String get listening => 'Ouvindo...';

  @override
  String get deleteChat => 'Excluir Chat';

  @override
  String get deleteChatConfirmation => 'Tem certeza de que deseja excluir este chat? Esta ação não pode ser desfeita.';

  @override
  String get chatDeletedSuccessfully => 'Chat excluído com sucesso';

  @override
  String get pleaseEnterSearchQuery => 'Por favor, insira uma consulta de pesquisa';

  @override
  String get dailySearchLimitReached => 'Limite diário de pesquisa atingido. Você pode realizar 5 pesquisas por dia.';

  @override
  String get searchingTheWeb => 'Pesquisando na Web...';

  @override
  String get webSearchModeActive => 'Modo de Pesquisa Web Ativo';

  @override
  String get pleaseWaitWhileSearching => 'Por favor, aguarde enquanto procuro informações';

  @override
  String get yourNextMessageWillSearch => 'Sua próxima mensagem pesquisará na web';

  @override
  String get disableWebSearch => 'Desativar Pesquisa Web';

  @override
  String get enableWebSearch => 'Ativar Pesquisa Web';

  @override
  String get switchBackToAiChatMode => 'Voltar ao modo de chat IA';

  @override
  String get searchWebForCurrentInfo => 'Pesquisar informações atuais na web';

  @override
  String get pickImageFromGallery => 'Escolher Imagem da Galeria';

  @override
  String get uploadImageForAiAnalysis => 'Carregar imagem para análise IA';

  @override
  String get yourMessage => 'Sua Mensagem';

  @override
  String get wanderlyAi => 'Wanderly AI';

  @override
  String get webSearch => 'Pesquisa Web:';

  @override
  String get like => 'Curtir';

  @override
  String get dislike => 'Não Curtir';

  @override
  String get copy => 'Copiar';

  @override
  String get regenerate => 'Regenerar';

  @override
  String get failedToSubmitFeedback => 'Falha ao enviar feedback. Por favor, tente novamente.';

  @override
  String get thankYouForFeedback => 'Obrigado pelo seu feedback! 🙏';

  @override
  String get feedbackReceivedThanks => 'Feedback recebido. Obrigado por nos ajudar a melhorar! 🚀';

  @override
  String get responseCopiedToClipboard => 'Resposta copiada para a área de transferência';

  @override
  String get wanderlyAiIsTyping => 'Wanderly AI está digitando';

  @override
  String get stopGeneration => 'Parar Geração';

  @override
  String get youHaveChatsLeft => 'Você tem 10 chats restantes';

  @override
  String get enterSearchQuery => 'Digite sua consulta de pesquisa...';

  @override
  String get askMeAnythingOrLongPress => 'Pergunte-me qualquer coisa ou pressione e segure para falar...';

  @override
  String failedToPickImage(String error) {
    return 'Falha ao escolher imagem: $error';
  }

  @override
  String get failedToAnalyzeImage => 'Falha ao analisar imagem. Por favor, tente novamente.';

  @override
  String get responseGenerationStopped => 'Geração de resposta foi interrompida.';

  @override
  String get unknownDestination => 'Destino Desconhecido';

  @override
  String get congratulations => 'Congratulations';

  @override
  String get itsAMatch => 'It\'s a match';

  @override
  String get travelVibesAligned => 'Travel vibes aligned pack\nyour bags, you\'ve got a match!';

  @override
  String get matchedPreferences => 'Matched Preferences:';

  @override
  String get addToItinerary => 'Add to Itinerary';

  @override
  String get keepSwiping => 'Keep swiping';

  @override
  String get versionInfo => 'Version Info';

  @override
  String get appVersion => 'App Version';

  @override
  String get buildNumber => 'Build Number';

  @override
  String get packageName => 'Package Name';

  @override
  String get appName => 'App Name';

  @override
  String get buildSignature => 'Build Signature';

  @override
  String get installerStore => 'Installer Store';

  @override
  String get deviceInfo => 'Device Information';

  @override
  String get operatingSystem => 'Operating System';

  @override
  String get flutterVersion => 'Flutter Version';

  @override
  String get dartVersion => 'Dart Version';

  @override
  String get leaveAReview => 'Leave a Review';

  @override
  String get rateOurApp => 'Rate Our App';

  @override
  String get enjoyingTripWiseGo => 'Enjoying TripWiseGo?';

  @override
  String get helpUsImproveByLeavingReview => 'Help us improve by leaving a review on the app store. Your feedback means a lot to us!';

  @override
  String get rateNow => 'Rate Now';

  @override
  String get maybeLater => 'Maybe Later';

  @override
  String get reviewFeatureNotAvailable => 'Review feature is only available in production builds';

  @override
  String get unableToOpenStore => 'Unable to open app store. Please try again later.';

  @override
  String get thankYouForReview => 'Thank you for taking the time to review our app!';
}
