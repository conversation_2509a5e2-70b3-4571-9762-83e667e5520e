import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../generated/l10n/app_localizations.dart';

class TermsOfServiceScreen extends StatelessWidget {
  const TermsOfServiceScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F9FC),
      appBar: AppBar(
        backgroundColor: const Color(0xFFF7F9FC),
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(
            Icons.arrow_back_ios,
            color: Color(0xFF2D3748),
          ),
        ),
        title: Text(
          AppLocalizations.of(context).termsOfService,
          style: GoogleFonts.instrumentSans(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: const Color(0xFF2D3748),
          ),
        ),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildLastUpdated(),
            const SizedBox(height: 24),
            _buildSection(
              'Acceptance of Terms',
              'By downloading, installing, or using the TripwiseGO mobile application ("App"), you agree to be bound by these Terms of Service ("Terms"). If you do not agree to these Terms, do not use the App.',
            ),
            _buildSection(
              'Description of Service',
              'TripwiseGO is a travel planning application that provides AI-powered itinerary creation, collaborative trip planning, destination recommendations, and related travel services. The App uses artificial intelligence to help users plan and organize their travel experiences.',
            ),
            _buildSection(
              'User Accounts',
              'To access certain features, you must create an account. You are responsible for:\n\n• Maintaining the confidentiality of your account credentials\n• All activities that occur under your account\n• Providing accurate and current information\n• Notifying us immediately of any unauthorized use\n\nYou must be at least 13 years old to create an account.',
            ),
            _buildSection(
              'Prohibited Uses',
              'You agree not to:\n\n• Use the App for any unlawful purpose or in violation of these Terms\n• Attempt to gain unauthorized access to our systems\n• Upload malicious code, viruses, or harmful content\n• Harass, abuse, or harm other users\n• Impersonate any person or entity\n• Use automated systems to access the App without permission\n• Share inappropriate, offensive, or illegal content\n• Violate any applicable laws or regulations',
            ),
            _buildSection(
              'AI and Content Generation',
              'Our AI-powered features provide travel recommendations and itinerary suggestions. These are for informational purposes only. We do not guarantee the accuracy, completeness, or reliability of AI-generated content. You are responsible for verifying all travel information and making informed decisions.',
            ),
            _buildSection(
              'User Content',
              'You retain ownership of content you submit but grant us a worldwide, royalty-free license to use, modify, and display your content in connection with the App. You represent that you have the right to share any content you upload.',
            ),
            _buildSection(
              'Privacy and Data',
              'Your privacy is important to us. Please review our Privacy Policy, which explains how we collect, use, and protect your information. By using the App, you consent to our data practices as described in the Privacy Policy.',
            ),
            _buildSection(
              'Third-Party Services',
              'The App integrates with third-party services including Google services, mapping providers, and authentication services. Your use of these services is subject to their respective terms and policies.',
            ),
            _buildSection(
              'Intellectual Property',
              'The App and its content, features, and functionality are owned by TripwiseGO and are protected by international copyright, trademark, and other intellectual property laws. You may not reproduce, distribute, or create derivative works without our written permission.',
            ),
            _buildSection(
              'Disclaimers',
              'THE APP IS PROVIDED "AS IS" WITHOUT WARRANTIES OF ANY KIND. WE DISCLAIM ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT.\n\nWe do not guarantee that the App will be uninterrupted, secure, or error-free. Travel information may change, and you should verify all details independently.',
            ),
            _buildSection(
              'Limitation of Liability',
              'TO THE MAXIMUM EXTENT PERMITTED BY LAW, WE SHALL NOT BE LIABLE FOR ANY INDIRECT, INCIDENTAL, SPECIAL, CONSEQUENTIAL, OR PUNITIVE DAMAGES, INCLUDING LOST PROFITS, DATA, OR USE, ARISING FROM YOUR USE OF THE APP.\n\nOur total liability shall not exceed the amount you paid for the App in the 12 months preceding the claim.',
            ),
            _buildSection(
              'Indemnification',
              'You agree to indemnify and hold us harmless from any claims, damages, or expenses arising from your use of the App, violation of these Terms, or infringement of any rights of another party.',
            ),
            _buildSection(
              'Termination',
              'We may terminate or suspend your account and access to the App at any time, with or without notice, for conduct that we believe violates these Terms or is harmful to other users or us.\n\nUpon termination, your right to use the App ceases immediately.',
            ),
            _buildSection(
              'Changes to Terms',
              'We reserve the right to modify these Terms at any time. We will notify users of significant changes through the App or email. Continued use of the App after changes constitutes acceptance of the new Terms.',
            ),
            _buildSection(
              'Governing Law',
              'These Terms are governed by and construed in accordance with the laws of Indonesia, without regard to conflict of law principles. Any disputes shall be resolved in the courts of Indonesia.',
            ),
            _buildSection(
              'Contact Information',
              'If you have questions about these Terms, please contact us at:\n\nEmail: <EMAIL>',
            ),
            const SizedBox(height: 40),
          ],
        ),
      ),
    );
  }

  Widget _buildLastUpdated() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF0D76FF).withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF0D76FF).withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.info_outline,
            color: Color(0xFF0D76FF),
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'Last updated: July 4, 2025',
              style: GoogleFonts.instrumentSans(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: const Color(0xFF0D76FF),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSection(String title, String content) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: GoogleFonts.instrumentSans(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF2D3748),
            ),
          ),
          const SizedBox(height: 12),
          Text(
            content,
            style: GoogleFonts.instrumentSans(
              fontSize: 14,
              height: 1.6,
              color: const Color(0xFF4A5568),
            ),
          ),
        ],
      ),
    );
  }
}
