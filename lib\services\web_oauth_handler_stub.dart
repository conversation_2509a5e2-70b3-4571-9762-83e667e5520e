// Stub implementation for non-web platforms
class WebStorageImpl {
  String? operator [](String key) => null;
  void operator []=(String key, String value) {}
  void remove(String key) {}
}

class WebLocationImpl {
  String get href => '';
}

class WebHistoryImpl {
  void replaceState(dynamic data, String title, String url) {}
}

class WebWindowImpl {
  WebStorageImpl get sessionStorage => WebStorageImpl();
  WebLocationImpl get location => WebLocationImpl();
  WebHistoryImpl get history => WebHistoryImpl();
}

WebWindowImpl get window => WebWindowImpl();

// Stub functions for non-web platforms
String? getOAuthCallbackData() => null;
void removeOAuthCallbackData() {}
void setOAuthCallbackData(String data) {}
