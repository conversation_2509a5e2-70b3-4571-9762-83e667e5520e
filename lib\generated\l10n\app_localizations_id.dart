/// Generated file. Do not edit.
///
/// This file contains the localized strings for the TripWiseGo app.
/// To add a new localized string, add it to the appropriate .arb file
/// in the lib/l10n directory and run `flutter gen-l10n`.


import 'app_localizations.dart';

/// The translations for Indonesian (`id`).
class AppLocalizationsId extends AppLocalizations {
  AppLocalizationsId([String locale = 'id']) : super(locale);

  @override
  String get appTitle => 'TripWiseGo';

  @override
  String get welcome => 'Selamat datang';

  @override
  String get guestUser => 'Pengguna tamu';

  @override
  String get readyForAdventure => 'Siap untuk petualangan berikutnya';

  @override
  String get exploringAsGuest => 'Menjelajahi dunia sebagai tamu';

  @override
  String get editProfile => 'Edit profil';

  @override
  String get saveChanges => 'Simpan perubahan';

  @override
  String get cancel => 'Batal';

  @override
  String get username => '<PERSON>a pengguna';

  @override
  String get email => 'Email';

  @override
  String get profileUpdatedSuccessfully => 'Profil berhasil diperbarui!';

  @override
  String failedToUpdateProfile(String error) {
    return 'Gagal memperbarui profil: $error';
  }

  @override
  String get profilePictureUpdatedSuccessfully => 'Foto profil berhasil diperbarui!';

  @override
  String failedToUploadImage(String error) {
    return 'Gagal mengunggah gambar: $error';
  }

  @override
  String get profileEditingNotAvailableForGuests => 'Pengeditan profil tidak tersedia untuk pengguna tamu';

  @override
  String get profilePictureEditingNotAvailableForGuests => 'Pengeditan foto profil tidak tersedia untuk pengguna tamu';

  @override
  String get usernameCannotBeEmpty => 'Nama pengguna tidak boleh kosong';

  @override
  String get usernameMustBeBetween2And30Characters => 'Nama pengguna harus antara 2 dan 30 karakter';

  @override
  String get plan => 'Rencana';

  @override
  String get termsOfService => 'Syarat layanan';

  @override
  String get language => 'Bahasa';

  @override
  String get privacyPolicy => 'Kebijakan privasi';

  @override
  String get support => 'Dukungan';

  @override
  String get helpCenter => 'Pusat bantuan';

  @override
  String get contactUs => 'Hubungi kami';

  @override
  String get helpSupport => 'Help & Support';

  @override
  String get howCanWeHelpYou => 'How can we help you?';

  @override
  String get helpSupportGreeting => 'Hi! I\'m here to help you with TripwiseGO. You can ask me about app features, troubleshooting, or report any issues you\'re experiencing.';

  @override
  String get helpSupportWelcome => 'Welcome to TripwiseGO Support! Here are some things I can help you with:';

  @override
  String get helpSupportFeatures => '• App features and how to use them\n• Navigation and account help\n• Troubleshooting common issues\n• Reporting bugs or problems';

  @override
  String get helpSupportAskQuestion => 'Feel free to ask me anything or describe any issues you\'re having!';

  @override
  String get reportIssue => 'Report Issue';

  @override
  String get reportBug => 'Report Bug';

  @override
  String get reportProblem => 'Report Problem';

  @override
  String get issueCategory => 'Issue Category';

  @override
  String get bugReport => 'Bug Report';

  @override
  String get featureRequest => 'Feature Request';

  @override
  String get generalFeedback => 'General Feedback';

  @override
  String get accountIssue => 'Account Issue';

  @override
  String get technicalProblem => 'Technical Problem';

  @override
  String get yourName => 'Your Name';

  @override
  String get yourEmail => 'Your Email';

  @override
  String get issueDescription => 'Issue Description';

  @override
  String get describeIssueDetail => 'Please describe the issue in detail';

  @override
  String get optionalScreenshot => 'Screenshot (Optional)';

  @override
  String get submitReport => 'Submit Report';

  @override
  String get reportSubmitted => 'Report Submitted';

  @override
  String get reportSubmittedSuccess => 'Thank you! Your report has been submitted successfully. We\'ll look into it and get back to you if needed.';

  @override
  String get reportSubmissionFailed => 'Failed to submit report. Please try again later.';

  @override
  String get nameRequired => 'Name is required';

  @override
  String get emailRequired => 'Email is required';

  @override
  String get validEmailRequired => 'Please enter a valid email address';

  @override
  String get descriptionRequired => 'Issue description is required';

  @override
  String get selectCategory => 'Please select a category';

  @override
  String get subscription => 'Subscription';

  @override
  String get subscriptionActive => 'Subscription Active!';

  @override
  String get welcomeToPremium => 'Welcome to TripwiseGO Premium! Enjoy unlimited access to all features.';

  @override
  String get currentPlan => 'Current Plan';

  @override
  String get trial => 'Trial';

  @override
  String get active => 'Active';

  @override
  String trialEndsIn(int days) {
    return 'Trial ends in $days days';
  }

  @override
  String renewsIn(int days) {
    return 'Renews in $days days';
  }

  @override
  String get yourBenefits => 'Your Benefits';

  @override
  String get startPlanningTrip => 'Start Planning Your Trip';

  @override
  String get manageSubscription => 'Manage Subscription';

  @override
  String get basic => 'Basic';

  @override
  String get premium => 'Premium';

  @override
  String get basicPlaceRecommendations => 'Basic Place Recommendations';

  @override
  String get locationRecommendationSwiping => 'Location Recommendation Swiping (20 swipes/day)';

  @override
  String get aiPoweredTravelPlanner => 'AI-Powered Travel Planner';

  @override
  String get unlimitedQuizzes => 'Unlimited Quizzes & Fun Facts';

  @override
  String get noAds => 'No-Ads';

  @override
  String get oneMonth => '1 Month';

  @override
  String get threeMonths => '3 Months';

  @override
  String get fiveMonths => '5 Months';

  @override
  String dayFreeTrial(int days) {
    return '$days-day free trial';
  }

  @override
  String get billedMonthly => 'Billed monthly';

  @override
  String get billedTwiceAnnually => 'Billed twice annually';

  @override
  String get billedAnnually => 'Billed annually';

  @override
  String get save45Percent => 'SAVE 45%';

  @override
  String get continueButton => 'Continue';

  @override
  String get termsAndConditions => 'Terms and Conditions';

  @override
  String get failedToSubscribe => 'Failed to subscribe. Please try again.';

  @override
  String get signOut => 'Keluar';

  @override
  String get selectLanguage => 'Pilih bahasa';

  @override
  String get chooseYourPreferredLanguage => 'Pilih bahasa yang Anda sukai';

  @override
  String get languageUpdatedSuccessfully => 'Bahasa berhasil diperbarui!';

  @override
  String get home => 'Beranda';

  @override
  String get match => 'Cocok';

  @override
  String get chat => 'Obrolan';

  @override
  String get profile => 'Profil';

  @override
  String get loading => 'Memuat...';

  @override
  String get error => 'Kesalahan';

  @override
  String get retry => 'Coba lagi';

  @override
  String get ok => 'OK';

  @override
  String get yes => 'Ya';

  @override
  String get no => 'Tidak';

  @override
  String get save => 'Simpan';

  @override
  String get delete => 'Hapus';

  @override
  String get edit => 'Edit';

  @override
  String get add => 'Tambah';

  @override
  String get remove => 'Hapus';

  @override
  String get close => 'Tutup';

  @override
  String get back => 'Kembali';

  @override
  String get next => 'Berikutnya';

  @override
  String get previous => 'Sebelumnya';

  @override
  String get done => 'Selesai';

  @override
  String get search => 'Cari';

  @override
  String get noResultsFound => 'Tidak ada hasil ditemukan';

  @override
  String get tryAgain => 'Coba lagi';

  @override
  String get somethingWentWrong => 'Terjadi kesalahan';

  @override
  String get networkError => 'Kesalahan jaringan. Periksa koneksi Anda.';

  @override
  String get serverError => 'Kesalahan server. Coba lagi nanti.';

  @override
  String get invalidInput => 'Input tidak valid';

  @override
  String get required => 'Wajib';

  @override
  String get optional => 'Opsional';

  @override
  String get itinerary => 'Itinerary';

  @override
  String get yourItineraries => 'Your Itineraries';

  @override
  String get startPlanningYourTrip => 'Start Planning Your Trip';

  @override
  String get tripPlanningFeaturesWillAppearHere => 'Your trip planning features will appear here. The persistent authentication is now working!';

  @override
  String get forYou => 'For You';

  @override
  String get liked => 'Liked';

  @override
  String get collab => 'Collab';

  @override
  String get collaborativeItinerary => 'Collaborative';

  @override
  String get endSession => 'End Session';

  @override
  String get logout => 'Logout';

  @override
  String logoutFailed(String error) {
    return 'Logout failed: $error';
  }

  @override
  String get noItineraryFound => 'Tidak Ada Itinerary Ditemukan';

  @override
  String get askAiToCreateTravelPlan => 'Minta AI kami untuk membuat rencana perjalanan untuk Anda!';

  @override
  String get saturday => 'Sabtu';

  @override
  String get tuesday => 'Selasa';

  @override
  String dayNumber(int number) {
    return 'Hari $number';
  }

  @override
  String get itineraryOverview => 'Ringkasan Itinerary';

  @override
  String daysAndNights(int days, int nights) {
    return '${days}h ${nights}m';
  }

  @override
  String get hiImWanderlyAi => 'Hai, saya Wanderly AI 🌏';

  @override
  String get yourTravelAiAssistant => 'Asisten AI perjalanan Anda, bagaimana saya bisa membantu Anda hari ini?';

  @override
  String get useThisBubbleChat => 'Gunakan chat gelembung ini';

  @override
  String get aiAssistant => 'Asisten AI';

  @override
  String get chatHistory => 'Riwayat Chat';

  @override
  String get newChat => 'Chat Baru';

  @override
  String get addImage => 'Tambah Gambar';

  @override
  String get camera => 'Kamera';

  @override
  String get gallery => 'Galeri';

  @override
  String get microphonePermissionRequired => 'Izin mikrofon diperlukan untuk input suara';

  @override
  String get speechRecognitionNotAvailable => 'Pengenalan suara tidak tersedia di perangkat ini';

  @override
  String get listening => 'Mendengarkan...';

  @override
  String get deleteChat => 'Hapus Chat';

  @override
  String get deleteChatConfirmation => 'Apakah Anda yakin ingin menghapus chat ini? Tindakan ini tidak dapat dibatalkan.';

  @override
  String get chatDeletedSuccessfully => 'Chat berhasil dihapus';

  @override
  String get pleaseEnterSearchQuery => 'Silakan masukkan kueri pencarian';

  @override
  String get dailySearchLimitReached => 'Batas pencarian harian tercapai. Anda dapat melakukan 5 pencarian per hari.';

  @override
  String get searchingTheWeb => 'Mencari di Web...';

  @override
  String get webSearchModeActive => 'Mode Pencarian Web Aktif';

  @override
  String get pleaseWaitWhileSearching => 'Harap tunggu sementara saya mencari informasi';

  @override
  String get yourNextMessageWillSearch => 'Pesan berikutnya akan mencari di web';

  @override
  String get disableWebSearch => 'Nonaktifkan Pencarian Web';

  @override
  String get enableWebSearch => 'Aktifkan Pencarian Web';

  @override
  String get switchBackToAiChatMode => 'Kembali ke mode chat AI';

  @override
  String get searchWebForCurrentInfo => 'Cari informasi terkini di web';

  @override
  String get pickImageFromGallery => 'Pilih Gambar dari Galeri';

  @override
  String get uploadImageForAiAnalysis => 'Unggah gambar untuk analisis AI';

  @override
  String get yourMessage => 'Pesan Anda';

  @override
  String get wanderlyAi => 'Wanderly AI';

  @override
  String get webSearch => 'Pencarian Web:';

  @override
  String get like => 'Suka';

  @override
  String get dislike => 'Tidak Suka';

  @override
  String get copy => 'Salin';

  @override
  String get regenerate => 'Regenerasi';

  @override
  String get failedToSubmitFeedback => 'Gagal mengirim umpan balik. Silakan coba lagi.';

  @override
  String get thankYouForFeedback => 'Terima kasih atas umpan balik Anda! 🙏';

  @override
  String get feedbackReceivedThanks => 'Umpan balik diterima. Terima kasih telah membantu kami berkembang! 🚀';

  @override
  String get responseCopiedToClipboard => 'Respons disalin ke clipboard';

  @override
  String get wanderlyAiIsTyping => 'Wanderly AI sedang mengetik';

  @override
  String get stopGeneration => 'Hentikan Generasi';

  @override
  String get youHaveChatsLeft => 'Anda memiliki 10 chat tersisa';

  @override
  String get enterSearchQuery => 'Masukkan kueri pencarian Anda...';

  @override
  String get askMeAnythingOrLongPress => 'Tanya apa saja atau tekan lama untuk berbicara...';

  @override
  String failedToPickImage(String error) {
    return 'Gagal memilih gambar: $error';
  }

  @override
  String get failedToAnalyzeImage => 'Gagal menganalisis gambar. Silakan coba lagi.';

  @override
  String get responseGenerationStopped => 'Generasi respons dihentikan.';

  @override
  String get unknownDestination => 'Destinasi Tidak Dikenal';

  @override
  String get congratulations => 'Selamat';

  @override
  String get itsAMatch => 'Ini adalah kecocokan';

  @override
  String get travelVibesAligned => 'Vibe perjalanan selaras\nkemas tas Anda, Anda mendapat kecocokan!';

  @override
  String get matchedPreferences => 'Preferensi yang Cocok:';

  @override
  String get addToItinerary => 'Tambah ke Itinerary';

  @override
  String get keepSwiping => 'Terus menggeser';

  @override
  String get versionInfo => 'Version Info';

  @override
  String get appVersion => 'App Version';

  @override
  String get buildNumber => 'Build Number';

  @override
  String get packageName => 'Package Name';

  @override
  String get appName => 'App Name';

  @override
  String get buildSignature => 'Build Signature';

  @override
  String get installerStore => 'Installer Store';

  @override
  String get deviceInfo => 'Device Information';

  @override
  String get operatingSystem => 'Operating System';

  @override
  String get flutterVersion => 'Flutter Version';

  @override
  String get dartVersion => 'Dart Version';

  @override
  String get leaveAReview => 'Leave a Review';

  @override
  String get rateOurApp => 'Rate Our App';

  @override
  String get enjoyingTripWiseGo => 'Enjoying TripWiseGo?';

  @override
  String get helpUsImproveByLeavingReview => 'Help us improve by leaving a review on the app store. Your feedback means a lot to us!';

  @override
  String get rateNow => 'Rate Now';

  @override
  String get maybeLater => 'Maybe Later';

  @override
  String get reviewFeatureNotAvailable => 'Review feature is only available in production builds';

  @override
  String get unableToOpenStore => 'Unable to open app store. Please try again later.';

  @override
  String get thankYouForReview => 'Thank you for taking the time to review our app!';
}
