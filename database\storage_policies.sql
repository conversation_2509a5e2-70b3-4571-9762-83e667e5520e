-- Storage Policies for Collaborative Itinerary Images and Profile Avatars
-- Run this in your Supabase SQL Editor

-- Create storage bucket for collaborative itinerary images (if not created via dashboard)
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'collaborative-itinerary-images',
  'collaborative-itinerary-images',
  true,
  10485760, -- 10MB
  ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']
) ON CONFLICT (id) DO NOTHING;

-- Create storage bucket for profile avatars (if not created via dashboard)
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'avatars',
  'avatars',
  true,
  5242880, -- 5MB
  ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']
) ON CONFLICT (id) DO NOTHING;

-- Enable RLS on storage.objects
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Policy: Users can view images from collaborative itineraries they have access to
CREATE POLICY "Users can view collaborative itinerary images" ON storage.objects
FOR SELECT USING (
  bucket_id = 'collaborative-itinerary-images' AND
  (
    -- Public images (for public itineraries)
    auth.uid() IS NOT NULL OR
    -- Images from itineraries the user owns or participates in
    (SELECT COUNT(*) FROM collaborative_itineraries ci
     LEFT JOIN collaboration_participants cp ON ci.id = cp.itinerary_id
     WHERE (ci.owner_id = auth.uid() OR cp.user_id = auth.uid())
     AND name LIKE ci.id::text || '/%') > 0
  )
);

-- Policy: Authenticated users can upload images
CREATE POLICY "Authenticated users can upload images" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'collaborative-itinerary-images' AND
  auth.uid() IS NOT NULL AND
  -- Ensure the path starts with a valid itinerary ID that the user has access to
  (SELECT COUNT(*) FROM collaborative_itineraries ci
   LEFT JOIN collaboration_participants cp ON ci.id = cp.itinerary_id
   WHERE (ci.owner_id = auth.uid() OR cp.user_id = auth.uid())
   AND name LIKE ci.id::text || '/%') > 0
);

-- Policy: Users can update images from itineraries they have access to
CREATE POLICY "Users can update collaborative itinerary images" ON storage.objects
FOR UPDATE USING (
  bucket_id = 'collaborative-itinerary-images' AND
  auth.uid() IS NOT NULL AND
  (SELECT COUNT(*) FROM collaborative_itineraries ci
   LEFT JOIN collaboration_participants cp ON ci.id = cp.itinerary_id
   WHERE (ci.owner_id = auth.uid() OR cp.user_id = auth.uid())
   AND name LIKE ci.id::text || '/%') > 0
);

-- Policy: Users can delete images from itineraries they have access to
CREATE POLICY "Users can delete collaborative itinerary images" ON storage.objects
FOR DELETE USING (
  bucket_id = 'collaborative-itinerary-images' AND
  auth.uid() IS NOT NULL AND
  (SELECT COUNT(*) FROM collaborative_itineraries ci
   LEFT JOIN collaboration_participants cp ON ci.id = cp.itinerary_id
   WHERE (ci.owner_id = auth.uid() OR cp.user_id = auth.uid())
   AND name LIKE ci.id::text || '/%') > 0
);

-- ========================================
-- AVATAR STORAGE POLICIES
-- ========================================

-- Policy: Users can view their own avatars and public avatars
CREATE POLICY "Users can view avatars" ON storage.objects
FOR SELECT USING (
  bucket_id = 'avatars' AND
  (
    -- Users can view their own avatars
    name LIKE 'profiles/' || auth.uid()::text || '_%' OR
    -- All authenticated users can view any avatar (for profile display)
    auth.uid() IS NOT NULL
  )
);

-- Policy: Authenticated users can upload their own avatars
CREATE POLICY "Users can upload their own avatars" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'avatars' AND
  auth.uid() IS NOT NULL AND
  -- Ensure the path starts with profiles/{user_id}_
  name LIKE 'profiles/' || auth.uid()::text || '_%'
);

-- Policy: Users can update their own avatars
CREATE POLICY "Users can update their own avatars" ON storage.objects
FOR UPDATE USING (
  bucket_id = 'avatars' AND
  auth.uid() IS NOT NULL AND
  name LIKE 'profiles/' || auth.uid()::text || '_%'
) WITH CHECK (
  bucket_id = 'avatars' AND
  auth.uid() IS NOT NULL AND
  name LIKE 'profiles/' || auth.uid()::text || '_%'
);

-- Policy: Users can delete their own avatars
CREATE POLICY "Users can delete their own avatars" ON storage.objects
FOR DELETE USING (
  bucket_id = 'avatars' AND
  auth.uid() IS NOT NULL AND
  name LIKE 'profiles/' || auth.uid()::text || '_%'
);
