/// Generated file. Do not edit.
///
/// This file contains the localized strings for the TripWiseGo app.
/// To add a new localized string, add it to the appropriate .arb file
/// in the lib/l10n directory and run `flutter gen-l10n`.


import 'app_localizations.dart';

/// The translations for Chinese (`zh`).
class AppLocalizationsZh extends AppLocalizations {
  AppLocalizationsZh([String locale = 'zh']) : super(locale);

  @override
  String get appTitle => 'TripWiseGo';

  @override
  String get welcome => '欢迎';

  @override
  String get guestUser => '访客用户';

  @override
  String get readyForAdventure => '准备好您的下一次冒险';

  @override
  String get exploringAsGuest => '以访客身份探索世界';

  @override
  String get editProfile => '编辑个人资料';

  @override
  String get saveChanges => '保存更改';

  @override
  String get cancel => '取消';

  @override
  String get username => '用户名';

  @override
  String get email => '电子邮件';

  @override
  String get profileUpdatedSuccessfully => '个人资料更新成功！';

  @override
  String failedToUpdateProfile(String error) {
    return '更新个人资料失败：$error';
  }

  @override
  String get profilePictureUpdatedSuccessfully => '头像更新成功！';

  @override
  String failedToUploadImage(String error) {
    return '上传图片失败：$error';
  }

  @override
  String get profileEditingNotAvailableForGuests => '访客用户无法编辑个人资料';

  @override
  String get profilePictureEditingNotAvailableForGuests => '访客用户无法编辑头像';

  @override
  String get usernameCannotBeEmpty => '用户名不能为空';

  @override
  String get usernameMustBeBetween2And30Characters => '用户名必须在2到30个字符之间';

  @override
  String get plan => '计划';

  @override
  String get termsOfService => '服务条款';

  @override
  String get language => '语言';

  @override
  String get privacyPolicy => '隐私政策';

  @override
  String get support => '支持';

  @override
  String get helpCenter => '帮助中心';

  @override
  String get contactUs => '联系我们';

  @override
  String get helpSupport => '帮助与支持';

  @override
  String get howCanWeHelpYou => '我们如何为您提供帮助？';

  @override
  String get helpSupportGreeting => '您好！我在这里帮助您使用TripwiseGO。您可以询问应用功能、故障排除或报告您遇到的任何问题。';

  @override
  String get helpSupportWelcome => '欢迎来到TripwiseGO支持！以下是我可以帮助您的一些事项：';

  @override
  String get helpSupportFeatures => '• 应用功能及使用方法\n• 导航和账户帮助\n• 常见问题故障排除\n• 报告错误或问题';

  @override
  String get helpSupportAskQuestion => '请随时向我提问或描述您遇到的任何问题！';

  @override
  String get reportIssue => '报告问题';

  @override
  String get reportBug => '报告错误';

  @override
  String get reportProblem => '报告问题';

  @override
  String get issueCategory => '问题类别';

  @override
  String get bugReport => '错误报告';

  @override
  String get featureRequest => '功能请求';

  @override
  String get generalFeedback => '一般反馈';

  @override
  String get accountIssue => '账户问题';

  @override
  String get technicalProblem => '技术问题';

  @override
  String get yourName => '您的姓名';

  @override
  String get yourEmail => '您的邮箱';

  @override
  String get issueDescription => '问题描述';

  @override
  String get describeIssueDetail => '请详细描述问题';

  @override
  String get optionalScreenshot => '截图（可选）';

  @override
  String get submitReport => '提交报告';

  @override
  String get reportSubmitted => '报告已提交';

  @override
  String get reportSubmittedSuccess => '谢谢！您的报告已成功提交。我们将查看并在需要时与您联系。';

  @override
  String get reportSubmissionFailed => '提交报告失败。请稍后重试。';

  @override
  String get nameRequired => '姓名为必填项';

  @override
  String get emailRequired => '邮箱为必填项';

  @override
  String get validEmailRequired => '请输入有效的邮箱地址';

  @override
  String get descriptionRequired => '问题描述为必填项';

  @override
  String get selectCategory => '请选择一个类别';

  @override
  String get subscription => '订阅';

  @override
  String get subscriptionActive => '订阅已激活！';

  @override
  String get welcomeToPremium => '欢迎使用TripwiseGO高级版！享受所有功能的无限访问。';

  @override
  String get currentPlan => '当前计划';

  @override
  String get trial => '试用';

  @override
  String get active => '活跃';

  @override
  String trialEndsIn(int days) {
    return '试用期将在$days天后结束';
  }

  @override
  String renewsIn(int days) {
    return '$days天后续订';
  }

  @override
  String get yourBenefits => '您的权益';

  @override
  String get startPlanningTrip => '开始规划您的旅行';

  @override
  String get manageSubscription => '管理订阅';

  @override
  String get basic => '基础版';

  @override
  String get premium => '高级版';

  @override
  String get basicPlaceRecommendations => '基础地点推荐';

  @override
  String get locationRecommendationSwiping => '位置推荐滑动（每天20次滑动）';

  @override
  String get aiPoweredTravelPlanner => 'AI驱动的旅行规划师';

  @override
  String get unlimitedQuizzes => '无限问答和趣味知识';

  @override
  String get noAds => '无广告';

  @override
  String get oneMonth => '1个月';

  @override
  String get threeMonths => '3个月';

  @override
  String get fiveMonths => '5个月';

  @override
  String dayFreeTrial(int days) {
    return '$days天免费试用';
  }

  @override
  String get billedMonthly => '按月计费';

  @override
  String get billedTwiceAnnually => '每年计费两次';

  @override
  String get billedAnnually => '按年计费';

  @override
  String get save45Percent => '节省45%';

  @override
  String get continueButton => '继续';

  @override
  String get termsAndConditions => '条款和条件';

  @override
  String get failedToSubscribe => '订阅失败。请重试。';

  @override
  String get signOut => '退出登录';

  @override
  String get selectLanguage => '选择语言';

  @override
  String get chooseYourPreferredLanguage => '选择您的首选语言';

  @override
  String get languageUpdatedSuccessfully => '语言更新成功！';

  @override
  String get home => '首页';

  @override
  String get match => '匹配';

  @override
  String get chat => '聊天';

  @override
  String get profile => '个人资料';

  @override
  String get loading => '加载中...';

  @override
  String get error => '错误';

  @override
  String get retry => '重试';

  @override
  String get ok => '确定';

  @override
  String get yes => '是';

  @override
  String get no => '否';

  @override
  String get save => '保存';

  @override
  String get delete => '删除';

  @override
  String get edit => '编辑';

  @override
  String get add => '添加';

  @override
  String get remove => '移除';

  @override
  String get close => '关闭';

  @override
  String get back => '返回';

  @override
  String get next => '下一步';

  @override
  String get previous => '上一步';

  @override
  String get done => '完成';

  @override
  String get search => '搜索';

  @override
  String get noResultsFound => '未找到结果';

  @override
  String get tryAgain => '重试';

  @override
  String get somethingWentWrong => '出现了问题';

  @override
  String get networkError => '网络错误。请检查您的连接。';

  @override
  String get serverError => '服务器错误。请稍后重试。';

  @override
  String get invalidInput => '无效输入';

  @override
  String get required => '必填';

  @override
  String get optional => '可选';

  @override
  String get itinerary => 'Itinerary';

  @override
  String get yourItineraries => 'Your Itineraries';

  @override
  String get startPlanningYourTrip => 'Start Planning Your Trip';

  @override
  String get tripPlanningFeaturesWillAppearHere => 'Your trip planning features will appear here. The persistent authentication is now working!';

  @override
  String get forYou => 'For You';

  @override
  String get liked => 'Liked';

  @override
  String get collab => 'Collab';

  @override
  String get collaborativeItinerary => '协作行程';

  @override
  String get endSession => 'End Session';

  @override
  String get logout => 'Logout';

  @override
  String logoutFailed(String error) {
    return 'Logout failed: $error';
  }

  @override
  String get noItineraryFound => '未找到行程';

  @override
  String get askAiToCreateTravelPlan => '请让我们的AI为您创建旅行计划！';

  @override
  String get saturday => '星期六';

  @override
  String get tuesday => '星期二';

  @override
  String dayNumber(int number) {
    return '第$number天';
  }

  @override
  String get itineraryOverview => '行程概览';

  @override
  String daysAndNights(int days, int nights) {
    return '$days天$nights夜';
  }

  @override
  String get hiImWanderlyAi => '您好，我是Wanderly AI 🌏';

  @override
  String get yourTravelAiAssistant => '您的旅行AI助手，今天我能为您做些什么？';

  @override
  String get useThisBubbleChat => '使用此气泡聊天';

  @override
  String get aiAssistant => 'AI助手';

  @override
  String get chatHistory => '聊天历史';

  @override
  String get newChat => '新聊天';

  @override
  String get addImage => '添加图片';

  @override
  String get camera => '相机';

  @override
  String get gallery => '相册';

  @override
  String get microphonePermissionRequired => '语音输入需要麦克风权限';

  @override
  String get speechRecognitionNotAvailable => '此设备不支持语音识别';

  @override
  String get listening => '正在聆听...';

  @override
  String get deleteChat => '删除聊天';

  @override
  String get deleteChatConfirmation => '您确定要删除此聊天吗？此操作无法撤销。';

  @override
  String get chatDeletedSuccessfully => '聊天删除成功';

  @override
  String get pleaseEnterSearchQuery => '请输入搜索查询';

  @override
  String get dailySearchLimitReached => '已达到每日搜索限制。您每天可以进行5次搜索。';

  @override
  String get searchingTheWeb => '正在搜索网络...';

  @override
  String get webSearchModeActive => '网络搜索模式已激活';

  @override
  String get pleaseWaitWhileSearching => '请稍候，我正在搜索信息';

  @override
  String get yourNextMessageWillSearch => '您的下一条消息将搜索网络';

  @override
  String get disableWebSearch => '禁用网络搜索';

  @override
  String get enableWebSearch => '启用网络搜索';

  @override
  String get switchBackToAiChatMode => '切换回AI聊天模式';

  @override
  String get searchWebForCurrentInfo => '搜索网络获取当前信息';

  @override
  String get pickImageFromGallery => '从相册选择图片';

  @override
  String get uploadImageForAiAnalysis => '上传图片进行AI分析';

  @override
  String get yourMessage => '您的消息';

  @override
  String get wanderlyAi => 'Wanderly AI';

  @override
  String get webSearch => '网络搜索：';

  @override
  String get like => '喜欢';

  @override
  String get dislike => '不喜欢';

  @override
  String get copy => '复制';

  @override
  String get regenerate => '重新生成';

  @override
  String get failedToSubmitFeedback => '提交反馈失败。请重试。';

  @override
  String get thankYouForFeedback => '感谢您的反馈！🙏';

  @override
  String get feedbackReceivedThanks => '已收到反馈。感谢您帮助我们改进！🚀';

  @override
  String get responseCopiedToClipboard => '回复已复制到剪贴板';

  @override
  String get wanderlyAiIsTyping => 'Wanderly AI正在输入';

  @override
  String get stopGeneration => '停止生成';

  @override
  String get youHaveChatsLeft => '您还有10次聊天';

  @override
  String get enterSearchQuery => '输入您的搜索查询...';

  @override
  String get askMeAnythingOrLongPress => '问我任何问题或长按说话...';

  @override
  String failedToPickImage(String error) {
    return '选择图片失败：$error';
  }

  @override
  String get failedToAnalyzeImage => '分析图片失败。请重试。';

  @override
  String get responseGenerationStopped => '回复生成已停止。';

  @override
  String get unknownDestination => '未知目的地';

  @override
  String get congratulations => '恭喜';

  @override
  String get itsAMatch => '匹配成功';

  @override
  String get travelVibesAligned => '旅行氛围一致\n收拾行李，你找到了匹配！';

  @override
  String get matchedPreferences => '匹配的偏好：';

  @override
  String get addToItinerary => '添加到行程';

  @override
  String get keepSwiping => '继续滑动';

  @override
  String get versionInfo => '版本信息';

  @override
  String get appVersion => '应用版本';

  @override
  String get buildNumber => '构建号';

  @override
  String get packageName => '包名';

  @override
  String get appName => '应用名称';

  @override
  String get buildSignature => '构建签名';

  @override
  String get installerStore => '安装商店';

  @override
  String get deviceInfo => '设备信息';

  @override
  String get operatingSystem => '操作系统';

  @override
  String get flutterVersion => 'Flutter版本';

  @override
  String get dartVersion => 'Dart版本';

  @override
  String get leaveAReview => '留下评价';

  @override
  String get rateOurApp => '为我们的应用评分';

  @override
  String get enjoyingTripWiseGo => '喜欢TripWiseGo吗？';

  @override
  String get helpUsImproveByLeavingReview => '请在应用商店留下评价帮助我们改进。您的反馈对我们很重要！';

  @override
  String get rateNow => '立即评分';

  @override
  String get maybeLater => '稍后再说';

  @override
  String get reviewFeatureNotAvailable => '评价功能仅在正式版本中可用';

  @override
  String get unableToOpenStore => '无法打开应用商店。请稍后再试。';

  @override
  String get thankYouForReview => '感谢您花时间评价我们的应用！';
}
