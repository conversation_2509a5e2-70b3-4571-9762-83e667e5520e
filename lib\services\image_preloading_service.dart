import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import '../utils/network_helper.dart';

/// Service for preloading and managing image cache for optimal performance
/// Specifically optimized for match card swiping experience
class ImagePreloadingService {
  static final ImagePreloadingService _instance =
      ImagePreloadingService._internal();
  factory ImagePreloadingService() => _instance;
  ImagePreloadingService._internal();

  // Cache management
  static const int _maxCacheSize = 100; // Maximum number of cached images
  static const int _preloadCount = 5; // Number of images to preload ahead
  static const Duration _cacheTimeout = Duration(hours: 24);

  // Connectivity tracking
  List<ConnectivityResult> _currentConnectivity = [ConnectivityResult.none];
  final Connectivity _connectivity = Connectivity();

  // Preloading state
  final Set<String> _preloadingUrls = <String>{};
  final Set<String> _preloadedUrls = <String>{};
  final Map<String, DateTime> _preloadTimestamps = <String, DateTime>{};

  /// Initialize the service and start connectivity monitoring
  Future<void> initialize() async {
    _currentConnectivity = await _connectivity.checkConnectivity();
    _connectivity.onConnectivityChanged
        .listen((List<ConnectivityResult> result) {
      _currentConnectivity = result;
      if (kDebugMode) {
        print('ImagePreloadingService: Connectivity changed to $result');
      }
    });

    // Clean up old cache entries
    _cleanupOldCache();
  }

  /// Get optimized image URL based on current connectivity
  String getOptimizedImageUrl(String originalUrl) {
    if (originalUrl.isEmpty) return originalUrl;

    // For Unsplash URLs, add appropriate quality parameters
    if (originalUrl.contains('unsplash.com')) {
      if (_currentConnectivity.contains(ConnectivityResult.mobile)) {
        // Lower quality for mobile data
        return originalUrl.contains('?')
            ? '$originalUrl&q=60&w=600&h=800&fit=crop'
            : '$originalUrl?q=60&w=600&h=800&fit=crop';
      } else if (_currentConnectivity.contains(ConnectivityResult.wifi) ||
          _currentConnectivity.contains(ConnectivityResult.ethernet)) {
        // Higher quality for WiFi/Ethernet
        return originalUrl.contains('?')
            ? '$originalUrl&q=80&w=800&h=1000&fit=crop'
            : '$originalUrl?q=80&w=800&h=1000&fit=crop';
      } else {
        // Default quality for unknown connections
        return originalUrl.contains('?')
            ? '$originalUrl&q=70&w=700&h=900&fit=crop'
            : '$originalUrl?q=70&w=700&h=900&fit=crop';
      }
    }

    return originalUrl;
  }

  /// Preload images for match cards to ensure smooth swiping
  Future<void> preloadMatchImages(
    BuildContext context,
    List<Map<String, dynamic>> destinations,
    int currentIndex,
  ) async {
    if (!await NetworkHelper.hasInternetConnection()) {
      if (kDebugMode) {
        print(
            'ImagePreloadingService: No internet connection, skipping preload');
      }
      return;
    }

    // Calculate which images to preload
    final startIndex = currentIndex;
    final endIndex =
        (currentIndex + _preloadCount).clamp(0, destinations.length);

    final imagesToPreload = <String>[];
    for (int i = startIndex; i < endIndex; i++) {
      final imageUrl = destinations[i]['image'] as String?;
      if (imageUrl != null && imageUrl.isNotEmpty) {
        final optimizedUrl = getOptimizedImageUrl(imageUrl);
        if (!_preloadedUrls.contains(optimizedUrl) &&
            !_preloadingUrls.contains(optimizedUrl)) {
          imagesToPreload.add(optimizedUrl);
        }
      }
    }

    if (imagesToPreload.isEmpty) return;

    if (kDebugMode) {
      print(
          'ImagePreloadingService: Preloading ${imagesToPreload.length} images');
    }

    // Preload images concurrently but limit concurrent operations
    final futures =
        imagesToPreload.map((url) => _preloadSingleImage(context, url));
    await Future.wait(futures, eagerError: false);
  }

  /// Preload a single image
  Future<void> _preloadSingleImage(
      BuildContext context, String imageUrl) async {
    if (_preloadingUrls.contains(imageUrl) ||
        _preloadedUrls.contains(imageUrl)) {
      return;
    }

    _preloadingUrls.add(imageUrl);

    try {
      // Use precacheImage for efficient preloading
      await precacheImage(
        CachedNetworkImageProvider(imageUrl),
        context,
        size: const Size(800, 1000), // Target size for match cards
      );

      _preloadedUrls.add(imageUrl);
      _preloadTimestamps[imageUrl] = DateTime.now();

      if (kDebugMode) {
        print('ImagePreloadingService: Successfully preloaded $imageUrl');
      }
    } catch (error) {
      if (kDebugMode) {
        print('ImagePreloadingService: Failed to preload $imageUrl - $error');
      }
    } finally {
      _preloadingUrls.remove(imageUrl);
    }
  }

  /// Preload progressive images (low-res first, then high-res)
  Future<void> preloadProgressiveImage(
    BuildContext context,
    String imageUrl, {
    String? lowResUrl,
  }) async {
    if (!await NetworkHelper.hasInternetConnection()) return;

    try {
      // First preload low-res version if available
      if (lowResUrl != null && lowResUrl.isNotEmpty) {
        await _preloadSingleImage(context, lowResUrl);
      }

      // Then preload high-res version
      final optimizedUrl = getOptimizedImageUrl(imageUrl);
      await _preloadSingleImage(context, optimizedUrl);
    } catch (error) {
      if (kDebugMode) {
        print('ImagePreloadingService: Progressive preload failed - $error');
      }
    }
  }

  /// Check if an image is already preloaded
  bool isImagePreloaded(String imageUrl) {
    final optimizedUrl = getOptimizedImageUrl(imageUrl);
    return _preloadedUrls.contains(optimizedUrl);
  }

  /// Get cache statistics
  Map<String, dynamic> getCacheStats() {
    return {
      'preloaded_count': _preloadedUrls.length,
      'preloading_count': _preloadingUrls.length,
      'connectivity': _currentConnectivity.toString(),
      'cache_size': _preloadTimestamps.length,
    };
  }

  /// Clean up old cache entries
  void _cleanupOldCache() {
    final now = DateTime.now();
    final expiredUrls = <String>[];

    _preloadTimestamps.forEach((url, timestamp) {
      if (now.difference(timestamp) > _cacheTimeout) {
        expiredUrls.add(url);
      }
    });

    for (final url in expiredUrls) {
      _preloadedUrls.remove(url);
      _preloadTimestamps.remove(url);
    }

    // Also limit cache size
    if (_preloadedUrls.length > _maxCacheSize) {
      final sortedEntries = _preloadTimestamps.entries.toList()
        ..sort((a, b) => a.value.compareTo(b.value));

      final urlsToRemove = sortedEntries
          .take(_preloadedUrls.length - _maxCacheSize)
          .map((e) => e.key)
          .toList();

      for (final url in urlsToRemove) {
        _preloadedUrls.remove(url);
        _preloadTimestamps.remove(url);
      }
    }

    if (kDebugMode && expiredUrls.isNotEmpty) {
      print(
          'ImagePreloadingService: Cleaned up ${expiredUrls.length} expired cache entries');
    }
  }

  /// Clear all preloaded images
  void clearCache() {
    _preloadedUrls.clear();
    _preloadTimestamps.clear();
    _preloadingUrls.clear();

    if (kDebugMode) {
      print('ImagePreloadingService: Cache cleared');
    }
  }

  /// Dispose of the service
  void dispose() {
    clearCache();
  }
}
