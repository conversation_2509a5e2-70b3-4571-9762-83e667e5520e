import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'chat_history_database.dart';

class ChatHistoryMigration {
  static const String _migrationCompleteKey = 'chat_history_migration_complete';
  static bool _migrationChecked = false;

  /// Check if migration from Hive to SQLite is needed and perform it
  static Future<void> checkAndMigrate() async {
    if (_migrationChecked) return;
    _migrationChecked = true;

    try {
      final prefs = await SharedPreferences.getInstance();
      final migrationComplete = prefs.getBool(_migrationCompleteKey) ?? false;

      if (migrationComplete) {
        if (kDebugMode) {
          print('Chat Migration: Migration already completed, skipping');
        }
        return;
      }

      if (kDebugMode) {
        print('Chat Migration: Checking for existing Hive data...');
      }

      // Check if there's any existing data to migrate
      final hasExistingData = await _hasExistingHiveData();

      if (!hasExistingData) {
        // No existing data, mark migration as complete
        await prefs.setBool(_migrationCompleteKey, true);
        if (kDebugMode) {
          print(
              'Chat Migration: No existing Hive data found, marking migration complete');
        }
        return;
      }

      if (kDebugMode) {
        print(
            'Chat Migration: Existing Hive data found, starting migration...');
      }

      // Perform the migration
      await _migrateHiveToSQLite();

      // Mark migration as complete
      await prefs.setBool(_migrationCompleteKey, true);

      if (kDebugMode) {
        print('Chat Migration: Migration completed successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Chat Migration: Migration failed: $e');
        print('Chat Migration: Continuing with fresh SQLite database');
      }

      // Mark migration as complete even if it failed to prevent repeated attempts
      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool(_migrationCompleteKey, true);
      } catch (prefsError) {
        if (kDebugMode) {
          print(
              'Chat Migration: Failed to mark migration complete: $prefsError');
        }
      }
    }
  }

  /// Check if there's existing Hive data that needs migration
  static Future<bool> _hasExistingHiveData() async {
    try {
      // Try to check if Hive data exists without importing Hive
      // This is a simple check - in a real scenario, you might want to
      // check for the existence of Hive database files
      final prefs = await SharedPreferences.getInstance();
      final activeSessionId = prefs.getString('active_session_id');

      // If there's an active session ID but no SQLite data, assume Hive data exists
      if (activeSessionId != null) {
        final stats = await ChatHistoryDatabase.getStatistics();
        return stats['sessions'] == 0; // No SQLite data but has active session
      }

      return false;
    } catch (e) {
      if (kDebugMode) {
        print('Chat Migration: Error checking for existing Hive data: $e');
      }
      return false;
    }
  }

  /// Migrate data from Hive to SQLite
  static Future<void> _migrateHiveToSQLite() async {
    try {
      if (kDebugMode) {
        print('Chat Migration: Starting Hive to SQLite migration...');
      }

      // Since we can't import Hive anymore, we'll create a fresh start
      // In a real migration scenario, you would:
      // 1. Import the old Hive data
      // 2. Convert it to the new SQLite format
      // 3. Insert it into the SQLite database

      // For now, we'll just clear any stale references and start fresh
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('active_session_id');

      if (kDebugMode) {
        print('Chat Migration: Cleared legacy preferences');
        print(
            'Chat Migration: Migration completed - starting with fresh SQLite database');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Chat Migration: Error during migration: $e');
      }
      rethrow;
    }
  }

  /// Reset migration status (for testing purposes)
  static Future<void> resetMigrationStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_migrationCompleteKey);
      _migrationChecked = false;

      if (kDebugMode) {
        print('Chat Migration: Migration status reset');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Chat Migration: Failed to reset migration status: $e');
      }
    }
  }

  /// Get migration status
  static Future<bool> isMigrationComplete() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_migrationCompleteKey) ?? false;
    } catch (e) {
      if (kDebugMode) {
        print('Chat Migration: Failed to get migration status: $e');
      }
      return false;
    }
  }
}
