import 'dart:async';
import 'dart:convert';
import 'dart:isolate';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/learning_models.dart';

/// Service for managing AI learning configuration and background processing
class LearningConfigurationService {
  static const String _configKey = 'learning_configuration';
  static const String _privacySettingsKey = 'learning_privacy_settings';
  static const String _performanceSettingsKey = 'learning_performance_settings';
  static const String _backgroundTaskKey = 'learning_background_task';

  // Singleton pattern
  static LearningConfigurationService? _instance;
  static LearningConfigurationService get instance =>
      _instance ??= LearningConfigurationService._();
  LearningConfigurationService._();

  // Internal state
  bool _isInitialized = false;
  LearningConfiguration _configuration = LearningConfiguration.defaultConfig();
  PrivacySettings _privacySettings = PrivacySettings.defaultSettings();
  PerformanceSettings _performanceSettings =
      PerformanceSettings.defaultSettings();
  Timer? _backgroundTimer;
  Isolate? _backgroundIsolate;

  /// Initialize the learning configuration service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadConfiguration();
      await _loadPrivacySettings();
      await _loadPerformanceSettings();

      // Start background processing if enabled
      if (_configuration.isLearningEnabled &&
          _performanceSettings.enableBackgroundProcessing) {
        await _startBackgroundProcessing();
      }

      _isInitialized = true;

      if (kDebugMode) {
        print('Learning Configuration Service: Initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Learning Configuration Service: Initialization failed - $e');
      }
    }
  }

  /// Dispose resources
  void dispose() {
    _backgroundTimer?.cancel();
    _backgroundIsolate?.kill();
    _isInitialized = false;
  }

  /// Get current learning configuration
  LearningConfiguration getConfiguration() => _configuration;

  /// Get current privacy settings
  PrivacySettings getPrivacySettings() => _privacySettings;

  /// Get current performance settings
  PerformanceSettings getPerformanceSettings() => _performanceSettings;

  /// Update learning configuration
  Future<void> updateConfiguration(LearningConfiguration newConfig) async {
    // Validate configuration
    if (!_validateConfiguration(newConfig)) {
      throw ArgumentError('Invalid learning configuration');
    }

    final oldConfig = _configuration;
    _configuration = newConfig;

    try {
      await _saveConfiguration();

      // Restart background processing if settings changed
      if (oldConfig.isLearningEnabled != newConfig.isLearningEnabled ||
          oldConfig.analysisInterval != newConfig.analysisInterval) {
        await _restartBackgroundProcessing();
      }

      if (kDebugMode) {
        print('Learning Configuration Service: Configuration updated');
      }
    } catch (e) {
      // Rollback on error
      _configuration = oldConfig;
      if (kDebugMode) {
        print(
            'Learning Configuration Service: Failed to update configuration - $e');
      }
      rethrow;
    }
  }

  /// Update privacy settings
  Future<void> updatePrivacySettings(PrivacySettings newSettings) async {
    _privacySettings = newSettings;
    await _savePrivacySettings();

    if (kDebugMode) {
      print('Learning Configuration Service: Privacy settings updated');
    }
  }

  /// Update performance settings
  Future<void> updatePerformanceSettings(
      PerformanceSettings newSettings) async {
    final oldSettings = _performanceSettings;
    _performanceSettings = newSettings;

    try {
      await _savePerformanceSettings();

      // Restart background processing if background settings changed
      if (oldSettings.enableBackgroundProcessing !=
          newSettings.enableBackgroundProcessing) {
        await _restartBackgroundProcessing();
      }

      if (kDebugMode) {
        print('Learning Configuration Service: Performance settings updated');
      }
    } catch (e) {
      // Rollback on error
      _performanceSettings = oldSettings;
      if (kDebugMode) {
        print(
            'Learning Configuration Service: Failed to update performance settings - $e');
      }
      rethrow;
    }
  }

  /// Check if learning is enabled and privacy compliant
  bool isLearningAllowed() {
    return _configuration.isLearningEnabled &&
        _privacySettings.allowDataCollection &&
        _privacySettings.allowFeedbackAnalysis;
  }

  /// Check if background processing is allowed
  bool isBackgroundProcessingAllowed() {
    return isLearningAllowed() &&
        _performanceSettings.enableBackgroundProcessing &&
        !_performanceSettings.lowPowerMode;
  }

  /// Get anonymized configuration for analytics
  Map<String, dynamic> getAnonymizedConfiguration() {
    if (!_privacySettings.allowAnonymousAnalytics) {
      return {'analytics_disabled': true};
    }

    return {
      'learning_enabled': _configuration.isLearningEnabled,
      'analysis_interval_hours': _configuration.analysisInterval.inHours,
      'prompt_optimization_enabled': _configuration.enablePromptOptimization,
      'ab_testing_enabled': _configuration.enableABTesting,
      'background_processing_enabled':
          _performanceSettings.enableBackgroundProcessing,
      'low_power_mode': _performanceSettings.lowPowerMode,
    };
  }

  /// Validate learning configuration
  bool _validateConfiguration(LearningConfiguration config) {
    // Check analysis interval bounds
    if (config.analysisInterval.inMinutes < 30 ||
        config.analysisInterval.inHours > 168) {
      return false; // Must be between 30 minutes and 1 week
    }

    // Check threshold bounds
    if (config.minFeedbackThreshold < 5 || config.minFeedbackThreshold > 1000) {
      return false; // Must be between 5 and 1000
    }

    if (config.negativeThreshold < 0.1 || config.negativeThreshold > 0.9) {
      return false; // Must be between 10% and 90%
    }

    // Check learning rate bounds
    if (config.learningRate < 0.01 || config.learningRate > 1.0) {
      return false; // Must be between 1% and 100%
    }

    // Check max prompt versions
    if (config.maxPromptVersions < 2 || config.maxPromptVersions > 20) {
      return false; // Must be between 2 and 20
    }

    return true;
  }

  /// Start background processing
  Future<void> _startBackgroundProcessing() async {
    if (!isBackgroundProcessingAllowed()) return;

    _backgroundTimer?.cancel();

    // Use timer for lightweight background tasks
    _backgroundTimer = Timer.periodic(_configuration.analysisInterval, (timer) {
      _performBackgroundTasks();
    });

    if (kDebugMode) {
      print('Learning Configuration Service: Background processing started');
    }
  }

  /// Stop background processing
  Future<void> _stopBackgroundProcessing() async {
    _backgroundTimer?.cancel();
    _backgroundIsolate?.kill();

    if (kDebugMode) {
      print('Learning Configuration Service: Background processing stopped');
    }
  }

  /// Restart background processing
  Future<void> _restartBackgroundProcessing() async {
    await _stopBackgroundProcessing();
    if (_configuration.isLearningEnabled &&
        _performanceSettings.enableBackgroundProcessing) {
      await _startBackgroundProcessing();
    }
  }

  /// Perform background tasks
  void _performBackgroundTasks() {
    if (!isBackgroundProcessingAllowed()) return;

    // Schedule background tasks based on performance settings
    if (_performanceSettings.lowPowerMode) {
      // Minimal processing in low power mode
      _performLightweightTasks();
    } else {
      // Full processing when power is not a concern
      _performFullBackgroundTasks();
    }
  }

  /// Perform lightweight background tasks
  void _performLightweightTasks() {
    // Only essential tasks in low power mode
    if (kDebugMode) {
      print(
          'Learning Configuration Service: Performing lightweight background tasks');
    }

    // Trigger minimal learning analysis
    // This would be implemented to call the AI learning service with reduced processing
  }

  /// Perform full background tasks
  void _performFullBackgroundTasks() {
    if (kDebugMode) {
      print('Learning Configuration Service: Performing full background tasks');
    }

    // Trigger comprehensive learning analysis
    // This would be implemented to call the AI learning service with full processing
  }

  /// Load configuration from storage
  Future<void> _loadConfiguration() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final configJson = prefs.getString(_configKey);

      if (configJson != null) {
        final configMap = json.decode(configJson);
        _configuration = LearningConfiguration.fromMap(configMap);
      }
    } catch (e) {
      if (kDebugMode) {
        print(
            'Learning Configuration Service: Failed to load configuration - $e');
      }
      _configuration = LearningConfiguration.defaultConfig();
    }
  }

  /// Save configuration to storage
  Future<void> _saveConfiguration() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_configKey, json.encode(_configuration.toMap()));
    } catch (e) {
      if (kDebugMode) {
        print(
            'Learning Configuration Service: Failed to save configuration - $e');
      }
      rethrow;
    }
  }

  /// Load privacy settings from storage
  Future<void> _loadPrivacySettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString(_privacySettingsKey);

      if (settingsJson != null) {
        final settingsMap = json.decode(settingsJson);
        _privacySettings = PrivacySettings.fromMap(settingsMap);
      }
    } catch (e) {
      if (kDebugMode) {
        print(
            'Learning Configuration Service: Failed to load privacy settings - $e');
      }
      _privacySettings = PrivacySettings.defaultSettings();
    }
  }

  /// Save privacy settings to storage
  Future<void> _savePrivacySettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
          _privacySettingsKey, json.encode(_privacySettings.toMap()));
    } catch (e) {
      if (kDebugMode) {
        print(
            'Learning Configuration Service: Failed to save privacy settings - $e');
      }
      rethrow;
    }
  }

  /// Load performance settings from storage
  Future<void> _loadPerformanceSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString(_performanceSettingsKey);

      if (settingsJson != null) {
        final settingsMap = json.decode(settingsJson);
        _performanceSettings = PerformanceSettings.fromMap(settingsMap);
      }
    } catch (e) {
      if (kDebugMode) {
        print(
            'Learning Configuration Service: Failed to load performance settings - $e');
      }
      _performanceSettings = PerformanceSettings.defaultSettings();
    }
  }

  /// Save performance settings to storage
  Future<void> _savePerformanceSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
          _performanceSettingsKey, json.encode(_performanceSettings.toMap()));
    } catch (e) {
      if (kDebugMode) {
        print(
            'Learning Configuration Service: Failed to save performance settings - $e');
      }
      rethrow;
    }
  }
}

/// Privacy settings for AI learning
class PrivacySettings {
  bool allowDataCollection;
  bool allowFeedbackAnalysis;
  bool allowAnonymousAnalytics;
  bool allowPromptOptimization;
  bool retainDataLocally;
  int dataRetentionDays;

  PrivacySettings({
    required this.allowDataCollection,
    required this.allowFeedbackAnalysis,
    required this.allowAnonymousAnalytics,
    required this.allowPromptOptimization,
    required this.retainDataLocally,
    required this.dataRetentionDays,
  });

  /// Create default privacy settings
  factory PrivacySettings.defaultSettings() {
    return PrivacySettings(
      allowDataCollection: true,
      allowFeedbackAnalysis: true,
      allowAnonymousAnalytics: true,
      allowPromptOptimization: true,
      retainDataLocally: true,
      dataRetentionDays: 90,
    );
  }

  /// Create from map
  factory PrivacySettings.fromMap(Map<String, dynamic> map) {
    return PrivacySettings(
      allowDataCollection: map['allowDataCollection'] ?? true,
      allowFeedbackAnalysis: map['allowFeedbackAnalysis'] ?? true,
      allowAnonymousAnalytics: map['allowAnonymousAnalytics'] ?? true,
      allowPromptOptimization: map['allowPromptOptimization'] ?? true,
      retainDataLocally: map['retainDataLocally'] ?? true,
      dataRetentionDays: map['dataRetentionDays'] ?? 90,
    );
  }

  /// Convert to map
  Map<String, dynamic> toMap() {
    return {
      'allowDataCollection': allowDataCollection,
      'allowFeedbackAnalysis': allowFeedbackAnalysis,
      'allowAnonymousAnalytics': allowAnonymousAnalytics,
      'allowPromptOptimization': allowPromptOptimization,
      'retainDataLocally': retainDataLocally,
      'dataRetentionDays': dataRetentionDays,
    };
  }
}

/// Performance settings for AI learning
class PerformanceSettings {
  bool enableBackgroundProcessing;
  bool lowPowerMode;
  int maxConcurrentTasks;
  int maxMemoryUsageMB;
  bool enableCaching;
  bool optimizeForBattery;

  PerformanceSettings({
    required this.enableBackgroundProcessing,
    required this.lowPowerMode,
    required this.maxConcurrentTasks,
    required this.maxMemoryUsageMB,
    required this.enableCaching,
    required this.optimizeForBattery,
  });

  /// Create default performance settings
  factory PerformanceSettings.defaultSettings() {
    return PerformanceSettings(
      enableBackgroundProcessing: true,
      lowPowerMode: false,
      maxConcurrentTasks: 2,
      maxMemoryUsageMB: 50,
      enableCaching: true,
      optimizeForBattery: true,
    );
  }

  /// Create from map
  factory PerformanceSettings.fromMap(Map<String, dynamic> map) {
    return PerformanceSettings(
      enableBackgroundProcessing: map['enableBackgroundProcessing'] ?? true,
      lowPowerMode: map['lowPowerMode'] ?? false,
      maxConcurrentTasks: map['maxConcurrentTasks'] ?? 2,
      maxMemoryUsageMB: map['maxMemoryUsageMB'] ?? 50,
      enableCaching: map['enableCaching'] ?? true,
      optimizeForBattery: map['optimizeForBattery'] ?? true,
    );
  }

  /// Convert to map
  Map<String, dynamic> toMap() {
    return {
      'enableBackgroundProcessing': enableBackgroundProcessing,
      'lowPowerMode': lowPowerMode,
      'maxConcurrentTasks': maxConcurrentTasks,
      'maxMemoryUsageMB': maxMemoryUsageMB,
      'enableCaching': enableCaching,
      'optimizeForBattery': optimizeForBattery,
    };
  }
}
