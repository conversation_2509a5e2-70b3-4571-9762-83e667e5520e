import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../config/supabase_config.dart';
import '../utils/network_helper.dart';
import 'network_aware_supabase.dart';
import 'survey_service.dart';

class AuthService {
  static final SupabaseClient _supabase = SupabaseConfig.client;
  static final NetworkAwareSupabase _networkWrapper = NetworkAwareSupabase();

  // Google Sign In configuration - using client IDs directly (no config files needed)
  static final GoogleSignIn _googleSignIn = GoogleSignIn(
    clientId: kIsWeb
        ? '***********-et67jk6v8na6mc64eh88442gi81pdmhf.apps.googleusercontent.com' // Web client ID
        : null, // Android client ID will be read from google-services.json
    serverClientId:
        '***********-et67jk6v8na6mc64eh88442gi81pdmhf.apps.googleusercontent.com', // Add server client ID
    scopes: [
      'email',
      'profile',
      'openid', // Required for ID token
    ],
  );

  // Sign in with Google - simplified implementation using client IDs only
  static Future<AuthResponse?> signInWithGoogle() async {
    try {
      if (kDebugMode) {
        print('Google Sign In: Starting authentication process...');
      }

      // Sign in with Google
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      // User canceled the sign-in process
      if (googleUser == null) {
        if (kDebugMode) {
          print('Google Sign In: User canceled the sign-in process');
        }
        return null;
      }

      if (kDebugMode) {
        print('Google Sign In: User account obtained: ${googleUser.email}');
      }

      // Get authentication tokens
      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      if (kDebugMode) {
        print(
            'Google Sign In: Access token present: ${googleAuth.accessToken != null}');
        print(
            'Google Sign In: ID token present: ${googleAuth.idToken != null}');
      }

      // Check if we have the required ID token
      if (googleAuth.idToken == null) {
        if (kDebugMode) {
          print('Google Sign In Error: No ID token received');
        }
        throw Exception('Google Sign In failed: No ID token received');
      }

      if (kDebugMode) {
        print('Google Sign In: Authenticating with Supabase...');
      }

      // Authenticate with Supabase using the Google tokens
      final AuthResponse response = await _supabase.auth.signInWithIdToken(
        provider: OAuthProvider.google,
        idToken: googleAuth.idToken!,
        accessToken: googleAuth.accessToken,
      );

      if (kDebugMode) {
        print('Google Sign In: Supabase authentication successful');
        print('Google Sign In: User ID: ${response.user?.id}');
      }

      return response;
    } catch (error) {
      if (kDebugMode) {
        print('Google Sign In Error: $error');
        print('Error type: ${error.runtimeType}');
        print('Error details: ${error.toString()}');
      }

      // Provide more specific error message
      if (error.toString().contains('DEVELOPER_ERROR') ||
          error.toString().contains('10')) {
        throw Exception(
            'Google Sign-In configuration error. Please check your SHA-1 fingerprint and google-services.json file.');
      } else if (error.toString().contains('SIGN_IN_CANCELLED')) {
        throw Exception('Google Sign-In was cancelled by user.');
      } else if (error.toString().contains('NETWORK_ERROR')) {
        throw Exception(
            'Network error during Google Sign-In. Please check your internet connection.');
      }

      rethrow;
    }
  }

  // Sign in with email and password
  static Future<AuthResponse> signInWithEmail(
      String email, String password) async {
    try {
      final AuthResponse response = await _supabase.auth.signInWithPassword(
        email: email,
        password: password,
      );
      return response;
    } catch (error) {
      if (kDebugMode) {
        print('Email Sign In Error: $error');
      }
      rethrow;
    }
  }

  // Sign up with email and password (with immediate access)
  static Future<AuthResponse> signUpWithEmail(String email, String password,
      {String? username}) async {
    try {
      final AuthResponse response = await _supabase.auth.signUp(
        email: email,
        password: password,
        data: username != null ? {'username': username} : null,
        emailRedirectTo: null, // Disable email verification redirect
      );

      // Check if we have a valid session immediately
      if (response.user != null && response.session != null) {
        // User is already signed in, return the response
        return response;
      }

      // If user is created but not confirmed (due to email verification being disabled),
      // we need to sign them in immediately
      if (response.user != null && response.session == null) {
        if (kDebugMode) {
          print(
              'User created but not signed in, attempting immediate sign-in...');
        }

        // Wait a moment for the user to be fully created in the database
        await Future.delayed(const Duration(milliseconds: 500));

        // Sign in the user immediately after registration
        final signInResponse = await signInWithEmail(email, password);
        return signInResponse;
      }

      return response;
    } catch (error) {
      if (kDebugMode) {
        print('Email Sign Up Error: $error');
      }
      rethrow;
    }
  }

  // Sign in as guest (anonymous)
  static Future<AuthResponse> signInAsGuest() async {
    try {
      if (kDebugMode) {
        print('Auth Service: Attempting guest sign in...');
      }

      // Check network connectivity first
      final hasInternet = await NetworkHelper.hasInternetConnection();
      if (!hasInternet) {
        throw Exception(
            'No internet connection available. Please check your network settings and try again.');
      }

      // Check if Supabase host is reachable
      final canReachSupabase =
          await NetworkHelper.canReachHost('ktdstluymbpqejmjkpsg.supabase.co');
      if (!canReachSupabase) {
        throw Exception(
            'Cannot reach Supabase server. Please check your network connection or try again later.');
      }

      // Use retry mechanism for the actual sign-in
      final AuthResponse response = await NetworkHelper.retryNetworkOperation(
        () => _supabase.auth.signInAnonymously(),
        maxRetries: 3,
        delay: const Duration(seconds: 2),
      );

      if (kDebugMode) {
        print('Auth Service: Guest sign in successful');
      }

      return response;
    } catch (error) {
      if (kDebugMode) {
        print('Guest Sign In Error: $error');

        // Provide user-friendly error message
        final userMessage = NetworkHelper.getNetworkErrorMessage(error);
        print('User-friendly message: $userMessage');
      }
      rethrow;
    }
  }

  // Reset password
  static Future<void> resetPassword(String email) async {
    try {
      await _supabase.auth.resetPasswordForEmail(
        email,
        redirectTo: kIsWeb ? null : 'io.supabase.tripwisego://reset-password/',
      );
    } catch (error) {
      if (kDebugMode) {
        print('Reset Password Error: $error');
      }
      rethrow;
    }
  }

  // Sign out
  static Future<void> signOut() async {
    try {
      if (kDebugMode) {
        print('Auth Service: Starting sign out process...');
        print('Auth Service: User type: ${getUserType()}');
      }

      // Sign out from Google if user was signed in with Google
      await _googleSignIn.signOut();

      // Sign out from Supabase with network-aware handling
      await _networkWrapper.executeWithNetworkHandling(
        () => _supabase.auth.signOut(),
        operationName: 'sign_out',
        logErrors: false, // Don't log network errors for sign out
      );

      if (kDebugMode) {
        print('Auth Service: Sign out completed successfully');
      }
    } catch (error) {
      if (kDebugMode) {
        print('Sign Out Error: $error');
      }
      rethrow;
    }
  }

  // Check if current user is a guest
  static bool get isGuestUser {
    final user = currentUser;
    return user != null && user.isAnonymous;
  }

  // Get current user
  static User? get currentUser => _supabase.auth.currentUser;

  // Check if user is signed in
  static bool get isSignedIn => _supabase.auth.currentUser != null;

  // Listen to auth state changes
  static Stream<AuthState> get authStateChanges =>
      _supabase.auth.onAuthStateChange;

  // Get current session
  static Session? get currentSession => _supabase.auth.currentSession;

  // Check if session is valid and not expired
  static bool get hasValidSession {
    final session = currentSession;
    if (session == null) return false;

    // Check if session is expired
    final expiresAt =
        DateTime.fromMillisecondsSinceEpoch(session.expiresAt! * 1000);
    return DateTime.now().isBefore(expiresAt);
  }

  // Refresh session if needed
  static Future<AuthResponse?> refreshSession() async {
    try {
      final session = currentSession;
      if (session?.refreshToken != null) {
        // Use network wrapper to handle network errors gracefully
        final response = await _networkWrapper.executeWithNetworkHandling(
          () => _supabase.auth.refreshSession(),
          operationName: 'session_refresh',
          fallbackValue: null,
        );
        return response;
      }
      return null;
    } catch (error) {
      if (kDebugMode) {
        print('Session refresh error: $error');
      }
      return null;
    }
  }

  // Initialize session on app start
  static Future<bool> initializeSession() async {
    try {
      // Check if we have a valid session
      if (hasValidSession) {
        return true;
      }

      // Try to refresh session if we have a refresh token
      final refreshResponse = await refreshSession();
      return refreshResponse?.session != null;
    } catch (error) {
      if (kDebugMode) {
        print('Session initialization error: $error');
      }
      return false;
    }
  }

  // Check if user is currently signed in with Google
  static Future<bool> isGoogleSignedIn() async {
    try {
      return await _googleSignIn.isSignedIn();
    } catch (error) {
      if (kDebugMode) {
        print('Error checking Google Sign-In status: $error');
      }
      return false;
    }
  }

  // Wait for OAuth authentication to complete
  static Future<bool> waitForOAuthCompletion(
      {Duration timeout = const Duration(minutes: 2)}) async {
    if (kDebugMode) {
      print('Auth Service: Waiting for OAuth completion...');
    }

    final completer = Completer<bool>();
    StreamSubscription<AuthState>? subscription;
    Timer? timeoutTimer;

    // Set up timeout
    timeoutTimer = Timer(timeout, () {
      if (!completer.isCompleted) {
        if (kDebugMode) {
          print('Auth Service: OAuth completion timeout');
        }
        subscription?.cancel();
        completer.complete(false);
      }
    });

    // Listen for auth state changes
    subscription = authStateChanges.listen((AuthState authState) {
      if (authState.event == AuthChangeEvent.signedIn &&
          authState.session != null) {
        if (kDebugMode) {
          print('Auth Service: OAuth authentication completed successfully');
        }
        timeoutTimer?.cancel();
        subscription?.cancel();
        if (!completer.isCompleted) {
          completer.complete(true);
        }
      }
    });

    // Check if already signed in
    if (isSignedIn && hasValidSession) {
      if (kDebugMode) {
        print('Auth Service: Already authenticated');
      }
      timeoutTimer.cancel();
      subscription.cancel();
      if (!completer.isCompleted) {
        completer.complete(true);
      }
    }

    return completer.future;
  }

  // Enhanced session persistence check
  static Future<bool> ensureSessionPersistence() async {
    try {
      if (kDebugMode) {
        print('Auth Service: Ensuring session persistence...');
      }

      // Check if we have a current session
      final session = currentSession;
      if (session == null) {
        if (kDebugMode) {
          print('Auth Service: No current session found');
        }
        return false;
      }

      // Check if session is still valid
      if (!hasValidSession) {
        if (kDebugMode) {
          print('Auth Service: Session expired, attempting refresh...');
        }

        // Try to refresh the session
        final refreshResponse = await refreshSession();
        if (refreshResponse?.session != null) {
          if (kDebugMode) {
            print('Auth Service: Session refreshed successfully');
          }
          return true;
        } else {
          if (kDebugMode) {
            print('Auth Service: Session refresh failed');
          }
          return false;
        }
      }

      if (kDebugMode) {
        print('Auth Service: Session is valid and persistent');
      }
      return true;
    } catch (error) {
      if (kDebugMode) {
        print('Auth Service: Error ensuring session persistence: $error');
      }
      return false;
    }
  }

  // Check if current user has completed the survey
  static Future<bool> hasSurveyCompleted() async {
    try {
      final user = currentUser;
      if (user == null) {
        if (kDebugMode) {
          print('Auth Service: No current user for survey check');
        }
        return false;
      }

      // Check if user is anonymous (guest) - they don't need to complete survey
      if (user.isAnonymous) {
        if (kDebugMode) {
          print('Auth Service: Anonymous user, survey not required');
        }
        return true;
      }

      return await SurveyService.hasSurveyCompleted(user.id);
    } catch (error) {
      if (kDebugMode) {
        print('Auth Service: Error checking survey completion: $error');
      }
      return false;
    }
  }

  // Check if user needs to complete survey (authenticated but not completed survey)
  static Future<bool> needsSurveyCompletion() async {
    try {
      if (!isSignedIn) {
        return false;
      }

      final user = currentUser;
      if (user == null || user.isAnonymous) {
        return false;
      }

      final surveyCompleted = await hasSurveyCompleted();
      return !surveyCompleted;
    } catch (error) {
      if (kDebugMode) {
        print('Auth Service: Error checking survey completion need: $error');
      }
      return false;
    }
  }

  // Mark survey as completed for current user
  static Future<bool> markSurveyCompleted() async {
    try {
      final user = currentUser;
      if (user == null) {
        if (kDebugMode) {
          print('Auth Service: No current user to mark survey completed');
        }
        return false;
      }

      return await SurveyService.markSurveyCompleted(user.id);
    } catch (error) {
      if (kDebugMode) {
        print('Auth Service: Error marking survey completed: $error');
      }
      return false;
    }
  }

  // Get user type for survey requirements
  static String getUserType() {
    final user = currentUser;
    if (user == null) return 'unauthenticated';
    if (user.isAnonymous) return 'guest';
    return 'registered';
  }

  // Check if user is a registered user (not guest/anonymous)
  static bool get isRegisteredUser {
    final user = currentUser;
    return user != null && !user.isAnonymous;
  }
}
