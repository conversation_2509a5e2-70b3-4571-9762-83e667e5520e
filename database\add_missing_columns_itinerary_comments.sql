-- SQL script to add missing columns to itinerary_comments table
-- Run this in your Supabase SQL Editor

-- Add missing columns to itinerary_comments table
ALTER TABLE itinerary_comments
ADD COLUMN IF NOT EXISTS itinerary_id UUID NOT NULL REFERENCES collaborative_itineraries(id) ON DELETE CASCADE,
ADD COLUMN IF NOT EXISTS user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
ADD COLUMN IF NOT EXISTS content TEXT NOT NULL,
ADD COLUMN IF NOT EXISTS parent_comment_id BIGINT REFERENCES itinerary_comments(id) ON DELETE CASCADE,
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Create an index on itinerary_id for better query performance
CREATE INDEX IF NOT EXISTS idx_itinerary_comments_itinerary_id ON itinerary_comments(itinerary_id);

-- Create an index on user_id for better query performance
CREATE INDEX IF NOT EXISTS idx_itinerary_comments_user_id ON itinerary_comments(user_id);

-- Create an index on parent_comment_id for threaded comments
CREATE INDEX IF NOT EXISTS idx_itinerary_comments_parent_id ON itinerary_comments(parent_comment_id);

-- Create an index on created_at for chronological ordering
CREATE INDEX IF NOT EXISTS idx_itinerary_comments_created_at ON itinerary_comments(created_at);

-- Add a trigger to automatically update the updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for updated_at
DROP TRIGGER IF EXISTS update_itinerary_comments_updated_at ON itinerary_comments;
CREATE TRIGGER update_itinerary_comments_updated_at
    BEFORE UPDATE ON itinerary_comments
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS) if not already enabled
ALTER TABLE itinerary_comments ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for itinerary_comments
-- Policy: Users can view comments for itineraries they have access to
DROP POLICY IF EXISTS "Users can view comments for accessible itineraries" ON itinerary_comments;
CREATE POLICY "Users can view comments for accessible itineraries" ON itinerary_comments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM collaboration_participants cp
            WHERE cp.itinerary_id = itinerary_comments.itinerary_id
            AND cp.user_id = auth.uid()
        )
        OR
        EXISTS (
            SELECT 1 FROM collaborative_itineraries ci
            WHERE ci.id = itinerary_comments.itinerary_id
            AND ci.owner_id = auth.uid()
        )
    );

-- Policy: Users can insert comments for itineraries they have access to
DROP POLICY IF EXISTS "Users can insert comments for accessible itineraries" ON itinerary_comments;
CREATE POLICY "Users can insert comments for accessible itineraries" ON itinerary_comments
    FOR INSERT WITH CHECK (
        auth.uid() = user_id
        AND (
            EXISTS (
                SELECT 1 FROM collaboration_participants cp
                WHERE cp.itinerary_id = itinerary_comments.itinerary_id
                AND cp.user_id = auth.uid()
            )
            OR
            EXISTS (
                SELECT 1 FROM collaborative_itineraries ci
                WHERE ci.id = itinerary_comments.itinerary_id
                AND ci.owner_id = auth.uid()
            )
        )
    );

-- Policy: Users can update their own comments
DROP POLICY IF EXISTS "Users can update their own comments" ON itinerary_comments;
CREATE POLICY "Users can update their own comments" ON itinerary_comments
    FOR UPDATE USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

-- Policy: Users can delete their own comments
DROP POLICY IF EXISTS "Users can delete their own comments" ON itinerary_comments;
CREATE POLICY "Users can delete their own comments" ON itinerary_comments
    FOR DELETE USING (auth.uid() = user_id);

-- Grant necessary permissions
GRANT ALL ON itinerary_comments TO authenticated;
GRANT SELECT ON itinerary_comments TO anon;

-- Add comments to document the table structure
COMMENT ON TABLE itinerary_comments IS 'Comments on collaborative itineraries and activities';
COMMENT ON COLUMN itinerary_comments.id IS 'Primary key for the comment';
COMMENT ON COLUMN itinerary_comments.itinerary_id IS 'Reference to the collaborative itinerary';
COMMENT ON COLUMN itinerary_comments.user_id IS 'Reference to the user who made the comment';
COMMENT ON COLUMN itinerary_comments.content IS 'The actual comment text content';
COMMENT ON COLUMN itinerary_comments.parent_comment_id IS 'Reference to parent comment for threaded discussions';
COMMENT ON COLUMN itinerary_comments.activity_id IS 'Reference to specific activity (optional)';
COMMENT ON COLUMN itinerary_comments.activity_day IS 'Day number for activity-specific comments';
COMMENT ON COLUMN itinerary_comments.comment_type IS 'Type of comment: itinerary or activity';
COMMENT ON COLUMN itinerary_comments.created_at IS 'Timestamp when comment was created';
COMMENT ON COLUMN itinerary_comments.updated_at IS 'Timestamp when comment was last updated';
