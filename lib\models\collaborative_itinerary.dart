class CollaborativeItinerary {
  final String id;
  final String title;
  final String startDate;
  final String endDate;
  final List<String> destinations;
  final bool hasPhoto;
  final String? imagePath;
  final Map<String, List<String>> dailyActivities;
  final Map<int, Map<String, List<String>>>? daySpecificActivities;
  final Map<String, Map<String, Map<String, String>>>? activityTimes;
  final Map<String, Map<String, String>>? activityImages;
  final String accommodation;
  final String additionalNotes;

  // Collaboration specific fields
  final String ownerId;
  final String collaborationCode;
  final bool isPublic;
  final int maxCollaborators;
  final DateTime createdAt;
  final DateTime updatedAt;

  CollaborativeItinerary({
    required this.id,
    required this.title,
    required this.startDate,
    required this.endDate,
    required this.destinations,
    required this.hasPhoto,
    this.imagePath,
    required this.dailyActivities,
    this.daySpecificActivities,
    this.activityTimes,
    this.activityImages,
    required this.accommodation,
    required this.additionalNotes,
    required this.ownerId,
    required this.collaborationCode,
    this.isPublic = true,
    this.maxCollaborators = 10,
    required this.createdAt,
    required this.updatedAt,
  });

  // Convert from JSON (Supabase response)
  factory CollaborativeItinerary.fromJson(Map<String, dynamic> json) {
    return CollaborativeItinerary(
      id: json['id'] as String,
      title: json['title'] as String,
      startDate: CollaborativeItinerary._convertIsoToDisplayDate(
          json['start_date'] as String),
      endDate: CollaborativeItinerary._convertIsoToDisplayDate(
          json['end_date'] as String),
      destinations: List<String>.from(json['destinations'] ?? []),
      hasPhoto: json['has_photo'] as bool? ?? false,
      imagePath: json['image_path'] as String?,
      dailyActivities: Map<String, List<String>>.from(
        (json['daily_activities'] as Map<String, dynamic>? ?? {}).map(
          (key, value) => MapEntry(key, List<String>.from(value ?? [])),
        ),
      ),
      daySpecificActivities: json['day_specific_activities'] != null
          ? Map<int, Map<String, List<String>>>.from(
              (json['day_specific_activities'] as Map<String, dynamic>).map(
                (key, value) => MapEntry(
                  int.parse(key),
                  Map<String, List<String>>.from(
                    (value as Map<String, dynamic>).map(
                      (k, v) => MapEntry(k, List<String>.from(v ?? [])),
                    ),
                  ),
                ),
              ),
            )
          : null,
      activityTimes: json['activity_times'] != null
          ? Map<String, Map<String, Map<String, String>>>.from(
              (json['activity_times'] as Map<String, dynamic>).map(
                (key, value) => MapEntry(
                  key,
                  Map<String, Map<String, String>>.from(
                    (value as Map<String, dynamic>).map(
                      (k, v) => MapEntry(
                        k,
                        Map<String, String>.from(v as Map<String, dynamic>),
                      ),
                    ),
                  ),
                ),
              ),
            )
          : null,
      activityImages: json['activity_images'] != null
          ? Map<String, Map<String, String>>.from(
              (json['activity_images'] as Map<String, dynamic>).map(
                (key, value) => MapEntry(
                  key,
                  Map<String, String>.from(value as Map<String, dynamic>),
                ),
              ),
            )
          : null,
      accommodation: json['accommodation'] as String? ?? '',
      additionalNotes: json['additional_notes'] as String? ?? '',
      ownerId: json['owner_id'] as String,
      collaborationCode: json['collaboration_code'] as String,
      isPublic: json['is_public'] as bool? ?? true,
      maxCollaborators: json['max_collaborators'] as int? ?? 10,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  // Convert to JSON for Supabase
  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'title': title,
      'start_date': _convertDateToIso(startDate),
      'end_date': _convertDateToIso(endDate),
      'destinations': destinations,
      'has_photo': hasPhoto,
      'image_path': imagePath,
      'daily_activities': dailyActivities,
      'day_specific_activities': daySpecificActivities?.map(
        (day, activities) => MapEntry(day.toString(), activities),
      ),
      'activity_times': activityTimes,
      'activity_images': activityImages,
      'accommodation': accommodation,
      'additional_notes': additionalNotes,
      'owner_id': ownerId,
      'collaboration_code': collaborationCode,
      'is_public': isPublic,
      'max_collaborators': maxCollaborators,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };

    // Only include id if it's not empty (for updates)
    if (id.isNotEmpty) {
      json['id'] = id;
    }

    return json;
  }

  // Helper method to convert DD/MM/YYYY to YYYY-MM-DD
  String _convertDateToIso(String dateString) {
    try {
      // Check if it's already in ISO format (YYYY-MM-DD)
      if (RegExp(r'^\d{4}-\d{2}-\d{2}$').hasMatch(dateString)) {
        return dateString;
      }

      // Parse DD/MM/YYYY format
      if (RegExp(r'^\d{2}/\d{2}/\d{4}$').hasMatch(dateString)) {
        final parts = dateString.split('/');
        final day = parts[0];
        final month = parts[1];
        final year = parts[2];
        return '$year-$month-$day';
      }

      // If it's some other format, try to parse it as DateTime and convert
      final parsedDate = DateTime.parse(dateString);
      return '${parsedDate.year.toString().padLeft(4, '0')}-${parsedDate.month.toString().padLeft(2, '0')}-${parsedDate.day.toString().padLeft(2, '0')}';
    } catch (e) {
      // If all else fails, return the original string
      return dateString;
    }
  }

  // Helper method to convert YYYY-MM-DD to DD/MM/YYYY for display
  static String _convertIsoToDisplayDate(String isoDateString) {
    try {
      // Check if it's already in DD/MM/YYYY format
      if (RegExp(r'^\d{2}/\d{2}/\d{4}$').hasMatch(isoDateString)) {
        return isoDateString;
      }

      // Parse ISO format (YYYY-MM-DD)
      if (RegExp(r'^\d{4}-\d{2}-\d{2}$').hasMatch(isoDateString)) {
        final parts = isoDateString.split('-');
        final year = parts[0];
        final month = parts[1];
        final day = parts[2];
        return '$day/$month/$year';
      }

      // If it's some other format, try to parse it as DateTime and convert
      final parsedDate = DateTime.parse(isoDateString);
      return '${parsedDate.day.toString().padLeft(2, '0')}/${parsedDate.month.toString().padLeft(2, '0')}/${parsedDate.year}';
    } catch (e) {
      // If all else fails, return the original string
      return isoDateString;
    }
  }

  // Create from local Itinerary
  factory CollaborativeItinerary.fromLocalItinerary({
    required String id,
    required String ownerId,
    required String collaborationCode,
    required dynamic localItinerary,
  }) {
    return CollaborativeItinerary(
      id: id,
      title: localItinerary.title,
      startDate: localItinerary.startDate,
      endDate: localItinerary.endDate,
      destinations: localItinerary.destinations,
      hasPhoto: localItinerary.hasPhoto,
      imagePath: localItinerary.imagePath,
      dailyActivities: localItinerary.dailyActivities,
      daySpecificActivities: localItinerary.daySpecificActivities,
      activityTimes: localItinerary.activityTimes,
      activityImages: localItinerary.activityImages,
      accommodation: localItinerary.accommodation,
      additionalNotes: localItinerary.additionalNotes,
      ownerId: ownerId,
      collaborationCode: collaborationCode,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  // Helper getters
  String get dateRange {
    return '$startDate - $endDate';
  }

  int get destinationCount {
    return destinations.length;
  }

  int get totalDays {
    try {
      final start = _parseDisplayDate(startDate);
      final end = _parseDisplayDate(endDate);
      return end.difference(start).inDays + 1;
    } catch (e) {
      return 1;
    }
  }

  // Helper method to parse DD/MM/YYYY format dates
  DateTime _parseDisplayDate(String dateString) {
    try {
      // Try parsing as DD/MM/YYYY first
      if (RegExp(r'^\d{2}/\d{2}/\d{4}$').hasMatch(dateString)) {
        final parts = dateString.split('/');
        final day = int.parse(parts[0]);
        final month = int.parse(parts[1]);
        final year = int.parse(parts[2]);
        return DateTime(year, month, day);
      }

      // Fallback to standard DateTime.parse for other formats
      return DateTime.parse(dateString);
    } catch (e) {
      // If all else fails, return current date
      return DateTime.now();
    }
  }

  // Copy with method for updates
  CollaborativeItinerary copyWith({
    String? id,
    String? title,
    String? startDate,
    String? endDate,
    List<String>? destinations,
    bool? hasPhoto,
    String? imagePath,
    Map<String, List<String>>? dailyActivities,
    Map<int, Map<String, List<String>>>? daySpecificActivities,
    Map<String, Map<String, Map<String, String>>>? activityTimes,
    Map<String, Map<String, String>>? activityImages,
    String? accommodation,
    String? additionalNotes,
    String? ownerId,
    String? collaborationCode,
    bool? isPublic,
    int? maxCollaborators,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CollaborativeItinerary(
      id: id ?? this.id,
      title: title ?? this.title,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      destinations: destinations ?? this.destinations,
      hasPhoto: hasPhoto ?? this.hasPhoto,
      imagePath: imagePath ?? this.imagePath,
      dailyActivities: dailyActivities ?? this.dailyActivities,
      daySpecificActivities:
          daySpecificActivities ?? this.daySpecificActivities,
      activityTimes: activityTimes ?? this.activityTimes,
      activityImages: activityImages ?? this.activityImages,
      accommodation: accommodation ?? this.accommodation,
      additionalNotes: additionalNotes ?? this.additionalNotes,
      ownerId: ownerId ?? this.ownerId,
      collaborationCode: collaborationCode ?? this.collaborationCode,
      isPublic: isPublic ?? this.isPublic,
      maxCollaborators: maxCollaborators ?? this.maxCollaborators,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CollaborativeItinerary && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'CollaborativeItinerary(id: $id, title: $title, code: $collaborationCode)';
  }
}
