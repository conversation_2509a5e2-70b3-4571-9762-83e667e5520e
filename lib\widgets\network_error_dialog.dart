import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class NetworkErrorDialog extends StatelessWidget {
  final String title;
  final String message;
  final VoidCallback? onRetry;
  final VoidCallback? onCancel;

  const NetworkErrorDialog({
    super.key,
    this.title = 'Connection Error',
    required this.message,
    this.onRetry,
    this.onCancel,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: const Color(0xFFF7F9FC),
      surfaceTintColor: const Color(0xFFF7F9FC),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      title: Row(
        children: [
          const Icon(
            Icons.wifi_off,
            color: Color(0xFF0D76FF),
            size: 24,
          ),
          const SizedBox(width: 12),
          Text(
            title,
            style: GoogleFonts.instrumentSans(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF1A1A1A),
            ),
          ),
        ],
      ),
      content: Text(
        message,
        style: GoogleFonts.instrumentSans(
          fontSize: 14,
          fontWeight: FontWeight.w400,
          color: const Color(0xFF666666),
          height: 1.4,
        ),
      ),
      actions: [
        if (onCancel != null)
          TextButton(
            onPressed: onCancel,
            style: TextButton.styleFrom(
              foregroundColor: const Color(0xFF666666),
            ),
            child: Text(
              'Cancel',
              style: GoogleFonts.instrumentSans(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        if (onRetry != null)
          ElevatedButton(
            onPressed: onRetry,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF0D76FF),
              foregroundColor: Colors.white,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'Retry',
              style: GoogleFonts.instrumentSans(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
      ],
    );
  }

  /// Show network error dialog
  static Future<bool?> show(
    BuildContext context, {
    String? title,
    required String message,
    bool showRetry = true,
  }) {
    return showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => NetworkErrorDialog(
        title: title ?? 'Connection Error',
        message: message,
        onRetry: showRetry ? () => Navigator.of(context).pop(true) : null,
        onCancel: () => Navigator.of(context).pop(false),
      ),
    );
  }

  /// Show specific network error based on error type
  static Future<bool?> showNetworkError(
    BuildContext context,
    dynamic error,
  ) {
    String message;
    String title = 'Connection Error';

    final errorString = error.toString().toLowerCase();

    if (errorString.contains('socketexception') ||
        errorString.contains('failed host lookup')) {
      title = 'Network Unavailable';
      message =
          'Unable to connect to the server. Please check your internet connection and try again.';
    } else if (errorString.contains('timeout')) {
      title = 'Connection Timeout';
      message =
          'The connection is taking too long. Please check your internet connection and try again.';
    } else if (errorString.contains('certificate') ||
        errorString.contains('ssl') ||
        errorString.contains('tls')) {
      title = 'Security Error';
      message =
          'There was a problem with the secure connection. Please check your network settings.';
    } else {
      message = 'A network error occurred. Please try again later.';
    }

    return show(
      context,
      title: title,
      message: message,
    );
  }
}
