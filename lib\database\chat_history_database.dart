import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:flutter/foundation.dart';
import '../models/chat_history_models.dart';

class ChatHistoryDatabase {
  static Database? _database;
  static const String _databaseName = 'chat_history.db';
  static const int _databaseVersion = 1;

  // Table names
  static const String _chatSessionsTable = 'chat_sessions';
  static const String _chatMessagesTable = 'chat_messages';

  /// Get database instance
  static Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  /// Initialize the database
  static Future<Database> _initDatabase() async {
    try {
      final databasesPath = await getDatabasesPath();
      final path = join(databasesPath, _databaseName);

      if (kDebugMode) {
        print('Chat Database: Initializing database at $path');
      }

      return await openDatabase(
        path,
        version: _databaseVersion,
        onCreate: _onCreate,
        onUpgrade: _onUpgrade,
      );
    } catch (e) {
      if (kDebugMode) {
        print('Chat Database: Failed to initialize database: $e');
      }
      rethrow;
    }
  }

  /// Create database tables
  static Future<void> _onCreate(Database db, int version) async {
    try {
      if (kDebugMode) {
        print('Chat Database: Creating tables...');
      }

      // Create chat_sessions table
      await db.execute('''
        CREATE TABLE $_chatSessionsTable (
          id TEXT PRIMARY KEY,
          title TEXT NOT NULL,
          created_at INTEGER NOT NULL,
          updated_at INTEGER NOT NULL,
          is_active INTEGER NOT NULL DEFAULT 0
        )
      ''');

      // Create chat_messages table
      await db.execute('''
        CREATE TABLE $_chatMessagesTable (
          id TEXT PRIMARY KEY,
          chat_id TEXT NOT NULL,
          text TEXT NOT NULL,
          is_user INTEGER NOT NULL,
          timestamp INTEGER NOT NULL,
          image_path TEXT,
          is_image_message INTEGER NOT NULL DEFAULT 0,
          is_web_search_message INTEGER NOT NULL DEFAULT 0,
          search_query TEXT,
          search_result_data TEXT,
          has_user_feedback INTEGER,
          feedback_type TEXT,
          feedback_id TEXT,
          FOREIGN KEY (chat_id) REFERENCES $_chatSessionsTable (id) ON DELETE CASCADE
        )
      ''');

      // Create indexes for better performance
      await db.execute('''
        CREATE INDEX idx_chat_messages_chat_id ON $_chatMessagesTable (chat_id)
      ''');

      await db.execute('''
        CREATE INDEX idx_chat_messages_timestamp ON $_chatMessagesTable (timestamp)
      ''');

      await db.execute('''
        CREATE INDEX idx_chat_sessions_updated_at ON $_chatSessionsTable (updated_at)
      ''');

      await db.execute('''
        CREATE INDEX idx_chat_sessions_is_active ON $_chatSessionsTable (is_active)
      ''');

      if (kDebugMode) {
        print('Chat Database: Tables created successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Chat Database: Failed to create tables: $e');
      }
      rethrow;
    }
  }

  /// Handle database upgrades
  static Future<void> _onUpgrade(
      Database db, int oldVersion, int newVersion) async {
    if (kDebugMode) {
      print('Chat Database: Upgrading from version $oldVersion to $newVersion');
    }
    // Handle future database schema changes here
  }

  /// Insert a new chat session
  static Future<void> insertChatSession(ChatSession session) async {
    try {
      final db = await database;
      await db.insert(
        _chatSessionsTable,
        {
          'id': session.id,
          'title': session.title,
          'created_at': session.createdAt.millisecondsSinceEpoch,
          'updated_at': session.updatedAt.millisecondsSinceEpoch,
          'is_active': session.isActive ? 1 : 0,
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      if (kDebugMode) {
        print('Chat Database: Inserted session ${session.id}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Chat Database: Failed to insert session: $e');
      }
      rethrow;
    }
  }

  /// Update a chat session
  static Future<void> updateChatSession(ChatSession session) async {
    try {
      final db = await database;
      await db.update(
        _chatSessionsTable,
        {
          'title': session.title,
          'updated_at': session.updatedAt.millisecondsSinceEpoch,
          'is_active': session.isActive ? 1 : 0,
        },
        where: 'id = ?',
        whereArgs: [session.id],
      );

      if (kDebugMode) {
        print('Chat Database: Updated session ${session.id}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Chat Database: Failed to update session: $e');
      }
      rethrow;
    }
  }

  /// Get a chat session by ID
  static Future<ChatSession?> getChatSession(String sessionId) async {
    try {
      final db = await database;
      final List<Map<String, dynamic>> maps = await db.query(
        _chatSessionsTable,
        where: 'id = ?',
        whereArgs: [sessionId],
      );

      if (maps.isEmpty) return null;

      final sessionData = maps.first;
      final messages = await getChatMessages(sessionId);

      return ChatSession(
        id: sessionData['id'],
        title: sessionData['title'],
        createdAt:
            DateTime.fromMillisecondsSinceEpoch(sessionData['created_at']),
        updatedAt:
            DateTime.fromMillisecondsSinceEpoch(sessionData['updated_at']),
        messages: messages,
        isActive: sessionData['is_active'] == 1,
      );
    } catch (e) {
      if (kDebugMode) {
        print('Chat Database: Failed to get session $sessionId: $e');
      }
      return null;
    }
  }

  /// Get all chat sessions ordered by updated_at descending
  static Future<List<ChatSession>> getAllChatSessions() async {
    try {
      final db = await database;
      final List<Map<String, dynamic>> maps = await db.query(
        _chatSessionsTable,
        orderBy: 'updated_at DESC',
      );

      List<ChatSession> sessions = [];
      for (final sessionData in maps) {
        final messages = await getChatMessages(sessionData['id']);
        sessions.add(ChatSession(
          id: sessionData['id'],
          title: sessionData['title'],
          createdAt:
              DateTime.fromMillisecondsSinceEpoch(sessionData['created_at']),
          updatedAt:
              DateTime.fromMillisecondsSinceEpoch(sessionData['updated_at']),
          messages: messages,
          isActive: sessionData['is_active'] == 1,
        ));
      }

      return sessions;
    } catch (e) {
      if (kDebugMode) {
        print('Chat Database: Failed to get all sessions: $e');
      }
      return [];
    }
  }

  /// Get the active chat session
  static Future<ChatSession?> getActiveChatSession() async {
    try {
      final db = await database;
      final List<Map<String, dynamic>> maps = await db.query(
        _chatSessionsTable,
        where: 'is_active = ?',
        whereArgs: [1],
        limit: 1,
      );

      if (maps.isEmpty) return null;

      final sessionData = maps.first;
      final messages = await getChatMessages(sessionData['id']);

      return ChatSession(
        id: sessionData['id'],
        title: sessionData['title'],
        createdAt:
            DateTime.fromMillisecondsSinceEpoch(sessionData['created_at']),
        updatedAt:
            DateTime.fromMillisecondsSinceEpoch(sessionData['updated_at']),
        messages: messages,
        isActive: true,
      );
    } catch (e) {
      if (kDebugMode) {
        print('Chat Database: Failed to get active session: $e');
      }
      return null;
    }
  }

  /// Set a session as active (deactivates all others)
  static Future<void> setActiveSession(String sessionId) async {
    try {
      final db = await database;

      // Start a transaction to ensure atomicity
      await db.transaction((txn) async {
        // Deactivate all sessions
        await txn.update(
          _chatSessionsTable,
          {'is_active': 0},
          where: 'is_active = ?',
          whereArgs: [1],
        );

        // Activate the specified session
        await txn.update(
          _chatSessionsTable,
          {'is_active': 1},
          where: 'id = ?',
          whereArgs: [sessionId],
        );
      });

      if (kDebugMode) {
        print('Chat Database: Set session $sessionId as active');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Chat Database: Failed to set active session: $e');
      }
      rethrow;
    }
  }

  /// Deactivate all sessions
  static Future<void> deactivateAllSessions() async {
    try {
      final db = await database;
      await db.update(
        _chatSessionsTable,
        {'is_active': 0},
        where: 'is_active = ?',
        whereArgs: [1],
      );

      if (kDebugMode) {
        print('Chat Database: Deactivated all sessions');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Chat Database: Failed to deactivate all sessions: $e');
      }
      rethrow;
    }
  }

  /// Delete a chat session and all its messages
  static Future<void> deleteChatSession(String sessionId) async {
    try {
      final db = await database;

      // Delete session (messages will be deleted automatically due to CASCADE)
      await db.delete(
        _chatSessionsTable,
        where: 'id = ?',
        whereArgs: [sessionId],
      );

      if (kDebugMode) {
        print('Chat Database: Deleted session $sessionId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Chat Database: Failed to delete session: $e');
      }
      rethrow;
    }
  }

  /// Insert a new chat message
  static Future<void> insertChatMessage(
      String chatId, ChatMessageModel message) async {
    try {
      final db = await database;
      await db.insert(
        _chatMessagesTable,
        {
          'id': message.id,
          'chat_id': chatId,
          'text': message.text,
          'is_user': message.isUser ? 1 : 0,
          'timestamp': message.timestamp.millisecondsSinceEpoch,
          'image_path': message.imagePath,
          'is_image_message': message.isImageMessage ? 1 : 0,
          'is_web_search_message': message.isWebSearchMessage ? 1 : 0,
          'search_query': message.searchQuery,
          'search_result_data': message.searchResultData,
          'has_user_feedback': message.hasUserFeedback != null
              ? (message.hasUserFeedback! ? 1 : 0)
              : null,
          'feedback_type': message.feedbackType,
          'feedback_id': message.feedbackId,
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      if (kDebugMode) {
        print('Chat Database: Inserted message ${message.id} to chat $chatId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Chat Database: Failed to insert message: $e');
      }
      rethrow;
    }
  }

  /// Get all messages for a chat session
  static Future<List<ChatMessageModel>> getChatMessages(String chatId) async {
    try {
      final db = await database;
      final List<Map<String, dynamic>> maps = await db.query(
        _chatMessagesTable,
        where: 'chat_id = ?',
        whereArgs: [chatId],
        orderBy: 'timestamp ASC',
      );

      return maps.map((messageData) {
        return ChatMessageModel(
          id: messageData['id'],
          text: messageData['text'],
          isUser: messageData['is_user'] == 1,
          timestamp:
              DateTime.fromMillisecondsSinceEpoch(messageData['timestamp']),
          imagePath: messageData['image_path'],
          isImageMessage: messageData['is_image_message'] == 1,
          isWebSearchMessage: messageData['is_web_search_message'] == 1,
          searchQuery: messageData['search_query'],
          searchResultData: messageData['search_result_data'],
          hasUserFeedback: messageData['has_user_feedback'] != null
              ? messageData['has_user_feedback'] == 1
              : null,
          feedbackType: messageData['feedback_type'],
          feedbackId: messageData['feedback_id'],
        );
      }).toList();
    } catch (e) {
      if (kDebugMode) {
        print('Chat Database: Failed to get messages for chat $chatId: $e');
      }
      return [];
    }
  }

  /// Update a chat message
  static Future<void> updateChatMessage(ChatMessageModel message) async {
    try {
      final db = await database;
      await db.update(
        _chatMessagesTable,
        {
          'text': message.text,
          'image_path': message.imagePath,
          'is_image_message': message.isImageMessage ? 1 : 0,
          'is_web_search_message': message.isWebSearchMessage ? 1 : 0,
          'search_query': message.searchQuery,
          'search_result_data': message.searchResultData,
          'has_user_feedback': message.hasUserFeedback != null
              ? (message.hasUserFeedback! ? 1 : 0)
              : null,
          'feedback_type': message.feedbackType,
          'feedback_id': message.feedbackId,
        },
        where: 'id = ?',
        whereArgs: [message.id],
      );

      if (kDebugMode) {
        print('Chat Database: Updated message ${message.id}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Chat Database: Failed to update message: $e');
      }
      rethrow;
    }
  }

  /// Clear all chat history
  static Future<void> clearAllHistory() async {
    try {
      final db = await database;

      await db.transaction((txn) async {
        await txn.delete(_chatMessagesTable);
        await txn.delete(_chatSessionsTable);
      });

      if (kDebugMode) {
        print('Chat Database: Cleared all chat history');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Chat Database: Failed to clear all history: $e');
      }
      rethrow;
    }
  }

  /// Close the database
  static Future<void> close() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
      if (kDebugMode) {
        print('Chat Database: Database closed');
      }
    }
  }

  /// Get database statistics
  static Future<Map<String, int>> getStatistics() async {
    try {
      final db = await database;

      final sessionCount = Sqflite.firstIntValue(
              await db.rawQuery('SELECT COUNT(*) FROM $_chatSessionsTable')) ??
          0;

      final messageCount = Sqflite.firstIntValue(
              await db.rawQuery('SELECT COUNT(*) FROM $_chatMessagesTable')) ??
          0;

      return {
        'sessions': sessionCount,
        'messages': messageCount,
      };
    } catch (e) {
      if (kDebugMode) {
        print('Chat Database: Failed to get statistics: $e');
      }
      return {'sessions': 0, 'messages': 0};
    }
  }
}
