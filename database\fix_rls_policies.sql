-- Fix infinite recursion in RLS policies
-- Run this in your Supabase SQL editor to fix the recursion error

-- First, drop the problematic policies
DROP POLICY IF EXISTS "Users can view participants" ON collaboration_participants;
DROP POLICY IF EXISTS "Users can join itineraries" ON collaboration_participants;
DROP POLICY IF EXISTS "Owners can manage participants" ON collaboration_participants;

-- Create fixed policies without recursion

-- 1. Users can view participants of public itineraries or itineraries they own
CREATE POLICY "Users can view participants of accessible itineraries" ON collaboration_participants
    FOR SELECT USING (
        itinerary_id IN (
            SELECT id FROM collaborative_itineraries 
            WHERE is_public = true OR owner_id = auth.uid()
        )
        OR user_id = auth.uid()  -- Users can always see their own participation
    );

-- 2. Users can join public itineraries (simplified)
CREATE POLICY "Users can join public itineraries" ON collaboration_participants
    FOR INSERT WITH CHECK (
        auth.uid() IS NOT NULL AND 
        user_id = auth.uid() AND
        itinerary_id IN (
            SELECT id FROM collaborative_itineraries 
            WHERE is_public = true
        )
    );

-- 3. Owners can manage participants of their itineraries
CREATE POLICY "Owners can manage their itinerary participants" ON collaboration_participants
    FOR ALL USING (
        itinerary_id IN (
            SELECT id FROM collaborative_itineraries 
            WHERE owner_id = auth.uid()
        )
    );

-- 4. Users can delete their own participation
CREATE POLICY "Users can leave itineraries" ON collaboration_participants
    FOR DELETE USING (user_id = auth.uid());

-- Also fix the activity logs policy if it has similar issues
DROP POLICY IF EXISTS "Users can view activity logs" ON itinerary_activity_logs;

CREATE POLICY "Users can view activity logs of accessible itineraries" ON itinerary_activity_logs
    FOR SELECT USING (
        itinerary_id IN (
            SELECT id FROM collaborative_itineraries 
            WHERE is_public = true OR owner_id = auth.uid()
        )
    );
