import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../services/ai_itinerary_parser.dart';

/// Widget to display tables with enhanced visual formatting
class EnhancedTableDisplay extends StatelessWidget {
  final ParsedTable table;
  final bool isCompact;

  const EnhancedTableDisplay({
    super.key,
    required this.table,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16),
      decoration: BoxDecoration(
        color: const Color(0xFFF7F9FC),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFFE2E8F0),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Table title
          if (table.title.isNotEmpty) ...[
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                color: Color(0xFF0D76FF),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Text(
                table.title,
                style: GoogleFonts.instrumentSans(
                  fontSize: isCompact ? 14 : 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
          ],

          // Table content
          Padding(
            padding: const EdgeInsets.all(16),
            child: _buildTable(),
          ),
        ],
      ),
    );
  }

  Widget _buildTable() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: DataTable(
        headingRowColor: MaterialStateProperty.all(
          const Color(0xFF0D76FF).withOpacity(0.1),
        ),
        dataRowColor: MaterialStateProperty.resolveWith<Color?>(
          (Set<MaterialState> states) {
            if (states.contains(MaterialState.selected)) {
              return const Color(0xFF0D76FF).withOpacity(0.1);
            }
            return null; // Use default value for other states and odd rows.
          },
        ),
        headingTextStyle: GoogleFonts.instrumentSans(
          fontSize: isCompact ? 12 : 14,
          fontWeight: FontWeight.bold,
          color: const Color(0xFF0D76FF),
        ),
        dataTextStyle: GoogleFonts.instrumentSans(
          fontSize: isCompact ? 11 : 13,
          color: const Color(0xFF2D3748),
          height: 1.4,
        ),
        columnSpacing: 24,
        horizontalMargin: 0,
        headingRowHeight: isCompact ? 40 : 48,
        dataRowHeight: isCompact ? 36 : 44,
        border: TableBorder.all(
          color: const Color(0xFFE2E8F0),
          width: 1,
          borderRadius: BorderRadius.circular(8),
        ),
        columns: table.headers.map((header) {
          return DataColumn(
            label: Expanded(
              child: Text(
                header,
                overflow: TextOverflow.ellipsis,
                maxLines: 2,
              ),
            ),
          );
        }).toList(),
        rows: table.rows.asMap().entries.map((entry) {
          final index = entry.key;
          final row = entry.value;
          
          return DataRow(
            color: MaterialStateProperty.all(
              index.isEven 
                ? Colors.white 
                : const Color(0xFFF7F9FC),
            ),
            cells: _buildDataCells(row),
          );
        }).toList(),
      ),
    );
  }

  List<DataCell> _buildDataCells(List<String> rowData) {
    final cells = <DataCell>[];
    
    for (int i = 0; i < table.headers.length; i++) {
      final cellData = i < rowData.length ? rowData[i] : '';
      
      cells.add(
        DataCell(
          Container(
            constraints: const BoxConstraints(maxWidth: 200),
            child: Text(
              cellData,
              overflow: TextOverflow.ellipsis,
              maxLines: 3,
              style: GoogleFonts.instrumentSans(
                fontSize: isCompact ? 11 : 13,
                color: const Color(0xFF2D3748),
                height: 1.4,
              ),
            ),
          ),
        ),
      );
    }
    
    return cells;
  }
}

/// Widget to display multiple tables
class MultiTableDisplay extends StatelessWidget {
  final List<ParsedTable> tables;
  final bool isCompact;

  const MultiTableDisplay({
    super.key,
    required this.tables,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: tables.map((table) {
        return EnhancedTableDisplay(
          table: table,
          isCompact: isCompact,
        );
      }).toList(),
    );
  }
}

/// Compact table widget for use in smaller spaces
class CompactTableDisplay extends StatelessWidget {
  final ParsedTable table;

  const CompactTableDisplay({
    super.key,
    required this.table,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        color: const Color(0xFFF7F9FC),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: const Color(0xFFE2E8F0),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Compact title
          if (table.title.isNotEmpty) ...[
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: const BoxDecoration(
                color: Color(0xFF0D76FF),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
              ),
              child: Text(
                table.title,
                style: GoogleFonts.instrumentSans(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
          ],

          // Compact table content
          Padding(
            padding: const EdgeInsets.all(8),
            child: _buildCompactTable(),
          ),
        ],
      ),
    );
  }

  Widget _buildCompactTable() {
    return Column(
      children: [
        // Headers
        Container(
          padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 8),
          decoration: BoxDecoration(
            color: const Color(0xFF0D76FF).withOpacity(0.1),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Row(
            children: table.headers.map((header) {
              return Expanded(
                child: Text(
                  header,
                  style: GoogleFonts.instrumentSans(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF0D76FF),
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              );
            }).toList(),
          ),
        ),
        
        const SizedBox(height: 4),
        
        // Rows
        ...table.rows.asMap().entries.map((entry) {
          final index = entry.key;
          final row = entry.value;
          
          return Container(
            padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
            decoration: BoxDecoration(
              color: index.isEven ? Colors.white : const Color(0xFFF7F9FC),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Row(
              children: List.generate(table.headers.length, (i) {
                final cellData = i < row.length ? row[i] : '';
                return Expanded(
                  child: Text(
                    cellData,
                    style: GoogleFonts.instrumentSans(
                      fontSize: 9,
                      color: const Color(0xFF2D3748),
                      height: 1.3,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 2,
                  ),
                );
              }),
            ),
          );
        }).toList(),
      ],
    );
  }
}
