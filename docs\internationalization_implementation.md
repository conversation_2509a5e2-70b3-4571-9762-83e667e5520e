# Internationalization (i18n) Implementation Guide

## Overview

This document describes the comprehensive internationalization (i18n) system implemented for the TripWiseGo Flutter app, supporting 14 languages with persistent language preferences and RTL support.

## Supported Languages

The app supports the following 14 languages:

| Language Code | Language Name | Native Name | Country | RTL Support |
|---------------|---------------|-------------|---------|-------------|
| `en` | English | English | US | No |
| `fr` | French | Français | FR | No |
| `it` | Italian | Italiano | IT | No |
| `zh` | Chinese (Mandarin) | 中文 | CN | No |
| `ja` | Japanese | 日本語 | JP | No |
| `ko` | Korean | 한국어 | KR | No |
| `id` | Indonesian | Bahasa Indonesia | ID | No |
| `tl` | Filipino (Tagalog) | Filipino | PH | No |
| `th` | Thai | ไทย | TH | No |
| `ar` | Arabic | العربية | SA | Yes |
| `hi` | Hindi | हिन्दी | IN | No |
| `es` | Spanish | Español | ES | No |
| `ru` | Russian | Русский | RU | No |
| `pt` | Portuguese | Português | PT | No |

## Architecture

### Core Components

1. **LocalizationService** (`lib/services/localization_service.dart`)
   - Manages language preferences
   - Handles persistence via SharedPreferences and Supabase
   - Provides RTL detection and language metadata

2. **Language Picker Widget** (`lib/widgets/language_picker.dart`)
   - Interactive language selection interface
   - Available as full screen or bottom sheet
   - Displays native language names with flags

3. **ARB Files** (`lib/l10n/app_*.arb`)
   - Translation files for each supported language
   - Structured with descriptions and placeholders
   - Generated localization classes via `flutter gen-l10n`

### File Structure

```
lib/
├── l10n/
│   ├── app_en.arb          # English (template)
│   ├── app_fr.arb          # French
│   ├── app_es.arb          # Spanish
│   ├── app_ar.arb          # Arabic (RTL)
│   ├── app_zh.arb          # Chinese
│   ├── app_ja.arb          # Japanese
│   ├── app_ko.arb          # Korean
│   ├── app_id.arb          # Indonesian
│   ├── app_tl.arb          # Filipino
│   ├── app_th.arb          # Thai
│   ├── app_hi.arb          # Hindi
│   ├── app_ru.arb          # Russian
│   └── app_pt.arb          # Portuguese
├── generated/l10n/
│   ├── app_localizations.dart
│   └── app_localizations_*.dart
├── services/
│   └── localization_service.dart
└── widgets/
    └── language_picker.dart
```

## Implementation Details

### 1. Dependencies

Added to `pubspec.yaml`:
```yaml
dependencies:
  flutter_localizations:
    sdk: flutter
  intl: ^0.18.1

flutter:
  generate: true
```

### 2. Configuration

`l10n.yaml`:
```yaml
arb-dir: lib/l10n
template-arb-file: app_en.arb
output-localization-file: app_localizations.dart
output-class: AppLocalizations
output-dir: lib/generated/l10n
nullable-getter: false
synthetic-package: false
```

### 3. Main App Configuration

`lib/main.dart`:
```dart
MaterialApp(
  locale: LocalizationService().currentLocale,
  localizationsDelegates: [
    AppLocalizations.delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
  ],
  supportedLocales: LocalizationService.supportedLocales,
  builder: (context, child) {
    final locale = Localizations.localeOf(context);
    final isRTL = LocalizationService.isRTL(locale.languageCode);
    return Directionality(
      textDirection: isRTL ? TextDirection.rtl : TextDirection.ltr,
      child: child!,
    );
  },
  // ...
)
```

## Usage

### 1. Accessing Localized Strings

```dart
import '../generated/l10n/app_localizations.dart';

// In widget build method
Text(AppLocalizations.of(context).welcome)
Text(AppLocalizations.of(context).failedToUpdateProfile(error.toString()))
```

### 2. Language Selection

```dart
import '../widgets/language_picker.dart';

// Show as bottom sheet
showLanguagePickerBottomSheet(
  context,
  onLanguageChanged: (languageCode) {
    // Language change handled automatically
  },
);

// Show as full screen
Navigator.push(
  context,
  MaterialPageRoute(builder: (context) => const LanguagePicker()),
);
```

### 3. Language Service

```dart
import '../services/localization_service.dart';

// Get current language
final currentLang = LocalizationService().currentLanguageCode;

// Change language
await LocalizationService().setLanguage('fr');

// Check RTL
final isRTL = LocalizationService.isRTL('ar');

// Get native name
final nativeName = LocalizationService.getNativeName('zh');
```

## Persistence

Language preferences are stored in two locations:

1. **SharedPreferences** - Local device storage for immediate access
2. **Supabase User Metadata** - Cloud storage for authenticated users

### Storage Keys

- SharedPreferences: `selected_language`, `selected_locale`
- Supabase: `preferred_language` in user metadata

## RTL Support

Arabic language has full RTL (Right-to-Left) support:

- Automatic text direction detection
- Layout mirroring via `Directionality` widget
- Proper text alignment and reading order

## Testing

Comprehensive test suite in `test/i18n_test.dart`:

- Language support verification
- RTL detection testing
- Widget rendering tests
- Localization content validation

Run tests:
```bash
flutter test test/i18n_test.dart
```

## Adding New Languages

1. **Create ARB file**: `lib/l10n/app_[code].arb`
2. **Add to LocalizationService**: Update `supportedLanguages` map
3. **Regenerate**: Run `flutter gen-l10n`
4. **Test**: Verify in language picker and app

### ARB File Template

```json
{
  "@@locale": "xx",
  "appTitle": "TripWiseGo",
  "welcome": "Welcome Translation",
  "home": "Home Translation",
  // ... other strings
}
```

## Adding New Strings

1. **Add to template**: Update `lib/l10n/app_en.arb`
2. **Add translations**: Update all language ARB files
3. **Regenerate**: Run `flutter gen-l10n`
4. **Use in code**: `AppLocalizations.of(context).newString`

### String with Placeholders

```json
{
  "failedToUpdateProfile": "Failed to update profile: {error}",
  "@failedToUpdateProfile": {
    "description": "Error message when profile update fails",
    "placeholders": {
      "error": {
        "type": "String",
        "description": "The error message"
      }
    }
  }
}
```

## Performance Considerations

- Lazy loading of localization delegates
- Efficient locale switching without app restart
- Minimal memory footprint for unused languages
- Cached language preferences for fast startup

## Best Practices

1. **Consistent Naming**: Use descriptive, consistent key names
2. **Context Descriptions**: Add `@key` descriptions for translators
3. **Placeholder Types**: Specify types for dynamic content
4. **RTL Testing**: Test Arabic layout thoroughly
5. **Fallback Handling**: English as default fallback language

## Troubleshooting

### Common Issues

1. **Missing Translations**: Run `flutter gen-l10n` to see untranslated messages
2. **Build Errors**: Ensure all ARB files have matching keys
3. **RTL Layout**: Check `Directionality` widget implementation
4. **Persistence Issues**: Verify SharedPreferences and Supabase integration

### Debug Commands

```bash
# Generate localization files
flutter gen-l10n

# Check for missing translations
flutter gen-l10n --untranslated-messages-file=missing.txt

# Analyze code
flutter analyze lib/services/localization_service.dart

# Run tests
flutter test test/i18n_test.dart
```

## Future Enhancements

1. **Pluralization**: Add support for plural forms
2. **Date/Time Formatting**: Locale-specific formatting
3. **Number Formatting**: Currency and number localization
4. **Dynamic Loading**: Load translations from remote server
5. **Translation Management**: Integration with translation services
