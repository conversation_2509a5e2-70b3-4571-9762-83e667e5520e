// Test file to verify the enhanced itinerary detection logic
import 'lib/services/ai_itinerary_parser.dart';

void main() {
  print('Testing Enhanced Itinerary Detection Logic');
  print('=' * 50);
  
  // Test cases that should NOT trigger itinerary detection
  final nonItineraryResponses = [
    "What's the weather like in Paris? Paris typically has mild weather in spring with temperatures around 15-20°C. You might want to visit the Eiffel Tower and explore the city.",
    "Tell me about Tokyo attractions. Tokyo has many amazing places to visit including temples, museums, and great restaurants. The city offers diverse experiences.",
    "The best restaurants in Rome include authentic Italian cuisine. You should try the local pasta dishes and visit some historic sites.",
    "When is the best time to visit Thailand? The weather is great from November to March. You can explore beaches and temples.",
    "How much does it cost to travel to Japan? Budget around \$100-150 per day. You can visit Tokyo, see temples, and try local food.",
  ];
  
  // Test cases that SHOULD trigger itinerary detection
  final itineraryResponses = [
    """Here's your complete 3-day itinerary for Tokyo:

Day 1: Arrival and Shibuya
Morning: Arrive at Narita Airport, take train to Shibuya
Afternoon: Visit Shibuya Crossing and Hachiko Statue
Evening: Dinner in Shibuya, explore nightlife

Day 2: Traditional Tokyo
Morning: Visit <PERSON>-ji Temple in Asakusa
Afternoon: Explore Tokyo National Museum
Evening: Traditional dinner in Ginza

Day 3: Modern Tokyo
Morning: Visit Tokyo Skytree
Afternoon: Shopping in Harajuku
Evening: Departure preparation""",
    
    """Complete 5-day travel plan for Paris:

Day 1: Arrival
- Morning: Arrive at Charles de Gaulle Airport
- Afternoon: Check into hotel, visit Eiffel Tower
- Evening: Seine River cruise

Day 2: Museums and Culture
- Morning: Louvre Museum
- Afternoon: Notre-Dame Cathedral
- Evening: Dinner in Latin Quarter""",
  ];
  
  // Test user intent analysis
  final itineraryRequests = [
    "Create an itinerary for 5 days in Tokyo",
    "Plan my trip to Paris for 3 days",
    "Make a travel plan for my vacation to Rome",
    "What should I do for 4 days in London?",
    "Plan a 7-day trip to Thailand",
  ];
  
  final generalQuestions = [
    "What's the weather like in Paris?",
    "Tell me about Tokyo attractions",
    "What are the best restaurants in Rome?",
    "How much does it cost to travel to Japan?",
    "When is the best time to visit Thailand?",
  ];
  
  print('\n1. Testing Non-Itinerary Responses (should be FALSE):');
  for (int i = 0; i < nonItineraryResponses.length; i++) {
    final result = AIItineraryParser.containsItinerary(nonItineraryResponses[i]);
    print('Test ${i + 1}: ${result ? "FAIL ❌" : "PASS ✅"}');
    if (result) {
      print('   Response: ${nonItineraryResponses[i].substring(0, 50)}...');
    }
  }
  
  print('\n2. Testing Itinerary Responses (should be TRUE):');
  for (int i = 0; i < itineraryResponses.length; i++) {
    final result = AIItineraryParser.containsItinerary(itineraryResponses[i]);
    print('Test ${i + 1}: ${result ? "PASS ✅" : "FAIL ❌"}');
    if (!result) {
      print('   Response: ${itineraryResponses[i].substring(0, 50)}...');
    }
  }
  
  print('\n3. Testing User Intent - Itinerary Requests (should be TRUE):');
  for (int i = 0; i < itineraryRequests.length; i++) {
    final result = AIItineraryParser.userRequestedItinerary(itineraryRequests[i]);
    print('Test ${i + 1}: ${result ? "PASS ✅" : "FAIL ❌"} - "${itineraryRequests[i]}"');
  }
  
  print('\n4. Testing User Intent - General Questions (should be TRUE):');
  for (int i = 0; i < generalQuestions.length; i++) {
    final result = AIItineraryParser.isGeneralTravelQuestion(generalQuestions[i]);
    print('Test ${i + 1}: ${result ? "PASS ✅" : "FAIL ❌"} - "${generalQuestions[i]}"');
  }
  
  print('\n' + '=' * 50);
  print('Testing Complete!');
}
