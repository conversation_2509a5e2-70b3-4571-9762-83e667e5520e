-- Add activity-specific commenting support
-- Run this in your Supabase SQL editor to add activity commenting functionality

-- Add activity_id column to itinerary_comments table
ALTER TABLE itinerary_comments 
ADD COLUMN activity_id TEXT;

-- Add activity_day column to track which day the activity belongs to
ALTER TABLE itinerary_comments 
ADD COLUMN activity_day INTEGER;

-- Add comment_type to distinguish between itinerary and activity comments
ALTER TABLE itinerary_comments 
ADD COLUMN comment_type VARCHAR(20) DEFAULT 'itinerary' CHECK (comment_type IN ('itinerary', 'activity'));

-- Create index for activity comments
CREATE INDEX IF NOT EXISTS idx_itinerary_comments_activity ON itinerary_comments(activity_id) WHERE activity_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_itinerary_comments_type ON itinerary_comments(comment_type);

-- Update existing comments to be itinerary-level comments
UPDATE itinerary_comments 
SET comment_type = 'itinerary' 
WHERE comment_type IS NULL;

-- Add constraint to ensure activity comments have activity_id (with error handling)
DO $$
BEGIN
    BEGIN
        ALTER TABLE itinerary_comments
        ADD CONSTRAINT check_activity_comment_has_activity_id
        CHECK (
            (comment_type = 'itinerary' AND activity_id IS NULL) OR
            (comment_type = 'activity' AND activity_id IS NOT NULL)
        );
    EXCEPTION
        WHEN duplicate_object THEN
            -- Constraint already exists, ignore
            NULL;
    END;
END $$;

-- Update RLS policies to handle activity comments
-- The existing policies will work for activity comments since they're still tied to itinerary_id

-- Add helpful comments to the schema
COMMENT ON COLUMN itinerary_comments.activity_id IS 'Activity identifier for activity-specific comments. NULL for itinerary-level comments.';
COMMENT ON COLUMN itinerary_comments.activity_day IS 'Day number (1-based) for activity comments. NULL for itinerary-level comments.';
COMMENT ON COLUMN itinerary_comments.comment_type IS 'Type of comment: itinerary (general) or activity (specific to an activity).';
