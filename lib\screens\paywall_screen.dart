import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../generated/l10n/app_localizations.dart';
import '../models/subscription.dart';
import '../services/subscription_service.dart';
import '../screens/subscription_active_screen.dart';

class PaywallScreen extends StatefulWidget {
  const PaywallScreen({super.key});

  @override
  State<PaywallScreen> createState() => _PaywallScreenState();
}

class _PaywallScreenState extends State<PaywallScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeAnimationController;
  late Animation<double> _fadeAnimation;

  late AnimationController _slideAnimationController;
  late Animation<Offset> _slideAnimation;

  int _selectedPlanIndex = 0;
  bool _isBasicSelected = true;
  bool _isLoading = false;

  final List<SubscriptionTier> _subscriptionTiers =
      SubscriptionTier.availableTiers;

  @override
  void initState() {
    super.initState();

    // Initialize animations
    _fadeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
          parent: _fadeAnimationController, curve: Curves.easeInOut),
    );

    _slideAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
        parent: _slideAnimationController, curve: Curves.easeInOut));

    // Start animations
    _fadeAnimationController.forward();
    _slideAnimationController.forward();

    // Set default selected plan to the popular one (5 months)
    _selectedPlanIndex =
        _subscriptionTiers.indexWhere((tier) => tier.isPopular);
    if (_selectedPlanIndex == -1) _selectedPlanIndex = 0;
  }

  @override
  void dispose() {
    _fadeAnimationController.dispose();
    _slideAnimationController.dispose();
    super.dispose();
  }

  void _selectPlan(int index) {
    setState(() {
      _selectedPlanIndex = index;
    });
  }

  Future<void> _subscribeToPlan() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final selectedTier = _subscriptionTiers[_selectedPlanIndex];

      // Use RevenueCat for real purchases
      final success = await SubscriptionService.subscribeToPlan(selectedTier);

      if (success && mounted) {
        // Navigate to subscription active screen
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => const SubscriptionActiveScreen(),
          ),
        );
      } else if (mounted) {
        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to subscribe. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        String errorMessage = 'Purchase failed. Please try again.';

        // Handle specific RevenueCat errors
        if (e.toString().contains('UserCancelledError')) {
          errorMessage = 'Purchase was cancelled.';
        } else if (e.toString().contains('PaymentPendingError')) {
          errorMessage = 'Payment is pending. Please wait.';
        } else if (e
            .toString()
            .contains('ProductNotAvailableForPurchaseError')) {
          errorMessage = 'This subscription is not available.';
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F9FC),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: SafeArea(
            child: Column(
              children: [
                // Header with close button
                _buildHeader(),

                // Content
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.symmetric(horizontal: 24),
                    child: Column(
                      children: [
                        const SizedBox(height: 20),

                        // App icon
                        _buildAppIcon(),

                        const SizedBox(height: 32),

                        // Plan selector tabs
                        _buildPlanSelector(),

                        const SizedBox(height: 24),

                        // Features list
                        _buildFeaturesList(),

                        const SizedBox(height: 32),

                        // Subscription tiers
                        _buildSubscriptionTiers(),

                        const SizedBox(height: 24),

                        // Continue button
                        _buildContinueButton(),

                        const SizedBox(height: 16),

                        // Terms and privacy
                        _buildTermsAndPrivacy(),

                        const SizedBox(height: 16),

                        // Restore purchases button
                        _buildRestorePurchasesButton(),

                        const SizedBox(height: 24),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(
              Icons.close,
              color: Color(0xFF2D3748),
              size: 24,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAppIcon() {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        color: const Color(0xFF0D76FF),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF0D76FF).withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: const Icon(
        Icons.location_on,
        color: Colors.white,
        size: 40,
      ),
    );
  }

  Widget _buildPlanSelector() {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFFE2E8F0),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _isBasicSelected = true;
                });
              },
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: _isBasicSelected
                      ? const Color(0xFF0D76FF)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  AppLocalizations.of(context).basic,
                  textAlign: TextAlign.center,
                  style: GoogleFonts.instrumentSans(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: _isBasicSelected
                        ? Colors.white
                        : const Color(0xFF718096),
                  ),
                ),
              ),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _isBasicSelected = false;
                });
              },
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: !_isBasicSelected
                      ? const Color(0xFF0D76FF)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  AppLocalizations.of(context).premium,
                  textAlign: TextAlign.center,
                  style: GoogleFonts.instrumentSans(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: !_isBasicSelected
                        ? Colors.white
                        : const Color(0xFF718096),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeaturesList() {
    final features = _subscriptionTiers.first.features;

    return Column(
      children: features.map((feature) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 6),
          child: Row(
            children: [
              Container(
                width: 20,
                height: 20,
                decoration: const BoxDecoration(
                  color: Color(0xFF0D76FF),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.check,
                  color: Colors.white,
                  size: 14,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  feature,
                  style: GoogleFonts.instrumentSans(
                    fontSize: 14,
                    color: const Color(0xFF2D3748),
                    height: 1.4,
                  ),
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildSubscriptionTiers() {
    return Column(
      children: _subscriptionTiers.asMap().entries.map((entry) {
        final index = entry.key;
        final tier = entry.value;
        final isSelected = index == _selectedPlanIndex;

        return GestureDetector(
          onTap: () => _selectPlan(index),
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            margin: const EdgeInsets.only(bottom: 12),
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: isSelected
                    ? const Color(0xFF0D76FF)
                    : const Color(0xFFE2E8F0),
                width: isSelected ? 2 : 1,
              ),
              boxShadow: isSelected
                  ? [
                      BoxShadow(
                        color: const Color(0xFF0D76FF).withOpacity(0.1),
                        blurRadius: 20,
                        offset: const Offset(0, 8),
                      ),
                    ]
                  : [],
            ),
            child: _buildTierContent(tier, isSelected),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildTierContent(SubscriptionTier tier, bool isSelected) {
    return Stack(
      children: [
        Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    tier.name,
                    style: GoogleFonts.instrumentSans(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: const Color(0xFF2D3748),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${tier.freeTrialDays}-day free trial',
                    style: GoogleFonts.instrumentSans(
                      fontSize: 12,
                      color: const Color(0xFF718096),
                    ),
                  ),
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '\$${tier.price.toStringAsFixed(2)}',
                  style: GoogleFonts.instrumentSans(
                    fontSize: 24,
                    fontWeight: FontWeight.w700,
                    color: const Color(0xFF2D3748),
                  ),
                ),
                Text(
                  tier.billingCycle,
                  style: GoogleFonts.instrumentSans(
                    fontSize: 12,
                    color: const Color(0xFF718096),
                  ),
                ),
              ],
            ),
          ],
        ),
        if (tier.isPopular)
          Positioned(
            top: -8,
            right: -8,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: const Color(0xFF0D76FF),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                tier.discountText ?? 'POPULAR',
                style: GoogleFonts.instrumentSans(
                  fontSize: 10,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildContinueButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _subscribeToPlan,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF0D76FF),
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 0,
        ),
        child: _isLoading
            ? const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    AppLocalizations.of(context).continueButton,
                    style: GoogleFonts.instrumentSans(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(width: 8),
                  const Icon(
                    Icons.arrow_forward,
                    size: 20,
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildTermsAndPrivacy() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        TextButton(
          onPressed: () {
            // Navigate to privacy policy
          },
          child: Text(
            'Privacy Policy', // AppLocalizations.of(context).privacyPolicy,
            style: GoogleFonts.instrumentSans(
              fontSize: 14,
              color: const Color(0xFF718096),
              decoration: TextDecoration.underline,
            ),
          ),
        ),
        TextButton(
          onPressed: () {
            // Navigate to terms and conditions
          },
          child: Text(
            'Terms and Conditions', // AppLocalizations.of(context).termsAndConditions,
            style: GoogleFonts.instrumentSans(
              fontSize: 14,
              color: const Color(0xFF718096),
              decoration: TextDecoration.underline,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildRestorePurchasesButton() {
    return TextButton(
      onPressed: _isLoading ? null : _handleRestorePurchases,
      style: TextButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: 12),
      ),
      child: Text(
        'Restore Purchases',
        style: GoogleFonts.instrumentSans(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: _isLoading ? const Color(0xFF718096) : const Color(0xFF0D76FF),
        ),
      ),
    );
  }

  Future<void> _handleRestorePurchases() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final success = await SubscriptionService.restorePurchases();

      if (success && mounted) {
        // Navigate to subscription active screen
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => const SubscriptionActiveScreen(),
          ),
        );
      } else if (mounted) {
        // Show message that no purchases were found
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('No previous purchases found.'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to restore purchases: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
