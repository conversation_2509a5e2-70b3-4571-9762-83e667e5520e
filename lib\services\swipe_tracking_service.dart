import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../services/auth_service.dart';
import '../services/network_aware_supabase.dart';
import '../services/subscription_service.dart';

class SwipeTrackingService {
  static const String _swipeCountKey = 'daily_swipe_count';
  static const String _lastSwipeDateKey = 'last_swipe_date';
  static const String _swipeDataTableName = 'user_swipe_data';

  // Swipe limits
  static const int _freeUserDailyLimit = 170;
  static const int _subscribedUserDailyLimit = 100;

  static int _currentSwipeCount = 0;
  static String _lastSwipeDate = '';
  static bool _isInitialized = false;

  // Network-aware wrapper
  static final NetworkAwareSupabase _networkWrapper = NetworkAwareSupabase();

  /// Initialize the swipe tracking service
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadSwipeDataFromStorage();

      // Try to sync from Supabase using network wrapper
      await _networkWrapper.executeSilently(
        () => _syncSwipeDataFromSupabase(),
        operationName: 'swipe_data_sync',
      );

      await _checkAndResetDailyCount();
      _isInitialized = true;

      if (kDebugMode) {
        print('Swipe Tracking Service: Initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Swipe Tracking Service: Initialization error - $e');
      }
      // Mark as initialized even if there are errors to prevent blocking the app
      _isInitialized = true;
    }
  }

  /// Ensure service is initialized
  static Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  /// Get current swipe count for today
  static Future<int> getCurrentSwipeCount() async {
    await _ensureInitialized();
    await _checkAndResetDailyCount();
    return _currentSwipeCount;
  }

  /// Get remaining swipes for today
  static Future<int> getRemainingSwipes() async {
    await _ensureInitialized();
    await _checkAndResetDailyCount();

    final limit = await _getDailySwipeLimit();
    return (limit - _currentSwipeCount).clamp(0, limit);
  }

  /// Check if user can swipe (has remaining swipes)
  static Future<bool> canSwipe() async {
    final remaining = await getRemainingSwipes();
    return remaining > 0;
  }

  /// Record a swipe and return if it was successful
  static Future<bool> recordSwipe() async {
    await _ensureInitialized();
    await _checkAndResetDailyCount();

    final canPerformSwipe = await canSwipe();
    if (!canPerformSwipe) {
      if (kDebugMode) {
        print('Swipe Tracking Service: Daily limit reached');
      }
      return false;
    }

    _currentSwipeCount++;
    await _saveSwipeDataToStorage();

    // Save to Supabase using network wrapper
    await _networkWrapper.executeSilently(
      () => _saveSwipeDataToSupabase(),
      operationName: 'swipe_data_save',
    );

    if (kDebugMode) {
      print(
          'Swipe Tracking Service: Swipe recorded. Count: $_currentSwipeCount');
    }

    return true;
  }

  /// Get daily swipe limit based on subscription status
  static Future<int> _getDailySwipeLimit() async {
    final hasSubscription = await SubscriptionService.hasActiveSubscription();
    return hasSubscription ? _subscribedUserDailyLimit : _freeUserDailyLimit;
  }

  /// Check if it's a new day and reset count if needed
  static Future<void> _checkAndResetDailyCount() async {
    final today = DateTime.now().toIso8601String().split('T')[0];

    if (_lastSwipeDate != today) {
      _currentSwipeCount = 0;
      _lastSwipeDate = today;
      await _saveSwipeDataToStorage();

      // Save to Supabase using network wrapper
      await _networkWrapper.executeSilently(
        () => _saveSwipeDataToSupabase(),
        operationName: 'daily_reset_save',
      );

      if (kDebugMode) {
        print('Swipe Tracking Service: Reset daily count for new day');
      }
    }
  }

  /// Load swipe data from local storage
  static Future<void> _loadSwipeDataFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _currentSwipeCount = prefs.getInt(_swipeCountKey) ?? 0;
      _lastSwipeDate = prefs.getString(_lastSwipeDateKey) ?? '';

      if (kDebugMode) {
        print(
            'Swipe Tracking Service: Loaded from storage - Count: $_currentSwipeCount, Date: $_lastSwipeDate');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Swipe Tracking Service: Failed to load from storage - $e');
      }
    }
  }

  /// Save swipe data to local storage
  static Future<void> _saveSwipeDataToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_swipeCountKey, _currentSwipeCount);
      await prefs.setString(_lastSwipeDateKey, _lastSwipeDate);

      if (kDebugMode) {
        print('Swipe Tracking Service: Saved to storage');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Swipe Tracking Service: Failed to save to storage - $e');
      }
    }
  }

  /// Sync swipe data from Supabase
  static Future<void> _syncSwipeDataFromSupabase() async {
    try {
      final userId = AuthService.currentUser?.id;
      if (userId == null) return;

      final today = DateTime.now().toIso8601String().split('T')[0];
      final response = await Supabase.instance.client
          .from(_swipeDataTableName)
          .select()
          .eq('user_id', userId)
          .eq('date', today)
          .limit(1);

      if (response.isNotEmpty) {
        final swipeData = response.first;
        _currentSwipeCount = swipeData['swipe_count'] ?? 0;
        _lastSwipeDate = swipeData['date'] ?? today;
        await _saveSwipeDataToStorage();

        if (kDebugMode) {
          print('Swipe Tracking Service: Synced from Supabase');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Swipe Tracking Service: Failed to sync from Supabase - $e');
      }
    }
  }

  /// Save swipe data to Supabase
  static Future<void> _saveSwipeDataToSupabase() async {
    try {
      final userId = AuthService.currentUser?.id;
      if (userId == null) return;

      final swipeData = {
        'user_id': userId,
        'date': _lastSwipeDate,
        'swipe_count': _currentSwipeCount,
        'updated_at': DateTime.now().toIso8601String(),
      };

      await Supabase.instance.client
          .from(_swipeDataTableName)
          .upsert(swipeData);

      if (kDebugMode) {
        print('Swipe Tracking Service: Saved to Supabase');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Swipe Tracking Service: Failed to save to Supabase - $e');
      }
    }
  }

  /// Get swipe statistics for debugging/analytics
  static Future<Map<String, dynamic>> getSwipeStats() async {
    await _ensureInitialized();
    await _checkAndResetDailyCount();

    final limit = await _getDailySwipeLimit();
    final remaining = await getRemainingSwipes();
    final hasSubscription = await SubscriptionService.hasActiveSubscription();

    return {
      'current_count': _currentSwipeCount,
      'daily_limit': limit,
      'remaining': remaining,
      'has_subscription': hasSubscription,
      'last_swipe_date': _lastSwipeDate,
    };
  }

  /// Reset swipe count (for testing purposes)
  static Future<void> resetSwipeCount() async {
    _currentSwipeCount = 0;
    _lastSwipeDate = DateTime.now().toIso8601String().split('T')[0];
    await _saveSwipeDataToStorage();

    // Save to Supabase using network wrapper
    await _networkWrapper.executeSilently(
      () => _saveSwipeDataToSupabase(),
      operationName: 'reset_count_save',
    );

    if (kDebugMode) {
      print('Swipe Tracking Service: Reset swipe count');
    }
  }
}
