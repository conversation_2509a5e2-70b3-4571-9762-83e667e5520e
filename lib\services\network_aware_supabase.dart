import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Network-aware wrapper for Supabase operations to handle network errors gracefully
class NetworkAwareSupabase {
  static final NetworkAwareSupabase _instance = NetworkAwareSupabase._internal();
  factory NetworkAwareSupabase() => _instance;
  NetworkAwareSupabase._internal();

  final Connectivity _connectivity = Connectivity();
  List<ConnectivityResult> _currentConnectivity = [ConnectivityResult.none];
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  
  // Track network-related errors to avoid spam
  final Set<String> _recentNetworkErrors = <String>{};
  Timer? _errorCleanupTimer;

  /// Initialize the network-aware wrapper
  Future<void> initialize() async {
    try {
      _currentConnectivity = await _connectivity.checkConnectivity();
      
      _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
        (List<ConnectivityResult> result) {
          _currentConnectivity = result;
          if (kDebugMode) {
            print('NetworkAwareSupabase: Connectivity changed to $result');
          }
        },
        onError: (error) {
          if (kDebugMode) {
            print('NetworkAwareSupabase: Connectivity monitoring error: $error');
          }
        },
        cancelOnError: false,
      );

      // Set up periodic cleanup of error tracking
      _errorCleanupTimer = Timer.periodic(
        const Duration(minutes: 5),
        (_) => _recentNetworkErrors.clear(),
      );

      if (kDebugMode) {
        print('NetworkAwareSupabase: Initialized successfully');
      }
    } catch (error) {
      if (kDebugMode) {
        print('NetworkAwareSupabase: Initialization error: $error');
      }
    }
  }

  /// Check if device has internet connectivity
  bool get hasConnectivity {
    return _currentConnectivity.any((result) => 
      result != ConnectivityResult.none
    );
  }

  /// Check if an error is network-related
  bool isNetworkError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    return errorString.contains('socketexception') ||
        errorString.contains('no address associated with hostname') ||
        errorString.contains('failed host lookup') ||
        errorString.contains('network error') ||
        errorString.contains('connection refused') ||
        errorString.contains('timeout') ||
        errorString.contains('authretryablefetchexception') ||
        errorString.contains('clientexception');
  }

  /// Execute a Supabase operation with network error handling
  Future<T?> executeWithNetworkHandling<T>(
    Future<T> Function() operation, {
    String? operationName,
    T? fallbackValue,
    bool logErrors = true,
  }) async {
    try {
      // Check connectivity before attempting operation
      if (!hasConnectivity) {
        if (kDebugMode && logErrors) {
          print('NetworkAwareSupabase: No connectivity, skipping ${operationName ?? 'operation'}');
        }
        return fallbackValue;
      }

      return await operation();
    } catch (error) {
      if (isNetworkError(error)) {
        // Handle network errors gracefully
        final errorKey = '${operationName ?? 'unknown'}_${error.runtimeType}';
        
        // Only log each type of network error once per cleanup period
        if (!_recentNetworkErrors.contains(errorKey)) {
          _recentNetworkErrors.add(errorKey);
          if (kDebugMode && logErrors) {
            print('NetworkAwareSupabase: Network error in ${operationName ?? 'operation'}: $error');
          }
        }
        
        return fallbackValue;
      } else {
        // Re-throw non-network errors
        if (kDebugMode && logErrors) {
          print('NetworkAwareSupabase: Non-network error in ${operationName ?? 'operation'}: $error');
        }
        rethrow;
      }
    }
  }

  /// Execute a Supabase operation without throwing on network errors
  Future<void> executeSilently(
    Future<void> Function() operation, {
    String? operationName,
  }) async {
    try {
      await executeWithNetworkHandling(
        operation,
        operationName: operationName,
        logErrors: false,
      );
    } catch (error) {
      // Silently handle all errors
      if (kDebugMode) {
        print('NetworkAwareSupabase: Silent operation ${operationName ?? 'unknown'} failed: $error');
      }
    }
  }

  /// Wrap auth state changes to handle network errors
  Stream<AuthState> wrapAuthStateChanges(Stream<AuthState> authStream) {
    return authStream.handleError(
      (error) {
        if (isNetworkError(error)) {
          if (kDebugMode) {
            print('NetworkAwareSupabase: Auth state change network error (handled): $error');
          }
          // Don't propagate network errors
          return;
        }
        // Re-throw non-network errors
        throw error;
      },
    );
  }

  /// Dispose resources
  void dispose() {
    _connectivitySubscription?.cancel();
    _errorCleanupTimer?.cancel();
    _recentNetworkErrors.clear();
  }
}
