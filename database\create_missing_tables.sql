-- Create missing tables for collaborative itineraries
-- Run this in your Supabase SQL editor if you get 404 errors

-- 1. Create collaboration_participants table
CREATE TABLE IF NOT EXISTS collaboration_participants (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    itinerary_id UUID REFERENCES collaborative_itineraries(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    role VARCHAR(20) DEFAULT 'viewer' CHECK (role IN ('owner', 'editor', 'viewer')),
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure unique participant per itinerary
    UNIQUE(itinerary_id, user_id)
);

-- 2. Create itinerary_activity_logs table
CREATE TABLE IF NOT EXISTS itinerary_activity_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    itinerary_id UUID REFERENCES collaborative_itineraries(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    action_type VARCHAR(50) NOT NULL, -- 'created', 'updated', 'deleted', 'joined', 'commented'
    action_details JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_collaboration_participants_itinerary ON collaboration_participants(itinerary_id);
CREATE INDEX IF NOT EXISTS idx_collaboration_participants_user ON collaboration_participants(user_id);
CREATE INDEX IF NOT EXISTS idx_itinerary_activity_logs_itinerary ON itinerary_activity_logs(itinerary_id);

-- Enable Row Level Security
ALTER TABLE collaboration_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE itinerary_activity_logs ENABLE ROW LEVEL SECURITY;

-- RLS Policies for collaboration_participants
CREATE POLICY "Users can view participants" ON collaboration_participants
    FOR SELECT USING (
        itinerary_id IN (
            SELECT id FROM collaborative_itineraries 
            WHERE is_public = true OR owner_id = auth.uid() OR 
            id IN (SELECT itinerary_id FROM collaboration_participants WHERE user_id = auth.uid())
        )
    );

CREATE POLICY "Users can join itineraries" ON collaboration_participants
    FOR INSERT WITH CHECK (
        auth.uid() IS NOT NULL AND 
        user_id = auth.uid() AND
        itinerary_id IN (SELECT id FROM collaborative_itineraries WHERE is_public = true)
    );

CREATE POLICY "Owners can manage participants" ON collaboration_participants
    FOR ALL USING (
        itinerary_id IN (SELECT id FROM collaborative_itineraries WHERE owner_id = auth.uid())
    );

-- RLS Policies for itinerary_activity_logs
CREATE POLICY "Users can view activity logs" ON itinerary_activity_logs
    FOR SELECT USING (
        itinerary_id IN (
            SELECT id FROM collaborative_itineraries 
            WHERE is_public = true OR owner_id = auth.uid() OR 
            id IN (SELECT itinerary_id FROM collaboration_participants WHERE user_id = auth.uid())
        )
    );

CREATE POLICY "System can insert activity logs" ON itinerary_activity_logs
    FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);

-- Enable real-time for the tables
ALTER PUBLICATION supabase_realtime ADD TABLE collaboration_participants;
ALTER PUBLICATION supabase_realtime ADD TABLE itinerary_activity_logs;
