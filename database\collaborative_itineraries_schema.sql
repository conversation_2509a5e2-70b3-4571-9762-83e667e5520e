-- Collaborative Itineraries Database Schema for TripWiseGo
-- This schema supports real-time collaboration features

-- Enable Row Level Security and Real-time
ALTER DATABASE postgres SET "app.settings.enable_rls" = 'on';

-- 1. Collaborative Itineraries Table
CREATE TABLE IF NOT EXISTS collaborative_itineraries (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    destinations JSONB NOT NULL DEFAULT '[]',
    daily_activities JSONB NOT NULL DEFAULT '{}',
    day_specific_activities JSONB,
    activity_times JSONB,
    activity_images JSONB,
    accommodation TEXT,
    additional_notes TEXT,
    has_photo BOOLEAN DEFAULT FALSE,
    image_path TEXT,
    
    -- Collaboration metadata
    owner_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    collaboration_code VARCHAR(6) UNIQUE NOT NULL,
    is_public BOOLEAN DEFAULT TRUE,
    max_collaborators INTEGER DEFAULT 10,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_date_range CHECK (end_date >= start_date),
    CONSTRAINT valid_collaboration_code CHECK (collaboration_code ~ '^[A-Z0-9]{6}$')
);

-- 2. Collaboration Participants Table
CREATE TABLE IF NOT EXISTS collaboration_participants (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    itinerary_id UUID REFERENCES collaborative_itineraries(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    role VARCHAR(20) DEFAULT 'viewer' CHECK (role IN ('owner', 'editor', 'viewer')),
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure unique participant per itinerary
    UNIQUE(itinerary_id, user_id)
);

-- 3. Comments Table
CREATE TABLE IF NOT EXISTS itinerary_comments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    itinerary_id UUID REFERENCES collaborative_itineraries(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    parent_comment_id UUID REFERENCES itinerary_comments(id) ON DELETE CASCADE,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT non_empty_content CHECK (LENGTH(TRIM(content)) > 0)
);

-- 4. Activity Logs Table (for tracking changes)
CREATE TABLE IF NOT EXISTS itinerary_activity_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    itinerary_id UUID REFERENCES collaborative_itineraries(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    action_type VARCHAR(50) NOT NULL, -- 'created', 'updated', 'deleted', 'joined', 'commented'
    action_details JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_collaborative_itineraries_owner ON collaborative_itineraries(owner_id);
CREATE INDEX IF NOT EXISTS idx_collaborative_itineraries_code ON collaborative_itineraries(collaboration_code);
CREATE INDEX IF NOT EXISTS idx_collaborative_itineraries_created ON collaborative_itineraries(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_collaboration_participants_itinerary ON collaboration_participants(itinerary_id);
CREATE INDEX IF NOT EXISTS idx_collaboration_participants_user ON collaboration_participants(user_id);
CREATE INDEX IF NOT EXISTS idx_itinerary_comments_itinerary ON itinerary_comments(itinerary_id);
CREATE INDEX IF NOT EXISTS idx_itinerary_comments_created ON itinerary_comments(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_itinerary_activity_logs_itinerary ON itinerary_activity_logs(itinerary_id);

-- Enable real-time for all tables
ALTER PUBLICATION supabase_realtime ADD TABLE collaborative_itineraries;
ALTER PUBLICATION supabase_realtime ADD TABLE collaboration_participants;
ALTER PUBLICATION supabase_realtime ADD TABLE itinerary_comments;
ALTER PUBLICATION supabase_realtime ADD TABLE itinerary_activity_logs;

-- Row Level Security Policies

-- Collaborative Itineraries RLS
ALTER TABLE collaborative_itineraries ENABLE ROW LEVEL SECURITY;

-- Users can view public itineraries or ones they participate in
CREATE POLICY "Users can view collaborative itineraries" ON collaborative_itineraries
    FOR SELECT USING (
        is_public = true OR 
        owner_id = auth.uid() OR 
        id IN (
            SELECT itinerary_id FROM collaboration_participants 
            WHERE user_id = auth.uid()
        )
    );

-- Only owners can update their itineraries
CREATE POLICY "Owners can update their itineraries" ON collaborative_itineraries
    FOR UPDATE USING (owner_id = auth.uid());

-- Only owners can delete their itineraries
CREATE POLICY "Owners can delete their itineraries" ON collaborative_itineraries
    FOR DELETE USING (owner_id = auth.uid());

-- Authenticated users can create itineraries
CREATE POLICY "Authenticated users can create itineraries" ON collaborative_itineraries
    FOR INSERT WITH CHECK (auth.uid() IS NOT NULL AND owner_id = auth.uid());

-- Collaboration Participants RLS
ALTER TABLE collaboration_participants ENABLE ROW LEVEL SECURITY;

-- Users can view participants of itineraries they have access to
CREATE POLICY "Users can view participants" ON collaboration_participants
    FOR SELECT USING (
        itinerary_id IN (
            SELECT id FROM collaborative_itineraries 
            WHERE is_public = true OR owner_id = auth.uid() OR 
            id IN (SELECT itinerary_id FROM collaboration_participants WHERE user_id = auth.uid())
        )
    );

-- Users can join public itineraries
CREATE POLICY "Users can join itineraries" ON collaboration_participants
    FOR INSERT WITH CHECK (
        auth.uid() IS NOT NULL AND 
        user_id = auth.uid() AND
        itinerary_id IN (SELECT id FROM collaborative_itineraries WHERE is_public = true)
    );

-- Owners can manage participants
CREATE POLICY "Owners can manage participants" ON collaboration_participants
    FOR ALL USING (
        itinerary_id IN (SELECT id FROM collaborative_itineraries WHERE owner_id = auth.uid())
    );

-- Comments RLS
ALTER TABLE itinerary_comments ENABLE ROW LEVEL SECURITY;

-- Users can view comments on itineraries they have access to
CREATE POLICY "Users can view comments" ON itinerary_comments
    FOR SELECT USING (
        itinerary_id IN (
            SELECT id FROM collaborative_itineraries 
            WHERE is_public = true OR owner_id = auth.uid() OR 
            id IN (SELECT itinerary_id FROM collaboration_participants WHERE user_id = auth.uid())
        )
    );

-- Participants can add comments
CREATE POLICY "Participants can add comments" ON itinerary_comments
    FOR INSERT WITH CHECK (
        auth.uid() IS NOT NULL AND 
        user_id = auth.uid() AND
        itinerary_id IN (
            SELECT id FROM collaborative_itineraries 
            WHERE owner_id = auth.uid() OR 
            id IN (SELECT itinerary_id FROM collaboration_participants WHERE user_id = auth.uid())
        )
    );

-- Users can update their own comments
CREATE POLICY "Users can update their comments" ON itinerary_comments
    FOR UPDATE USING (user_id = auth.uid());

-- Users can delete their own comments
CREATE POLICY "Users can delete their comments" ON itinerary_comments
    FOR DELETE USING (user_id = auth.uid());

-- Activity Logs RLS
ALTER TABLE itinerary_activity_logs ENABLE ROW LEVEL SECURITY;

-- Users can view activity logs for itineraries they have access to
CREATE POLICY "Users can view activity logs" ON itinerary_activity_logs
    FOR SELECT USING (
        itinerary_id IN (
            SELECT id FROM collaborative_itineraries 
            WHERE is_public = true OR owner_id = auth.uid() OR 
            id IN (SELECT itinerary_id FROM collaboration_participants WHERE user_id = auth.uid())
        )
    );

-- System can insert activity logs
CREATE POLICY "System can insert activity logs" ON itinerary_activity_logs
    FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);

-- Functions for generating collaboration codes
CREATE OR REPLACE FUNCTION generate_collaboration_code()
RETURNS VARCHAR(6) AS $$
DECLARE
    code VARCHAR(6);
    exists_check INTEGER;
BEGIN
    LOOP
        -- Generate a 6-character alphanumeric code
        code := UPPER(
            SUBSTRING(
                MD5(RANDOM()::TEXT || CLOCK_TIMESTAMP()::TEXT) 
                FROM 1 FOR 6
            )
        );
        
        -- Replace any lowercase letters or special chars with numbers/uppercase
        code := TRANSLATE(code, 'abcdefghijklmnopqrstuvwxyz', 'ABCDEFGHIJKLMNOPQRSTUVWXYZ');
        
        -- Check if code already exists
        SELECT COUNT(*) INTO exists_check 
        FROM collaborative_itineraries 
        WHERE collaboration_code = code;
        
        -- Exit loop if code is unique
        EXIT WHEN exists_check = 0;
    END LOOP;
    
    RETURN code;
END;
$$ LANGUAGE plpgsql;

-- Trigger to auto-generate collaboration codes
CREATE OR REPLACE FUNCTION set_collaboration_code()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.collaboration_code IS NULL OR NEW.collaboration_code = '' THEN
        NEW.collaboration_code := generate_collaboration_code();
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_set_collaboration_code
    BEFORE INSERT ON collaborative_itineraries
    FOR EACH ROW
    EXECUTE FUNCTION set_collaboration_code();

-- Trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_collaborative_itineraries_updated_at
    BEFORE UPDATE ON collaborative_itineraries
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_update_comments_updated_at
    BEFORE UPDATE ON itinerary_comments
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
