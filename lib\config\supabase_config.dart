import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/foundation.dart';

class SupabaseConfig {
  static const String url = 'https://ktdstluymbpqejmjkpsg.supabase.co';
  static const String anonKey =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0ZHN0bHV5bWJwcWVqbWprcHNnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ0NDU1ODksImV4cCI6MjA2MDAyMTU4OX0.o8yf7Dsow4pt-A8uekGPQbJI2A-2NKdAqkRISnsYqEY';

  static Future<void> initialize() async {
    try {
      if (kDebugMode) {
        print('Supabase Config: Initializing with URL: $url');
      }

      await Supabase.initialize(
        url: url,
        anonKey: anonKey,
        debug: kDebugMode,
        authOptions: const FlutterAuthClientOptions(
          authFlowType: AuthFlowType.pkce,
        ),
        realtimeClientOptions: const RealtimeClientOptions(
          logLevel: RealtimeLogLevel.info,
        ),
      );

      // Set up global error handler for Supabase auth errors
      client.auth.onAuthStateChange.listen(
        (data) {
          // Handle auth state changes
        },
        onError: (error) {
          if (kDebugMode) {
            print('Supabase Auth Error: $error');
          }
          // Don't rethrow network errors to prevent unhandled exceptions
          if (!_isNetworkError(error)) {
            // Only log non-network errors
            if (kDebugMode) {
              print('Non-network auth error: $error');
            }
          }
        },
        cancelOnError: false, // Don't cancel the stream on error
      );

      if (kDebugMode) {
        print('Supabase Config: Successfully initialized');
      }
    } catch (error) {
      if (kDebugMode) {
        print('Supabase Config: Initialization error: $error');
      }
      rethrow;
    }
  }

  // Check if an error is network-related
  static bool _isNetworkError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    return errorString.contains('socketexception') ||
        errorString.contains('no address associated with hostname') ||
        errorString.contains('failed host lookup') ||
        errorString.contains('network error') ||
        errorString.contains('connection refused') ||
        errorString.contains('timeout') ||
        errorString.contains('authretryablefetchexception');
  }

  static SupabaseClient get client => Supabase.instance.client;

  /// Test connection to Supabase
  static Future<bool> testConnection() async {
    try {
      if (kDebugMode) {
        print('Supabase Config: Testing connection...');
      }

      // Try a simple health check
      await client.from('_supabase_health_check').select().limit(1);

      if (kDebugMode) {
        print('Supabase Config: Connection test successful');
      }
      return true;
    } catch (error) {
      if (kDebugMode) {
        print('Supabase Config: Connection test failed: $error');
      }
      return false;
    }
  }
}
