import 'dart:ui';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../config/supabase_config.dart';
import '../services/auth_service.dart';

class LocalizationService extends ChangeNotifier {
  static const String _languageKey = 'selected_language';
  static const String _localeKey = 'selected_locale';
  
  static final LocalizationService _instance = LocalizationService._internal();
  factory LocalizationService() => _instance;
  LocalizationService._internal();

  Locale _currentLocale = const Locale('en', 'US');
  
  Locale get currentLocale => _currentLocale;
  
  /// Supported languages with their native names and locale information
  static const Map<String, Map<String, String>> supportedLanguages = {
    'en': {
      'name': 'English',
      'nativeName': 'English',
      'countryCode': 'US',
      'flag': '🇺🇸',
      'rtl': 'false',
    },
    'fr': {
      'name': 'French',
      'nativeName': 'Français',
      'countryCode': 'FR',
      'flag': '🇫🇷',
      'rtl': 'false',
    },
    'it': {
      'name': 'Italian',
      'nativeName': 'Italiano',
      'countryCode': 'IT',
      'flag': '🇮🇹',
      'rtl': 'false',
    },
    'zh': {
      'name': 'Chinese (Mandarin)',
      'nativeName': '中文',
      'countryCode': 'CN',
      'flag': '🇨🇳',
      'rtl': 'false',
    },
    'ja': {
      'name': 'Japanese',
      'nativeName': '日本語',
      'countryCode': 'JP',
      'flag': '🇯🇵',
      'rtl': 'false',
    },
    'ko': {
      'name': 'Korean',
      'nativeName': '한국어',
      'countryCode': 'KR',
      'flag': '🇰🇷',
      'rtl': 'false',
    },
    'id': {
      'name': 'Indonesian',
      'nativeName': 'Bahasa Indonesia',
      'countryCode': 'ID',
      'flag': '🇮🇩',
      'rtl': 'false',
    },
    'tl': {
      'name': 'Filipino (Tagalog)',
      'nativeName': 'Filipino',
      'countryCode': 'PH',
      'flag': '🇵🇭',
      'rtl': 'false',
    },
    'th': {
      'name': 'Thai',
      'nativeName': 'ไทย',
      'countryCode': 'TH',
      'flag': '🇹🇭',
      'rtl': 'false',
    },
    'ar': {
      'name': 'Arabic',
      'nativeName': 'العربية',
      'countryCode': 'SA',
      'flag': '🇸🇦',
      'rtl': 'true',
    },
    'hi': {
      'name': 'Hindi',
      'nativeName': 'हिन्दी',
      'countryCode': 'IN',
      'flag': '🇮🇳',
      'rtl': 'false',
    },
    'es': {
      'name': 'Spanish',
      'nativeName': 'Español',
      'countryCode': 'ES',
      'flag': '🇪🇸',
      'rtl': 'false',
    },
    'ru': {
      'name': 'Russian',
      'nativeName': 'Русский',
      'countryCode': 'RU',
      'flag': '🇷🇺',
      'rtl': 'false',
    },
    'pt': {
      'name': 'Portuguese',
      'nativeName': 'Português',
      'countryCode': 'PT',
      'flag': '🇵🇹',
      'rtl': 'false',
    },
  };

  /// Get list of supported locales
  static List<Locale> get supportedLocales {
    return supportedLanguages.entries.map((entry) {
      final languageCode = entry.key;
      final countryCode = entry.value['countryCode']!;
      return Locale(languageCode, countryCode);
    }).toList();
  }

  /// Check if a language is RTL
  static bool isRTL(String languageCode) {
    return supportedLanguages[languageCode]?['rtl'] == 'true';
  }

  /// Get native name for a language
  static String getNativeName(String languageCode) {
    return supportedLanguages[languageCode]?['nativeName'] ?? languageCode;
  }

  /// Get flag emoji for a language
  static String getFlag(String languageCode) {
    return supportedLanguages[languageCode]?['flag'] ?? '🌐';
  }

  /// Initialize the localization service
  Future<void> initialize() async {
    try {
      await _loadSavedLanguage();
      if (kDebugMode) {
        print('Localization Service: Initialized with locale ${_currentLocale.toString()}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Localization Service: Initialization failed - $e');
      }
      // Fall back to English if initialization fails
      _currentLocale = const Locale('en', 'US');
    }
  }

  /// Load saved language preference
  Future<void> _loadSavedLanguage() async {
    try {
      // First try to load from SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      final savedLanguage = prefs.getString(_languageKey);
      final savedLocale = prefs.getString(_localeKey);

      if (savedLanguage != null && savedLocale != null) {
        final parts = savedLocale.split('_');
        if (parts.length == 2) {
          _currentLocale = Locale(parts[0], parts[1]);
          if (kDebugMode) {
            print('Localization Service: Loaded language from SharedPreferences: $savedLanguage');
          }
          return;
        }
      }

      // If not found in SharedPreferences, try to load from Supabase user metadata
      final user = AuthService.currentUser;
      if (user != null && !user.isAnonymous) {
        final userLanguage = user.userMetadata?['preferred_language'];
        if (userLanguage != null && supportedLanguages.containsKey(userLanguage)) {
          await setLanguage(userLanguage);
          return;
        }
      }

      // Fall back to system locale if supported, otherwise English
      final systemLocale = PlatformDispatcher.instance.locale;
      if (supportedLanguages.containsKey(systemLocale.languageCode)) {
        final countryCode = supportedLanguages[systemLocale.languageCode]!['countryCode']!;
        _currentLocale = Locale(systemLocale.languageCode, countryCode);
      } else {
        _currentLocale = const Locale('en', 'US');
      }

      // Save the determined locale
      await _saveLanguagePreference(_currentLocale.languageCode);
      
    } catch (e) {
      if (kDebugMode) {
        print('Localization Service: Error loading saved language - $e');
      }
      _currentLocale = const Locale('en', 'US');
    }
  }

  /// Set the current language
  Future<void> setLanguage(String languageCode) async {
    if (!supportedLanguages.containsKey(languageCode)) {
      throw ArgumentError('Unsupported language code: $languageCode');
    }

    final countryCode = supportedLanguages[languageCode]!['countryCode']!;
    final newLocale = Locale(languageCode, countryCode);

    if (_currentLocale != newLocale) {
      _currentLocale = newLocale;
      await _saveLanguagePreference(languageCode);
      notifyListeners();
      
      if (kDebugMode) {
        print('Localization Service: Language changed to $languageCode');
      }
    }
  }

  /// Save language preference to both SharedPreferences and Supabase
  Future<void> _saveLanguagePreference(String languageCode) async {
    try {
      // Save to SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_languageKey, languageCode);
      await prefs.setString(_localeKey, _currentLocale.toString());

      // Save to Supabase user metadata if user is authenticated
      final user = AuthService.currentUser;
      if (user != null && !user.isAnonymous) {
        try {
          final supabase = SupabaseConfig.client;
          await supabase.auth.updateUser(
            UserAttributes(
              data: {
                'preferred_language': languageCode,
                // Preserve existing metadata
                if (user.userMetadata?['username'] != null)
                  'username': user.userMetadata!['username'],
                if (user.userMetadata?['profile_picture_url'] != null)
                  'profile_picture_url': user.userMetadata!['profile_picture_url'],
              },
            ),
          );
          
          if (kDebugMode) {
            print('Localization Service: Language preference saved to Supabase');
          }
        } catch (e) {
          if (kDebugMode) {
            print('Localization Service: Failed to save language to Supabase - $e');
          }
          // Continue even if Supabase save fails
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Localization Service: Failed to save language preference - $e');
      }
      rethrow;
    }
  }

  /// Get the current language code
  String get currentLanguageCode => _currentLocale.languageCode;

  /// Get the current language native name
  String get currentLanguageNativeName => getNativeName(_currentLocale.languageCode);

  /// Check if current language is RTL
  bool get isCurrentLanguageRTL => isRTL(_currentLocale.languageCode);
}
