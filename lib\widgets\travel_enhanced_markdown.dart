import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:url_launcher/url_launcher.dart';

/// Launch URL in external browser with proper error handling
Future<void> _launchUrlFromMarkdown(String url) async {
  try {
    // Ensure URL has a proper scheme
    String formattedUrl = url;
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      formattedUrl = 'https://$url';
    }

    final uri = Uri.parse(formattedUrl);

    if (await canLaunchUrl(uri)) {
      await launchUrl(
        uri,
        mode: LaunchMode.externalApplication,
      );
      debugPrint('Successfully launched URL: $formattedUrl');
    } else {
      debugPrint('Could not launch URL: $formattedUrl');
      debugPrint(
          'URL Launch Error: Unable to open link. Please check your browser settings.');
    }
  } catch (error) {
    debugPrint('Error launching URL: $error');
    debugPrint('URL Launch Error: Failed to open link: ${error.toString()}');
  }
}

class TravelEnhancedMarkdown extends StatelessWidget {
  final String data;
  final bool isUserMessage;
  final bool isCompact;

  const TravelEnhancedMarkdown({
    super.key,
    required this.data,
    this.isUserMessage = false,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    return MarkdownBody(
      data: data,
      selectable: true,
      styleSheet: _buildMarkdownStyleSheet(),
      shrinkWrap: true,
      fitContent: true,
      onTapLink: (text, href, title) {
        if (href != null) {
          _launchUrlFromMarkdown(href);
        }
      },
    );
  }

  MarkdownStyleSheet _buildMarkdownStyleSheet() {
    final baseColor = isUserMessage ? Colors.white : const Color(0xFF2D3748);
    final codeBackgroundColor =
        isUserMessage ? Colors.white.withOpacity(0.2) : const Color(0xFFF7F9FC);

    // Adjust font sizes for compact mode
    final baseFontSize = isCompact ? 13.0 : 14.0;
    final h1FontSize = isCompact ? 16.0 : 20.0;
    final h2FontSize = isCompact ? 15.0 : 18.0;
    final h3FontSize = isCompact ? 14.0 : 16.0;

    return MarkdownStyleSheet(
      // Paragraph styling
      p: GoogleFonts.instrumentSans(
        fontSize: baseFontSize,
        color: baseColor,
        height: isCompact ? 1.4 : 1.5,
      ),

      // Headers
      h1: GoogleFonts.instrumentSans(
        fontSize: h1FontSize,
        fontWeight: FontWeight.bold,
        color: baseColor,
        height: 1.3,
      ),
      h2: GoogleFonts.instrumentSans(
        fontSize: h2FontSize,
        fontWeight: FontWeight.bold,
        color: baseColor,
        height: 1.3,
      ),
      h3: GoogleFonts.instrumentSans(
        fontSize: h3FontSize,
        fontWeight: FontWeight.w600,
        color: baseColor,
        height: 1.3,
      ),

      // Text styling
      strong: GoogleFonts.instrumentSans(
        fontSize: baseFontSize,
        fontWeight: FontWeight.bold,
        color: baseColor,
      ),
      em: GoogleFonts.instrumentSans(
        fontSize: baseFontSize,
        fontStyle: FontStyle.italic,
        color: baseColor,
      ),

      // Code styling
      code: GoogleFonts.jetBrainsMono(
        fontSize: 13,
        color: isUserMessage ? Colors.white : const Color(0xFF0D76FF),
        backgroundColor: codeBackgroundColor,
      ),

      // List styling
      listBullet: GoogleFonts.instrumentSans(
        fontSize: 14,
        color: baseColor,
        height: 1.5,
      ),

      // Link styling
      a: GoogleFonts.instrumentSans(
        fontSize: 14,
        color: isUserMessage ? Colors.white : const Color(0xFF0D76FF),
        decoration: TextDecoration.underline,
      ),

      // Spacing
      pPadding: const EdgeInsets.only(bottom: 8),
      blockSpacing: 8.0,
    );
  }
}
