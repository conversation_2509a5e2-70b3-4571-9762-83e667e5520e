import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../config/supabase_config.dart';
import '../models/survey_models.dart';

class SurveyService {
  static final SupabaseClient _supabase = SupabaseConfig.client;

  // Check if user has completed the survey
  static Future<bool> hasSurveyCompleted(String userId) async {
    try {
      if (kDebugMode) {
        print('Survey Service: Checking survey completion for user: $userId');
      }

      // First check user metadata
      final user = _supabase.auth.currentUser;
      if (user != null && user.userMetadata != null) {
        final surveyCompleted = user.userMetadata!['survey_completed'] as bool?;
        if (surveyCompleted == true) {
          if (kDebugMode) {
            print('Survey Service: Survey completed found in user metadata');
          }
          return true;
        }
      }

      // Check survey responses table
      try {
        final response = await _supabase
            .from('survey_responses')
            .select('is_completed')
            .eq('user_id', userId)
            .maybeSingle();

        final isCompleted = response?['is_completed'] as bool? ?? false;
        if (isCompleted) {
          if (kDebugMode) {
            print('Survey Service: Survey completion found in database table');
          }
          return true;
        }
      } catch (tableError) {
        if (kDebugMode) {
          print(
              'Survey Service: Table not found, checking metadata only: $tableError');
        }
      }

      // If table doesn't exist or no data found, rely on user metadata
      final isCompleted =
          user?.userMetadata?['survey_completed'] as bool? ?? false;

      if (kDebugMode) {
        print('Survey Service: Survey completion status: $isCompleted');
      }

      return isCompleted;
    } catch (error) {
      if (kDebugMode) {
        print('Survey Service: Error checking survey completion: $error');
      }
      return false;
    }
  }

  // Save survey response
  static Future<bool> saveSurveyResponse(SurveyResponse surveyResponse) async {
    try {
      if (kDebugMode) {
        print(
            'Survey Service: Saving survey response for user: ${surveyResponse.userId}');
      }

      // Try to save to survey_responses table first
      try {
        await _supabase
            .from('survey_responses')
            .upsert(surveyResponse.toJson());
        if (kDebugMode) {
          print('Survey Service: Survey response saved to database table');
        }
      } catch (tableError) {
        if (kDebugMode) {
          print(
              'Survey Service: Table not found, saving to user metadata instead: $tableError');
        }

        // Fallback: Save survey data in user metadata
        await _supabase.auth.updateUser(
          UserAttributes(
            data: {
              'survey_completed': true,
              'survey_completed_at':
                  surveyResponse.completedAt.toIso8601String(),
              'survey_data': surveyResponse.toJson(),
            },
          ),
        );

        if (kDebugMode) {
          print(
              'Survey Service: Survey response saved to user metadata as fallback');
        }
      }

      // Always update user metadata for quick access
      await _supabase.auth.updateUser(
        UserAttributes(
          data: {
            'survey_completed': true,
            'survey_completed_at': surveyResponse.completedAt.toIso8601String(),
          },
        ),
      );

      if (kDebugMode) {
        print('Survey Service: Survey response saved successfully');
      }

      return true;
    } catch (error) {
      if (kDebugMode) {
        print('Survey Service: Error saving survey response: $error');
      }
      return false;
    }
  }

  // Get survey response for a user
  static Future<SurveyResponse?> getSurveyResponse(String userId) async {
    try {
      if (kDebugMode) {
        print('Survey Service: Getting survey response for user: $userId');
      }

      // Try to get from survey_responses table first
      try {
        final response = await _supabase
            .from('survey_responses')
            .select()
            .eq('user_id', userId)
            .maybeSingle();

        if (response != null) {
          if (kDebugMode) {
            print('Survey Service: Survey response found in database table');
          }
          return SurveyResponse.fromJson(response);
        }
      } catch (tableError) {
        if (kDebugMode) {
          print(
              'Survey Service: Table not found, checking user metadata: $tableError');
        }
      }

      // Fallback: Check user metadata
      final user = _supabase.auth.currentUser;
      if (user != null && user.userMetadata != null) {
        final surveyData =
            user.userMetadata!['survey_data'] as Map<String, dynamic>?;
        if (surveyData != null) {
          if (kDebugMode) {
            print('Survey Service: Survey response found in user metadata');
          }
          return SurveyResponse.fromJson(surveyData);
        }
      }

      return null;
    } catch (error) {
      if (kDebugMode) {
        print('Survey Service: Error getting survey response: $error');
      }
      return null;
    }
  }

  // Mark survey as completed in user metadata (for quick access)
  static Future<bool> markSurveyCompleted(String userId) async {
    try {
      if (kDebugMode) {
        print('Survey Service: Marking survey as completed for user: $userId');
      }

      await _supabase.auth.updateUser(
        UserAttributes(
          data: {
            'survey_completed': true,
            'survey_completed_at': DateTime.now().toIso8601String(),
          },
        ),
      );

      if (kDebugMode) {
        print('Survey Service: Survey marked as completed successfully');
      }

      return true;
    } catch (error) {
      if (kDebugMode) {
        print('Survey Service: Error marking survey as completed: $error');
      }
      return false;
    }
  }

  // Create survey responses table (for initial setup)
  static Future<void> createSurveyTable() async {
    try {
      if (kDebugMode) {
        print('Survey Service: Creating survey_responses table...');
      }

      // This would typically be done via Supabase SQL editor
      // Including here for reference of the table structure
      const createTableSQL = '''
        CREATE TABLE IF NOT EXISTS survey_responses (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
          hear_about_us TEXT,
          travel_companion TEXT,
          travel_priorities TEXT[], -- Array type for multiple selections
          travel_frustrations TEXT[], -- Array type for multiple selections
          preferred_destination_types TEXT[], -- Array type for multiple selections
          completed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          is_completed BOOLEAN DEFAULT TRUE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          UNIQUE(user_id)
        );

        -- Enable RLS
        ALTER TABLE survey_responses ENABLE ROW LEVEL SECURITY;

        -- Create policy for users to access their own survey data
        CREATE POLICY "Users can view their own survey responses" ON survey_responses
          FOR SELECT USING (auth.uid() = user_id);

        CREATE POLICY "Users can insert their own survey responses" ON survey_responses
          FOR INSERT WITH CHECK (auth.uid() = user_id);

        CREATE POLICY "Users can update their own survey responses" ON survey_responses
          FOR UPDATE USING (auth.uid() = user_id);

        -- Create index for faster lookups
        CREATE INDEX IF NOT EXISTS idx_survey_responses_user_id ON survey_responses(user_id);
        CREATE INDEX IF NOT EXISTS idx_survey_responses_completed ON survey_responses(is_completed);
      ''';

      if (kDebugMode) {
        print('Survey Service: Table creation SQL prepared');
        print('Please run this SQL in your Supabase SQL editor:');
        print(createTableSQL);
      }
    } catch (error) {
      if (kDebugMode) {
        print('Survey Service: Error preparing table creation: $error');
      }
    }
  }

  // Validate survey response completeness
  static bool validateSurveyResponse(SurveyResponse response) {
    // Check if all required fields are filled
    if ((response.hearAboutUs?.isEmpty ?? true) ||
        (response.travelCompanion?.isEmpty ?? true)) {
      return false;
    }

    // Check if at least one option is selected for multi-select fields
    if (response.preferredDestinationTypes.isEmpty ||
        response.travelFrustrations.isEmpty ||
        response.travelPriorities.isEmpty) {
      return false;
    }

    return true;
  }

  // Get survey completion statistics (for admin/analytics)
  static Future<Map<String, dynamic>> getSurveyStats() async {
    try {
      if (kDebugMode) {
        print('Survey Service: Getting survey statistics');
      }

      // Note: This is a simplified version for demonstration
      // In a real implementation, you would need proper admin access to count users
      final completedSurveys = await _supabase
          .from('survey_responses')
          .select('id')
          .eq('is_completed', true);

      const totalCount = 0; // Would need admin access to count total users
      final completedCount = completedSurveys.length;
      final completionRate =
          totalCount > 0 ? (completedCount / totalCount * 100) : 0.0;

      return {
        'total_users': totalCount,
        'completed_surveys': completedCount,
        'completion_rate': completionRate,
      };
    } catch (error) {
      if (kDebugMode) {
        print('Survey Service: Error getting survey statistics: $error');
      }
      return {
        'total_users': 0,
        'completed_surveys': 0,
        'completion_rate': 0.0,
      };
    }
  }
}
