import 'dart:convert';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:flutter/foundation.dart';

class LikedDestinationsService {
  static Database? _database;
  static const String _tableName = 'liked_destinations';
  static const String _databaseName = 'liked_destinations.db';
  static const int _databaseVersion = 1;

  /// Initialize the database
  static Future<void> initialize() async {
    if (_database != null) return;

    try {
      final databasePath = await getDatabasesPath();
      final path = join(databasePath, _databaseName);

      _database = await openDatabase(
        path,
        version: _databaseVersion,
        onCreate: _createDatabase,
        onUpgrade: _upgradeDatabase,
      );

      if (kDebugMode) {
        print('Liked Destinations Service: Database initialized');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Liked Destinations Service: Failed to initialize database - $e');
      }
      rethrow;
    }
  }

  /// Create the database tables
  static Future<void> _createDatabase(Database db, int version) async {
    await db.execute('''
      CREATE TABLE $_tableName (
        id TEXT PRIMARY KEY,
        destination_data TEXT NOT NULL,
        liked_at INTEGER NOT NULL,
        user_id TEXT
      )
    ''');

    // Create index for better performance
    await db.execute('''
      CREATE INDEX idx_liked_destinations_user_id ON $_tableName (user_id)
    ''');

    await db.execute('''
      CREATE INDEX idx_liked_destinations_liked_at ON $_tableName (liked_at DESC)
    ''');

    if (kDebugMode) {
      print('Liked Destinations Service: Database tables created');
    }
  }

  /// Handle database upgrades
  static Future<void> _upgradeDatabase(Database db, int oldVersion, int newVersion) async {
    if (kDebugMode) {
      print('Liked Destinations Service: Upgrading database from $oldVersion to $newVersion');
    }
    // Handle future database schema changes here
  }

  /// Ensure database is initialized
  static Future<void> _ensureInitialized() async {
    if (_database == null) {
      await initialize();
    }
  }

  /// Add a destination to liked destinations
  static Future<bool> addLikedDestination(Map<String, dynamic> destination, {String? userId}) async {
    try {
      await _ensureInitialized();
      
      final destinationId = destination['id']?.toString();
      if (destinationId == null) {
        if (kDebugMode) {
          print('Liked Destinations Service: Destination ID is null');
        }
        return false;
      }

      // Check if already liked
      final existing = await _database!.query(
        _tableName,
        where: 'id = ? AND user_id = ?',
        whereArgs: [destinationId, userId ?? 'anonymous'],
        limit: 1,
      );

      if (existing.isNotEmpty) {
        if (kDebugMode) {
          print('Liked Destinations Service: Destination already liked');
        }
        return true; // Already liked, consider it successful
      }

      // Add to database
      await _database!.insert(
        _tableName,
        {
          'id': destinationId,
          'destination_data': jsonEncode(destination),
          'liked_at': DateTime.now().millisecondsSinceEpoch,
          'user_id': userId ?? 'anonymous',
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      if (kDebugMode) {
        print('Liked Destinations Service: Added destination ${destination['name']}');
      }
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Liked Destinations Service: Failed to add liked destination - $e');
      }
      return false;
    }
  }

  /// Remove a destination from liked destinations
  static Future<bool> removeLikedDestination(Map<String, dynamic> destination, {String? userId}) async {
    try {
      await _ensureInitialized();
      
      final destinationId = destination['id']?.toString();
      if (destinationId == null) return false;

      final deletedRows = await _database!.delete(
        _tableName,
        where: 'id = ? AND user_id = ?',
        whereArgs: [destinationId, userId ?? 'anonymous'],
      );

      if (kDebugMode) {
        print('Liked Destinations Service: Removed destination ${destination['name']}');
      }
      return deletedRows > 0;
    } catch (e) {
      if (kDebugMode) {
        print('Liked Destinations Service: Failed to remove liked destination - $e');
      }
      return false;
    }
  }

  /// Get all liked destinations for a user
  static Future<List<Map<String, dynamic>>> getLikedDestinations({String? userId}) async {
    try {
      await _ensureInitialized();

      final results = await _database!.query(
        _tableName,
        where: 'user_id = ?',
        whereArgs: [userId ?? 'anonymous'],
        orderBy: 'liked_at DESC',
      );

      final destinations = <Map<String, dynamic>>[];
      for (final row in results) {
        try {
          final destinationData = jsonDecode(row['destination_data'] as String);
          destinations.add(destinationData);
        } catch (e) {
          if (kDebugMode) {
            print('Liked Destinations Service: Failed to parse destination data - $e');
          }
        }
      }

      if (kDebugMode) {
        print('Liked Destinations Service: Retrieved ${destinations.length} liked destinations');
      }
      return destinations;
    } catch (e) {
      if (kDebugMode) {
        print('Liked Destinations Service: Failed to get liked destinations - $e');
      }
      return [];
    }
  }

  /// Check if a destination is liked
  static Future<bool> isDestinationLiked(Map<String, dynamic> destination, {String? userId}) async {
    try {
      await _ensureInitialized();
      
      final destinationId = destination['id']?.toString();
      if (destinationId == null) return false;

      final results = await _database!.query(
        _tableName,
        where: 'id = ? AND user_id = ?',
        whereArgs: [destinationId, userId ?? 'anonymous'],
        limit: 1,
      );

      return results.isNotEmpty;
    } catch (e) {
      if (kDebugMode) {
        print('Liked Destinations Service: Failed to check if destination is liked - $e');
      }
      return false;
    }
  }

  /// Get the count of liked destinations
  static Future<int> getLikedDestinationsCount({String? userId}) async {
    try {
      await _ensureInitialized();

      final result = await _database!.rawQuery(
        'SELECT COUNT(*) as count FROM $_tableName WHERE user_id = ?',
        [userId ?? 'anonymous'],
      );

      return Sqflite.firstIntValue(result) ?? 0;
    } catch (e) {
      if (kDebugMode) {
        print('Liked Destinations Service: Failed to get liked destinations count - $e');
      }
      return 0;
    }
  }

  /// Clear all liked destinations for a user (for testing/debugging)
  static Future<bool> clearLikedDestinations({String? userId}) async {
    try {
      await _ensureInitialized();

      await _database!.delete(
        _tableName,
        where: 'user_id = ?',
        whereArgs: [userId ?? 'anonymous'],
      );

      if (kDebugMode) {
        print('Liked Destinations Service: Cleared all liked destinations');
      }
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Liked Destinations Service: Failed to clear liked destinations - $e');
      }
      return false;
    }
  }

  /// Close the database connection
  static Future<void> close() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
      if (kDebugMode) {
        print('Liked Destinations Service: Database closed');
      }
    }
  }
}
