import 'package:flutter/material.dart';
import '../services/ai_itinerary_parser.dart';
import '../services/itinerary_service.dart';

/// Service to handle automatic navigation to Plan page when AI generates itineraries
class PlanNavigationService {
  static const int _planTabIndex =
      1; // Index of the Plan tab in chat screen TabBar (0: Chat, 1: Plan)

  /// Check if AI response contains itinerary and handle navigation within chat screen
  static Future<void> handleAIResponse(
    String aiResponse,
    BuildContext context,
    Function() onNavigateToPlanTab, {
    String? userMessage,
  }) async {
    try {
      // First check if the AI response contains a complete structured itinerary
      if (!AIItineraryParser.containsItinerary(aiResponse)) {
        return; // No itinerary detected, don't navigate
      }

      // If we have the user message, verify they actually requested an itinerary
      if (userMessage != null) {
        // Skip if user was asking general travel questions
        if (AIItineraryParser.isGeneralTravelQuestion(userMessage)) {
          return;
        }

        // For explicit itinerary requests, proceed with navigation
        // For other cases, require stronger itinerary indicators in the response
        if (!AIItineraryParser.userRequestedItinerary(userMessage)) {
          // User didn't explicitly request an itinerary
          // Only proceed if the AI response is very clearly a complete itinerary
          if (!_isCompleteItineraryResponse(aiResponse)) {
            return;
          }
        }
      }

      // Parse the itinerary
      final parsedItinerary = AIItineraryParser.parseItinerary(aiResponse);

      if (parsedItinerary != null) {
        // Validate that the parsed itinerary has substantial content
        if (!_hasSubstantialContent(parsedItinerary)) {
          return; // Not enough content to warrant navigation
        }

        // Convert to Itinerary model and save
        final itinerary = parsedItinerary.toItinerary();
        final success = await ItineraryService.saveItinerary(itinerary);

        if (success) {
          // Add a small delay for better UX
          await Future.delayed(const Duration(milliseconds: 500));

          // Navigate to Plan tab within chat screen
          onNavigateToPlanTab();

          // Show success message
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: const Text(
                  'Itinerary created! Check it out in the Plan tab.',
                  style: TextStyle(color: Colors.white),
                ),
                backgroundColor: const Color(0xFF0D76FF),
                duration: const Duration(seconds: 3),
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            );
          }
        }
      }
    } catch (e) {
      debugPrint('Error handling AI response for plan navigation: $e');
    }
  }

  /// Check if the AI response represents a complete itinerary (not just partial info)
  static bool _isCompleteItineraryResponse(String aiResponse) {
    final response = aiResponse.toLowerCase();

    // Look for indicators of a complete itinerary
    final completeItineraryIndicators = [
      RegExp(r'here.s\s+(your|a)\s+(complete\s+)?itinerary'),
      RegExp(r'(complete|detailed)\s+itinerary'),
      RegExp(r'day-by-day\s+(plan|itinerary)'),
      RegExp(r'\d+\s*day\s+itinerary'),
      RegExp(r'full\s+travel\s+plan'),
    ];

    for (final indicator in completeItineraryIndicators) {
      if (indicator.hasMatch(response)) {
        return true;
      }
    }

    // Check for multiple day structure
    final dayMatches = RegExp(r'day \d+').allMatches(response);
    return dayMatches.length >= 2;
  }

  /// Check if parsed itinerary has substantial content worth displaying
  static bool _hasSubstantialContent(dynamic parsedItinerary) {
    try {
      // Must have destinations
      if (parsedItinerary.destinations == null ||
          parsedItinerary.destinations.isEmpty) {
        return false;
      }

      // Must have activities
      int totalActivities = 0;

      // Count activities from dailyActivities
      if (parsedItinerary.activities != null) {
        for (final activities in parsedItinerary.activities.values) {
          if (activities != null) {
            totalActivities += (activities.length as num).toInt();
          }
        }
      }

      // Also count day-specific activities
      if (parsedItinerary.daySpecificActivities != null) {
        for (final dayActivities
            in parsedItinerary.daySpecificActivities.values) {
          if (dayActivities != null) {
            for (final destinationActivities in dayActivities.values) {
              if (destinationActivities != null) {
                totalActivities +=
                    (destinationActivities.length as num).toInt();
              }
            }
          }
        }
      }

      // Require at least 3 activities for substantial content
      return totalActivities >= 3;
    } catch (e) {
      // If there's any error accessing the properties, assume it's not substantial
      return false;
    }
  }

  /// Check if a message contains travel planning keywords
  static bool isItineraryRelatedQuery(String message) {
    final lowerMessage = message.toLowerCase();

    final itineraryKeywords = [
      'itinerary',
      'travel plan',
      'trip plan',
      'plan my trip',
      'plan a trip',
      'travel to',
      'visit',
      'vacation',
      'holiday',
      'tour',
      'travel guide',
      'things to do',
      'places to visit',
      'travel schedule',
      'day by day',
      'travel route',
      'destination',
      'sightseeing',
      'travel advice',
      'trip ideas',
    ];

    for (final keyword in itineraryKeywords) {
      if (lowerMessage.contains(keyword)) {
        return true;
      }
    }

    return false;
  }

  /// Show a hint to user about automatic plan creation
  static void showPlanCreationHint(BuildContext context) {
    if (!context.mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Row(
          children: [
            Icon(
              Icons.lightbulb_outline,
              color: Colors.white,
              size: 20,
            ),
            SizedBox(width: 8),
            Expanded(
              child: Text(
                'Tip: Ask for a detailed itinerary and I\'ll create a plan for you!',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: const Color(0xFF0D76FF),
        duration: const Duration(seconds: 4),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// Navigate to Plan tab with animation
  static void navigateToPlanTab(Function(int) onNavigateToTab) {
    onNavigateToTab(_planTabIndex);
  }

  /// Check if current tab is Plan tab
  static bool isCurrentlyOnPlanTab(int currentIndex) {
    return currentIndex == _planTabIndex;
  }
}
