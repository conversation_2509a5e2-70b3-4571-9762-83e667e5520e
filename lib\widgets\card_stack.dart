import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'simple_swipeable_card.dart';

class CardStack extends StatefulWidget {
  final List<Map<String, dynamic>> destinations;
  final Function(Map<String, dynamic>, bool) onCardSwiped;

  const CardStack({
    super.key,
    required this.destinations,
    required this.onCardSwiped,
  });

  @override
  State<CardStack> createState() => _CardStackState();
}

class _CardStackState extends State<CardStack> with TickerProviderStateMixin {
  late List<Map<String, dynamic>> _currentDestinations;
  late AnimationController _stackController;
  late Animation<double> _stackAnimation;

  @override
  void initState() {
    super.initState();
    _currentDestinations = List.from(widget.destinations);

    _stackController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _stackAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _stackController,
      curve: Curves.easeOut,
    ));
  }

  @override
  void dispose() {
    _stackController.dispose();
    super.dispose();
  }

  void _onCardSwiped(bool isLike) {
    if (_currentDestinations.isNotEmpty) {
      final swipedCard = _currentDestinations.first;
      widget.onCardSwiped(swipedCard, isLike);

      setState(() {
        _currentDestinations.removeAt(0);
      });

      // Animate the stack reorganization
      _stackController.forward().then((_) {
        _stackController.reset();
      });
    }
  }

  void _swipeLeft() {
    _onCardSwiped(false);
  }

  void _swipeRight() {
    _onCardSwiped(true);
  }

  @override
  Widget build(BuildContext context) {
    if (_currentDestinations.isEmpty) {
      return _buildEmptyState();
    }

    return Column(
      children: [
        // Tab selector
        _buildTabSelector(),
        const SizedBox(height: 20),

        // Card stack
        Expanded(
          child: Stack(
            children: [
              // Background cards (show up to 3 cards in stack)
              // Reverse the order so upcoming cards appear behind current card
              for (int i = 0; i < _currentDestinations.length && i < 3; i++)
                _buildStackCard(i),
            ],
          ),
        ),

        // Action buttons
        _buildActionButtons(),
        const SizedBox(height: 20),
      ],
    );
  }

  Widget _buildTabSelector() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: const Color(0xFF0D76FF),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                'For You',
                textAlign: TextAlign.center,
                style: GoogleFonts.instrumentSans(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ),
          ),
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 12),
              child: Text(
                'Liked',
                textAlign: TextAlign.center,
                style: GoogleFonts.instrumentSans(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF718096),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStackCard(int index) {
    final destination = _currentDestinations[index];
    final isTopCard = index == 0;
    // Reverse the stack index calculation for proper layering
    final stackIndex = index;

    // Calculate offset and scale for stack effect
    final offset = stackIndex * 6.0;
    final scale = 1.0 - (stackIndex * 0.03);

    return AnimatedBuilder(
      animation: _stackAnimation,
      builder: (context, child) {
        return Positioned(
          top: offset,
          left: offset / 2,
          right: offset / 2,
          bottom: 100 + offset, // Leave space for action buttons
          child: Transform.scale(
            scale: scale,
            child: SimpleSwipeableCard(
              destination: destination,
              isTopCard: isTopCard,
              onSwipeLeft: isTopCard ? _swipeLeft : null,
              onSwipeRight: isTopCard ? _swipeRight : null,
            ),
          ),
        );
      },
    );
  }

  Widget _buildActionButtons() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 40),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // Pass button
          _buildActionButton(
            icon: Icons.close,
            color: Colors.red,
            onTap: _swipeLeft,
            size: 60,
          ),

          // Super like button (placeholder)
          _buildActionButton(
            icon: Icons.add,
            color: const Color(0xFF0D76FF),
            onTap: () {
              // Placeholder for super like functionality
            },
            size: 50,
          ),

          // Like button
          _buildActionButton(
            icon: Icons.check,
            color: Colors.green,
            onTap: _swipeRight,
            size: 60,
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
    required double size,
  }) {
    return GestureDetector(
      onTap: _currentDestinations.isNotEmpty ? onTap : null,
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: Colors.white,
          shape: BoxShape.circle,
          border: Border.all(
            color: color,
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Icon(
          icon,
          color: color,
          size: size * 0.4,
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Column(
      children: [
        _buildTabSelector(),
        Expanded(
          child: Center(
            child: Container(
              margin: const EdgeInsets.all(40),
              padding: const EdgeInsets.all(40),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.favorite_outline,
                    size: 80,
                    color: const Color(0xFF0D76FF).withOpacity(0.3),
                  ),
                  const SizedBox(height: 24),
                  Text(
                    'No More Destinations',
                    style: GoogleFonts.instrumentSans(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF2D3748),
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'You\'ve seen all available destinations. Check back later for more travel inspiration!',
                    style: GoogleFonts.instrumentSans(
                      fontSize: 16,
                      color: const Color(0xFF718096),
                      height: 1.4,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xFF0D76FF).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      'Coming Soon: More Destinations',
                      style: GoogleFonts.instrumentSans(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF0D76FF),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
