import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:tripwisego/services/localization_service.dart';
import 'package:tripwisego/widgets/language_picker.dart';
import 'package:tripwisego/generated/l10n/app_localizations.dart';

void main() {
  group('Internationalization (i18n) Tests', () {
    testWidgets('LocalizationService supports all required languages', (WidgetTester tester) async {
      // Test that all 14 required languages are supported
      final supportedLanguages = LocalizationService.supportedLanguages;
      
      expect(supportedLanguages.length, equals(14));
      
      // Check specific languages
      expect(supportedLanguages.containsKey('en'), isTrue);
      expect(supportedLanguages.containsKey('fr'), isTrue);
      expect(supportedLanguages.containsKey('it'), isTrue);
      expect(supportedLanguages.containsKey('zh'), isTrue);
      expect(supportedLanguages.containsKey('ja'), isTrue);
      expect(supportedLanguages.containsKey('ko'), isTrue);
      expect(supportedLanguages.containsKey('id'), isTrue);
      expect(supportedLanguages.containsKey('tl'), isTrue);
      expect(supportedLanguages.containsKey('th'), isTrue);
      expect(supportedLanguages.containsKey('ar'), isTrue);
      expect(supportedLanguages.containsKey('hi'), isTrue);
      expect(supportedLanguages.containsKey('es'), isTrue);
      expect(supportedLanguages.containsKey('ru'), isTrue);
      expect(supportedLanguages.containsKey('pt'), isTrue);
    });

    testWidgets('RTL language detection works correctly', (WidgetTester tester) async {
      // Test RTL detection
      expect(LocalizationService.isRTL('ar'), isTrue);
      expect(LocalizationService.isRTL('en'), isFalse);
      expect(LocalizationService.isRTL('fr'), isFalse);
      expect(LocalizationService.isRTL('zh'), isFalse);
    });

    testWidgets('Native language names are correct', (WidgetTester tester) async {
      // Test native names
      expect(LocalizationService.getNativeName('en'), equals('English'));
      expect(LocalizationService.getNativeName('fr'), equals('Français'));
      expect(LocalizationService.getNativeName('zh'), equals('中文'));
      expect(LocalizationService.getNativeName('ja'), equals('日本語'));
      expect(LocalizationService.getNativeName('ko'), equals('한국어'));
      expect(LocalizationService.getNativeName('ar'), equals('العربية'));
      expect(LocalizationService.getNativeName('hi'), equals('हिन्दी'));
      expect(LocalizationService.getNativeName('ru'), equals('Русский'));
      expect(LocalizationService.getNativeName('th'), equals('ไทย'));
    });

    testWidgets('Language picker displays correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          localizationsDelegates: [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: LocalizationService.supportedLocales,
          home: const Scaffold(
            body: LanguagePicker(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify that the language picker is displayed
      expect(find.byType(LanguagePicker), findsOneWidget);
      
      // Verify that language items are displayed
      expect(find.text('English'), findsOneWidget);
      expect(find.text('Français'), findsOneWidget);
      expect(find.text('中文'), findsOneWidget);
      expect(find.text('日本語'), findsOneWidget);
    });

    testWidgets('Language picker bottom sheet displays correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          localizationsDelegates: [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: LocalizationService.supportedLocales,
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () => showLanguagePickerBottomSheet(context),
                child: const Text('Show Language Picker'),
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Tap the button to show the bottom sheet
      await tester.tap(find.text('Show Language Picker'));
      await tester.pumpAndSettle();

      // Verify that the bottom sheet is displayed
      expect(find.byType(LanguagePicker), findsOneWidget);
    });

    test('Supported locales are generated correctly', () {
      final locales = LocalizationService.supportedLocales;
      
      expect(locales.length, equals(14));
      
      // Check specific locales
      expect(locales.any((l) => l.languageCode == 'en' && l.countryCode == 'US'), isTrue);
      expect(locales.any((l) => l.languageCode == 'fr' && l.countryCode == 'FR'), isTrue);
      expect(locales.any((l) => l.languageCode == 'zh' && l.countryCode == 'CN'), isTrue);
      expect(locales.any((l) => l.languageCode == 'ja' && l.countryCode == 'JP'), isTrue);
      expect(locales.any((l) => l.languageCode == 'ar' && l.countryCode == 'SA'), isTrue);
    });

    testWidgets('AppLocalizations works with different locales', (WidgetTester tester) async {
      // Test English
      await tester.pumpWidget(
        MaterialApp(
          locale: const Locale('en', 'US'),
          localizationsDelegates: [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: LocalizationService.supportedLocales,
          home: Builder(
            builder: (context) => Scaffold(
              body: Text(AppLocalizations.of(context).welcome),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();
      expect(find.text('Welcome'), findsOneWidget);

      // Test French
      await tester.pumpWidget(
        MaterialApp(
          locale: const Locale('fr', 'FR'),
          localizationsDelegates: [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: LocalizationService.supportedLocales,
          home: Builder(
            builder: (context) => Scaffold(
              body: Text(AppLocalizations.of(context).welcome),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();
      expect(find.text('Bienvenue'), findsOneWidget);
    });

    test('LocalizationService initialization', () async {
      final service = LocalizationService();
      
      // Test default locale
      expect(service.currentLocale.languageCode, equals('en'));
      
      // Test language change
      await service.setLanguage('fr');
      expect(service.currentLocale.languageCode, equals('fr'));
      expect(service.currentLocale.countryCode, equals('FR'));
      
      // Test invalid language
      expect(() => service.setLanguage('invalid'), throwsArgumentError);
    });

    test('Language flags are provided', () {
      final supportedLanguages = LocalizationService.supportedLanguages;
      
      for (final entry in supportedLanguages.entries) {
        final flag = LocalizationService.getFlag(entry.key);
        expect(flag.isNotEmpty, isTrue);
        expect(flag.length, greaterThan(0));
      }
    });
  });

  group('Localization Content Tests', () {
    testWidgets('All basic strings are available in English', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          locale: const Locale('en', 'US'),
          localizationsDelegates: [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: LocalizationService.supportedLocales,
          home: Builder(
            builder: (context) {
              final l10n = AppLocalizations.of(context);
              return Scaffold(
                body: Column(
                  children: [
                    Text(l10n.appTitle),
                    Text(l10n.welcome),
                    Text(l10n.home),
                    Text(l10n.profile),
                    Text(l10n.chat),
                    Text(l10n.match),
                    Text(l10n.language),
                    Text(l10n.save),
                    Text(l10n.cancel),
                    Text(l10n.loading),
                  ],
                ),
              );
            },
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify basic strings are present
      expect(find.text('TripWiseGo'), findsOneWidget);
      expect(find.text('Welcome'), findsOneWidget);
      expect(find.text('Home'), findsOneWidget);
      expect(find.text('Profile'), findsOneWidget);
      expect(find.text('Chat'), findsOneWidget);
      expect(find.text('Match'), findsOneWidget);
      expect(find.text('Language'), findsOneWidget);
      expect(find.text('Save'), findsOneWidget);
      expect(find.text('Cancel'), findsOneWidget);
      expect(find.text('Loading...'), findsOneWidget);
    });
  });
}
