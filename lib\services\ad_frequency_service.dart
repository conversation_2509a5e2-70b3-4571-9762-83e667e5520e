import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'subscription_service.dart';
import '../config/admob_config.dart';

/// Service for tracking swipe counts specifically for ad frequency
/// This is separate from the daily swipe limit tracking
class AdFrequencyService {
  static bool _isInitialized = false;
  static int _swipeCountSinceLastAd = 0;

  // SharedPreferences keys
  static const String _swipeCountKey = 'ad_frequency_swipe_count';

  /// Initialize the ad frequency service
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadSwipeCountFromStorage();
      _isInitialized = true;

      if (kDebugMode) {
        print('Ad Frequency Service: Initialized successfully');
        print('Current swipe count since last ad: $_swipeCountSinceLastAd');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Ad Frequency Service: Initialization error - $e');
      }
    }
  }

  /// Ensure service is initialized
  static Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  /// Load swipe count from local storage
  static Future<void> _loadSwipeCountFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _swipeCountSinceLastAd = prefs.getInt(_swipeCountKey) ?? 0;

      if (kDebugMode) {
        print(
            'Ad Frequency Service: Loaded swipe count from storage: $_swipeCountSinceLastAd');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Ad Frequency Service: Error loading from storage - $e');
      }
      _swipeCountSinceLastAd = 0;
    }
  }

  /// Save swipe count to local storage
  static Future<void> _saveSwipeCountToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_swipeCountKey, _swipeCountSinceLastAd);

      if (kDebugMode) {
        print(
            'Ad Frequency Service: Saved swipe count to storage: $_swipeCountSinceLastAd');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Ad Frequency Service: Error saving to storage - $e');
      }
    }
  }

  /// Record a swipe and check if it's time to show an ad
  static Future<bool> recordSwipeAndCheckAdTrigger() async {
    await _ensureInitialized();

    // Check if user should see ads (free users only)
    final shouldShowAds = await _shouldShowAds();
    if (!shouldShowAds) {
      if (kDebugMode) {
        print('Ad Frequency Service: Skipping ad tracking for subscribed user');
      }
      return false;
    }

    // Increment swipe count
    _swipeCountSinceLastAd++;
    await _saveSwipeCountToStorage();

    if (kDebugMode) {
      print(
          'Ad Frequency Service: Swipe recorded. Count since last ad: $_swipeCountSinceLastAd');
    }

    // Check if it's time to show an ad
    if (_swipeCountSinceLastAd >= AdMobConfig.adFrequency) {
      if (kDebugMode) {
        print(
            'Ad Frequency Service: Time to show ad! ($_swipeCountSinceLastAd swipes)');
      }
      return true;
    }

    return false;
  }

  /// Reset the swipe count (called after an ad is shown)
  static Future<void> resetSwipeCount() async {
    await _ensureInitialized();

    _swipeCountSinceLastAd = 0;
    await _saveSwipeCountToStorage();

    if (kDebugMode) {
      print('Ad Frequency Service: Swipe count reset after ad shown');
    }
  }

  /// Get current swipe count since last ad
  static Future<int> getCurrentSwipeCount() async {
    await _ensureInitialized();
    return _swipeCountSinceLastAd;
  }

  /// Get how many more swipes until next ad
  static Future<int> getSwipesUntilNextAd() async {
    await _ensureInitialized();

    final shouldShowAds = await _shouldShowAds();
    if (!shouldShowAds) {
      return -1; // No ads for subscribed users
    }

    return AdMobConfig.adFrequency - _swipeCountSinceLastAd;
  }

  /// Check if user should see ads (free users only)
  static Future<bool> _shouldShowAds() async {
    try {
      final hasActiveSubscription =
          await SubscriptionService.hasActiveSubscription();
      return !hasActiveSubscription;
    } catch (e) {
      if (kDebugMode) {
        print('Ad Frequency Service: Error checking subscription status - $e');
      }
      // Default to showing ads if we can't determine subscription status
      return true;
    }
  }

  /// Get ad frequency setting
  static int get adFrequency => AdMobConfig.adFrequency;

  /// Get debug information
  static Future<Map<String, dynamic>> getDebugInfo() async {
    await _ensureInitialized();

    return {
      'swipeCountSinceLastAd': _swipeCountSinceLastAd,
      'adFrequency': AdMobConfig.adFrequency,
      'swipesUntilNextAd': await getSwipesUntilNextAd(),
      'shouldShowAds': await _shouldShowAds(),
      'isInitialized': _isInitialized,
    };
  }

  /// Reset all data (for testing purposes)
  static Future<void> resetAllData() async {
    await _ensureInitialized();

    _swipeCountSinceLastAd = 0;
    await _saveSwipeCountToStorage();

    if (kDebugMode) {
      print('Ad Frequency Service: All data reset');
    }
  }
}
