import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../services/localization_service.dart';
import '../generated/l10n/app_localizations.dart';

class LanguagePicker extends StatefulWidget {
  final Function(String)? onLanguageChanged;
  final bool showAsBottomSheet;

  const LanguagePicker({
    super.key,
    this.onLanguageChanged,
    this.showAsBottomSheet = false,
  });

  @override
  State<LanguagePicker> createState() => _LanguagePickerState();
}

class _LanguagePickerState extends State<LanguagePicker>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  String _selectedLanguage = LocalizationService().currentLanguageCode;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _fadeController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  Future<void> _selectLanguage(String languageCode) async {
    if (_isLoading || languageCode == _selectedLanguage) return;

    setState(() {
      _isLoading = true;
      _selectedLanguage = languageCode;
    });

    try {
      await LocalizationService().setLanguage(languageCode);

      if (widget.onLanguageChanged != null) {
        widget.onLanguageChanged!(languageCode);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              AppLocalizations.of(context).languageUpdatedSuccessfully,
              style: GoogleFonts.instrumentSans(color: Colors.white),
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );

        // Close the picker if it's shown as bottom sheet
        if (widget.showAsBottomSheet) {
          Navigator.of(context).pop();
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Failed to update language: ${e.toString()}',
              style: GoogleFonts.instrumentSans(color: Colors.white),
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.showAsBottomSheet) {
      return _buildBottomSheet();
    } else {
      return _buildFullScreen();
    }
  }

  Widget _buildFullScreen() {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F9FC),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Color(0xFF0D76FF)),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          AppLocalizations.of(context).selectLanguage,
          style: GoogleFonts.instrumentSans(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF1A1A1A),
          ),
        ),
      ),
      body: SafeArea(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: _buildLanguageList(),
          ),
        ),
      ),
    );
  }

  Widget _buildBottomSheet() {
    return Container(
      decoration: const BoxDecoration(
        color: Color(0xFFF7F9FC),
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.only(top: 12),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Title
          Padding(
            padding: const EdgeInsets.all(20),
            child: Text(
              AppLocalizations.of(context).selectLanguage,
              style: GoogleFonts.instrumentSans(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF1A1A1A),
              ),
            ),
          ),

          // Language list
          Flexible(
            child: _buildLanguageList(),
          ),

          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildLanguageList() {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!widget.showAsBottomSheet) ...[
            Text(
              AppLocalizations.of(context).chooseYourPreferredLanguage,
              style: GoogleFonts.instrumentSans(
                fontSize: 16,
                color: const Color(0xFF666666),
              ),
            ),
            const SizedBox(height: 24),
          ],
          ...LocalizationService.supportedLanguages.entries.map((entry) {
            final languageCode = entry.key;
            final languageInfo = entry.value;
            final isSelected = languageCode == _selectedLanguage;
            final isRTL = LocalizationService.isRTL(languageCode);

            return _buildLanguageItem(
              languageCode: languageCode,
              nativeName: languageInfo['nativeName']!,
              englishName: languageInfo['name']!,
              flag: languageInfo['flag']!,
              isSelected: isSelected,
              isRTL: isRTL,
            );
          }).toList(),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildLanguageItem({
    required String languageCode,
    required String nativeName,
    required String englishName,
    required String flag,
    required bool isSelected,
    required bool isRTL,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _isLoading ? null : () => _selectLanguage(languageCode),
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: isSelected
                  ? const Color(0xFF0D76FF).withOpacity(0.1)
                  : const Color(0xFFF7F9FC),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isSelected ? const Color(0xFF0D76FF) : Colors.grey[200]!,
                width: isSelected ? 2 : 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                // Flag
                Text(
                  flag,
                  style: const TextStyle(fontSize: 24),
                ),

                const SizedBox(width: 16),

                // Language names
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        nativeName,
                        style: GoogleFonts.instrumentSans(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: isSelected
                              ? const Color(0xFF0D76FF)
                              : const Color(0xFF1A1A1A),
                        ),
                        textDirection:
                            isRTL ? TextDirection.rtl : TextDirection.ltr,
                      ),
                      if (nativeName != englishName) ...[
                        const SizedBox(height: 2),
                        Text(
                          englishName,
                          style: GoogleFonts.instrumentSans(
                            fontSize: 14,
                            color: const Color(0xFF666666),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),

                // Selection indicator
                if (isSelected) ...[
                  const SizedBox(width: 12),
                  if (_isLoading && languageCode == _selectedLanguage)
                    const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor:
                            AlwaysStoppedAnimation<Color>(Color(0xFF0D76FF)),
                      ),
                    )
                  else
                    const Icon(
                      Icons.check_circle,
                      color: Color(0xFF0D76FF),
                      size: 20,
                    ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// Show language picker as a bottom sheet
void showLanguagePickerBottomSheet(
  BuildContext context, {
  Function(String)? onLanguageChanged,
}) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder: (context) => LanguagePicker(
      showAsBottomSheet: true,
      onLanguageChanged: onLanguageChanged,
    ),
  );
}
