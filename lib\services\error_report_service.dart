import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';
import '../models/error_report.dart';
import '../services/auth_service.dart';
import '../services/google_sheets_service.dart';
import 'package:googleapis/sheets/v4.dart' as sheets;

class ErrorReportService {
  static const String _cacheKey = 'error_reports_cache';
  static const String _spreadsheetId = '18_dYxomtS1cjaKi6hWTiNHuELOcjnunf-2qOotPML4s';
  static const String _sheetName = 'Report';
  
  static List<ErrorReport> _reportCache = [];
  static bool _isInitialized = false;

  /// Initialize the error report service
  static Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      await _loadReportsFromStorage();
      _isInitialized = true;
      
      if (kDebugMode) {
        print('Error Report Service: Initialized with ${_reportCache.length} cached reports');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error Report Service: Initialization failed - $e');
      }
    }
  }

  /// Ensure service is initialized
  static Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  /// Submit an error report
  static Future<bool> submitErrorReport({
    required String category,
    required String name,
    required String email,
    required String description,
    String? screenshot,
  }) async {
    try {
      await _ensureInitialized();

      // Get device and app info
      final deviceInfo = await _getDeviceInfo();
      final appVersion = await _getAppVersion();
      final userId = AuthService.currentUser?.id ?? 'anonymous';

      // Create error report object
      final report = ErrorReport(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: userId,
        category: category,
        name: name,
        email: email,
        description: description,
        screenshot: screenshot,
        timestamp: DateTime.now(),
        deviceInfo: deviceInfo,
        appVersion: appVersion,
        isSynced: false,
      );

      // Try to submit directly to Google Sheets
      bool sheetsSuccess = false;
      if (GoogleSheetsService.isReady) {
        sheetsSuccess = await _submitToGoogleSheets(report);
        if (sheetsSuccess) {
          report.copyWith(isSynced: true);
          if (kDebugMode) {
            print('Error Report Service: Report submitted to Google Sheets - ${report.id}');
          }
        }
      }

      // Store locally as backup (always store, regardless of Sheets success)
      _reportCache.add(report);
      await _saveReportsToStorage();
      if (kDebugMode) {
        print('Error Report Service: Report stored locally as backup - ${report.id}');
      }

      // If Google Sheets failed, try to sync in background
      if (!sheetsSuccess) {
        if (kDebugMode) {
          print('Error Report Service: Google Sheets submission failed, will retry in background');
        }
        _syncReportsInBackground();
      }

      return true; // Return true if at least local storage succeeded
    } catch (e) {
      if (kDebugMode) {
        print('Error Report Service: Failed to submit error report - $e');
      }
      return false;
    }
  }

  /// Submit report to Google Sheets
  static Future<bool> _submitToGoogleSheets(ErrorReport report) async {
    try {
      // Ensure headers exist
      await _ensureReportHeaders();
      
      final row = report.toCsvRow();
      
      if (kDebugMode) {
        print('Error Report Service: Submitting report with data: ${row.take(3)}...');
        print('Error Report Service: Target range: $_sheetName!A:K');
      }

      final valueRange = sheets.ValueRange(
        values: [row],
      );

      final response = await GoogleSheetsService.sheetsApi!.spreadsheets.values.append(
        valueRange,
        _spreadsheetId,
        '$_sheetName!A:K',
        valueInputOption: 'RAW',
        insertDataOption: 'INSERT_ROWS',
      );

      if (kDebugMode) {
        print('Error Report Service: Report submitted successfully - ${report.id}');
        print('Error Report Service: Response: ${response.updates?.updatedRows} rows updated');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error Report Service: Failed to submit report to Google Sheets: $e');
        if (e.toString().contains('PERMISSION_DENIED')) {
          print('Error Report Service: Permission denied - check service account access to spreadsheet');
        }
        if (e.toString().contains('NOT_FOUND')) {
          print('Error Report Service: Sheet not found - check sheet name "$_sheetName"');
        }
      }
      return false;
    }
  }

  /// Ensure headers exist in the Reports sheet
  static Future<void> _ensureReportHeaders() async {
    if (GoogleSheetsService.sheetsApi == null) return;

    try {
      // Check if first row has headers
      final response = await GoogleSheetsService.sheetsApi!.spreadsheets.values.get(
        _spreadsheetId,
        '$_sheetName!A1:K1',
      );

      if (response.values == null || response.values!.isEmpty) {
        await _addReportHeaders();
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error Report Service: Failed to check headers: $e');
      }
      // Try to add headers anyway
      await _addReportHeaders();
    }
  }

  /// Add headers to the Reports sheet
  static Future<void> _addReportHeaders() async {
    if (GoogleSheetsService.sheetsApi == null) return;

    try {
      final headers = [
        'ID',
        'User ID',
        'Category',
        'Name',
        'Email',
        'Description',
        'Screenshot',
        'Timestamp',
        'Device Info',
        'App Version',
        'Is Synced',
      ];

      final valueRange = sheets.ValueRange(
        values: [headers],
      );

      await GoogleSheetsService.sheetsApi!.spreadsheets.values.update(
        valueRange,
        _spreadsheetId,
        '$_sheetName!A1:K1',
        valueInputOption: 'RAW',
      );

      if (kDebugMode) {
        print('Error Report Service: Headers added to Reports sheet');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error Report Service: Failed to add headers: $e');
      }
    }
  }

  /// Get device information
  static Future<String> _getDeviceInfo() async {
    try {
      final deviceInfo = DeviceInfoPlugin();
      
      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        return 'Android ${androidInfo.version.release} - ${androidInfo.model}';
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        return 'iOS ${iosInfo.systemVersion} - ${iosInfo.model}';
      } else {
        return 'Unknown Platform';
      }
    } catch (e) {
      return 'Device info unavailable';
    }
  }

  /// Get app version
  static Future<String> _getAppVersion() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      return '${packageInfo.version} (${packageInfo.buildNumber})';
    } catch (e) {
      return 'Version unavailable';
    }
  }

  /// Load reports from local storage
  static Future<void> _loadReportsFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final reportsJson = prefs.getString(_cacheKey);
      
      if (reportsJson != null) {
        final List<dynamic> reportsList = json.decode(reportsJson);
        _reportCache = reportsList
            .map((json) => ErrorReport.fromJson(json))
            .toList();
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error Report Service: Failed to load reports from storage - $e');
      }
      _reportCache = [];
    }
  }

  /// Save reports to local storage
  static Future<void> _saveReportsToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final reportsJson = json.encode(
        _reportCache.map((report) => report.toJson()).toList(),
      );
      await prefs.setString(_cacheKey, reportsJson);
    } catch (e) {
      if (kDebugMode) {
        print('Error Report Service: Failed to save reports to storage - $e');
      }
    }
  }

  /// Sync reports in background
  static void _syncReportsInBackground() {
    Future.delayed(const Duration(seconds: 5), () async {
      await syncReportsToSheets();
    });
  }

  /// Sync unsynced reports to Google Sheets
  static Future<bool> syncReportsToSheets() async {
    try {
      await _ensureInitialized();

      // Get unsynced reports
      final unsyncedReports = _reportCache.where((report) => !report.isSynced).toList();

      if (unsyncedReports.isEmpty) {
        if (kDebugMode) {
          print('Error Report Service: No unsynced reports to upload');
        }
        return true;
      }

      if (kDebugMode) {
        print('Error Report Service: Syncing ${unsyncedReports.length} reports');
      }

      // Check if Google Sheets service is ready
      if (!GoogleSheetsService.isReady) {
        if (kDebugMode) {
          print('Error Report Service: Google Sheets service not ready');
        }
        return false;
      }

      // Submit each report individually
      bool allSuccess = true;
      for (final report in unsyncedReports) {
        final success = await _submitToGoogleSheets(report);
        if (success) {
          // Update the report in cache as synced
          final index = _reportCache.indexWhere((r) => r.id == report.id);
          if (index != -1) {
            _reportCache[index] = report.copyWith(isSynced: true);
          }
        } else {
          allSuccess = false;
        }
      }

      if (allSuccess) {
        await _saveReportsToStorage();
        if (kDebugMode) {
          print('Error Report Service: Successfully synced ${unsyncedReports.length} reports to Google Sheets');
        }
      } else {
        if (kDebugMode) {
          print('Error Report Service: Some reports failed to sync to Google Sheets');
        }
      }

      return allSuccess;
    } catch (e) {
      if (kDebugMode) {
        print('Error Report Service: Sync failed - $e');
      }
      return false;
    }
  }

  /// Get all error reports
  static Future<List<ErrorReport>> getAllReports() async {
    await _ensureInitialized();
    return List.from(_reportCache);
  }

  /// Get unsynced reports count
  static Future<int> getUnsyncedReportsCount() async {
    await _ensureInitialized();
    return _reportCache.where((report) => !report.isSynced).length;
  }
}
