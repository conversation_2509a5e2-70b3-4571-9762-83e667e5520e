import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../services/auth_service.dart';

class DebugGoogleSignInScreen extends StatefulWidget {
  const DebugGoogleSignInScreen({super.key});

  @override
  State<DebugGoogleSignInScreen> createState() =>
      _DebugGoogleSignInScreenState();
}

class _DebugGoogleSignInScreenState extends State<DebugGoogleSignInScreen> {
  Map<String, dynamic>? _status;
  bool _isLoading = false;
  String? _testResult;

  @override
  void initState() {
    super.initState();
    _checkStatus();
  }

  Future<void> _checkStatus() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final isSignedIn = await AuthService.isGoogleSignedIn();
      final currentUser = AuthService.currentUser;

      setState(() {
        _status = {
          'isSignedIn': isSignedIn,
          'hasCurrentUser': currentUser != null,
          'userEmail': currentUser?.email ?? 'Not available',
          'isAnonymous': currentUser?.isAnonymous ?? false,
          'googleConfiguration':
              'Using direct client IDs (no config files needed)',
          'androidClientId':
              '502730429222-1h0pvgq6b45okjjoi0ob9q6d3acuucsl.apps.googleusercontent.com',
          'webClientId':
              '502730429222-1h0pvgq6b45okjjoi0ob9q6d3acuucsl.apps.googleusercontent.com',
        };
      });
    } catch (error) {
      setState(() {
        _status = {'error': error.toString()};
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testGoogleSignIn() async {
    setState(() {
      _isLoading = true;
      _testResult = null;
    });

    try {
      final response = await AuthService.signInWithGoogle();
      setState(() {
        _testResult = response != null
            ? 'SUCCESS: Google Sign-In completed successfully!'
            : 'CANCELLED: User cancelled the sign-in process';
      });
    } catch (error) {
      setState(() {
        _testResult = 'ERROR: ${error.toString()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _validateConfiguration() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Simple validation - check if we can get sign-in status
      final isSignedIn = await AuthService.isGoogleSignedIn();
      setState(() {
        _testResult =
            'VALID: Google Sign-In is properly configured with client IDs. '
            'Current status: ${isSignedIn ? "Signed in" : "Not signed in"}';
      });
    } catch (error) {
      setState(() {
        _testResult = 'ERROR: ${error.toString()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F9FC),
      appBar: AppBar(
        backgroundColor: const Color(0xFFF7F9FC),
        elevation: 0,
        title: Text(
          'OAuth Debug Tools',
          style: GoogleFonts.instrumentSans(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: const Color(0xFF2D3748),
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Color(0xFF2D3748)),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Status Card
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Configuration Status',
                            style: GoogleFonts.instrumentSans(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: const Color(0xFF2D3748),
                            ),
                          ),
                          IconButton(
                            onPressed: _checkStatus,
                            icon: const Icon(Icons.refresh),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      if (_isLoading)
                        const Center(child: CircularProgressIndicator())
                      else if (_status != null)
                        for (final entry in _status!.entries)
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 4.0),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                SizedBox(
                                  width: 120,
                                  child: Text(
                                    '${entry.key}:',
                                    style: GoogleFonts.instrumentSans(
                                      fontWeight: FontWeight.w600,
                                      color: const Color(0xFF4A5568),
                                    ),
                                  ),
                                ),
                                Expanded(
                                  child: Text(
                                    entry.value.toString(),
                                    style: GoogleFonts.instrumentSans(
                                      color: const Color(0xFF2D3748),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Test Buttons
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _validateConfiguration,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF0D76FF),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: Text(
                    'Validate Configuration',
                    style: GoogleFonts.instrumentSans(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 12),

              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _testGoogleSignIn,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF48BB78),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: Text(
                    'Test Google Sign-In',
                    style: GoogleFonts.instrumentSans(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Test Result
              if (_testResult != null)
                Card(
                  color: _testResult!.startsWith('SUCCESS')
                      ? Colors.green.shade50
                      : _testResult!.startsWith('ERROR')
                          ? Colors.red.shade50
                          : Colors.orange.shade50,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Test Result',
                          style: GoogleFonts.instrumentSans(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: const Color(0xFF2D3748),
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          _testResult!,
                          style: GoogleFonts.instrumentSans(
                            color: const Color(0xFF2D3748),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

              const Spacer(),

              // Instructions
              Card(
                color: Colors.blue.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Setup Instructions',
                        style: GoogleFonts.instrumentSans(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: const Color(0xFF2D3748),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'OAuth Authentication Debug Tools:\n\n'
                        'Google Sign-In:\n'
                        '• Uses direct client IDs (no config files needed)\n'
                        '• No Firebase setup required\n'
                        '• Works directly with Supabase\n\n'
                        'If you encounter issues:\n'
                        '1. Check your internet connection\n'
                        '2. Verify OAuth provider configuration in Supabase\n'
                        '3. Ensure callback URLs match exactly\n'
                        '4. Check Google OAuth setup in Supabase dashboard',
                        style: GoogleFonts.instrumentSans(
                          color: const Color(0xFF4A5568),
                          height: 1.4,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
