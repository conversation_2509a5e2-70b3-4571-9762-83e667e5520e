import 'dart:convert';
import '../services/google_search_service.dart';

class ChatSession {
  String id;
  String title;
  DateTime createdAt;
  DateTime updatedAt;
  List<ChatMessageModel> messages;
  bool isActive;

  ChatSession({
    required this.id,
    required this.title,
    required this.createdAt,
    required this.updatedAt,
    required this.messages,
    this.isActive = false,
  });

  /// Generate a title from the first user message
  static String generateTitle(String firstMessage) {
    // Clean the message and limit length
    String cleanMessage = firstMessage.trim();

    // Remove common prefixes
    final prefixes = [
      'can you',
      'could you',
      'please',
      'help me',
      'i want to',
      'i need to'
    ];
    for (String prefix in prefixes) {
      if (cleanMessage.toLowerCase().startsWith(prefix)) {
        cleanMessage = cleanMessage.substring(prefix.length).trim();
        break;
      }
    }

    // Capitalize first letter
    if (cleanMessage.isNotEmpty) {
      cleanMessage = cleanMessage[0].toUpperCase() + cleanMessage.substring(1);
    }

    // Limit to 50 characters and add ellipsis if needed
    if (cleanMessage.length > 50) {
      cleanMessage = '${cleanMessage.substring(0, 47)}...';
    }

    return cleanMessage.isNotEmpty ? cleanMessage : 'New Chat';
  }

  /// Get formatted date string
  String get formattedDate {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final chatDate = DateTime(createdAt.year, createdAt.month, createdAt.day);

    if (chatDate == today) {
      return 'Today';
    } else if (chatDate == yesterday) {
      return 'Yesterday';
    } else {
      return '${createdAt.month.toString().padLeft(2, '0')}/${createdAt.day.toString().padLeft(2, '0')}/${createdAt.year}';
    }
  }

  /// Get the last message timestamp
  DateTime get lastMessageTime {
    if (messages.isEmpty) return createdAt;
    return messages.last.timestamp;
  }

  /// Update the session's updated timestamp
  void updateTimestamp() {
    updatedAt = DateTime.now();
  }

  /// Add a message to the session
  void addMessage(ChatMessageModel message) {
    messages.add(message);
    updateTimestamp();
  }

  /// Convert to the existing ChatMessage format for compatibility
  List<dynamic> toChatMessages() {
    return messages
        .map((msg) => {
              'text': msg.text,
              'isUser': msg.isUser,
              'timestamp': msg.timestamp,
              'imagePath': msg.imagePath,
              'isImageMessage': msg.isImageMessage,
            })
        .toList();
  }
}

class ChatMessageModel {
  String id;
  String text;
  bool isUser;
  DateTime timestamp;
  String? imagePath;
  bool isImageMessage;
  bool isWebSearchMessage;
  String? searchQuery;
  String? searchResultData; // JSON string of search results
  bool? hasUserFeedback; // Whether user has provided feedback for this message
  String? feedbackType; // 'like' or 'dislike'
  String? feedbackId; // ID of the feedback entry

  ChatMessageModel({
    required this.id,
    required this.text,
    required this.isUser,
    required this.timestamp,
    this.imagePath,
    this.isImageMessage = false,
    this.isWebSearchMessage = false,
    this.searchQuery,
    this.searchResultData,
    this.hasUserFeedback,
    this.feedbackType,
    this.feedbackId,
  });

  /// Create from a map (to avoid circular dependency)
  factory ChatMessageModel.fromMap(Map<String, dynamic> map) {
    return ChatMessageModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      text: map['text'] as String,
      isUser: map['isUser'] as bool,
      timestamp: map['timestamp'] as DateTime,
      imagePath: map['imagePath'] as String?,
      isImageMessage: map['isImageMessage'] as bool? ?? false,
      isWebSearchMessage: map['isWebSearchMessage'] as bool? ?? false,
      searchQuery: map['searchQuery'] as String?,
      searchResultData: map['searchResultData'] as String?,
      hasUserFeedback: map['hasUserFeedback'] as bool?,
      feedbackType: map['feedbackType'] as String?,
      feedbackId: map['feedbackId'] as String?,
    );
  }

  /// Convert to map format
  Map<String, dynamic> toMap() {
    return {
      'text': text,
      'isUser': isUser,
      'timestamp': timestamp,
      'imagePath': imagePath,
      'isImageMessage': isImageMessage,
      'isWebSearchMessage': isWebSearchMessage,
      'searchQuery': searchQuery,
      'searchResultData': searchResultData,
      'hasUserFeedback': hasUserFeedback,
      'feedbackType': feedbackType,
      'feedbackId': feedbackId,
    };
  }

  /// Helper method to serialize GoogleSearchResult to JSON string
  static String? serializeSearchResult(GoogleSearchResult? searchResult) {
    if (searchResult == null) return null;

    try {
      final Map<String, dynamic> data = {
        'items': searchResult.items
            .map((item) => {
                  'title': item.title,
                  'link': item.link,
                  'snippet': item.snippet,
                  'displayLink': item.displayLink,
                })
            .toList(),
        'searchInformation': searchResult.searchInformation,
        'totalResults': searchResult.totalResults,
      };
      return jsonEncode(data);
    } catch (e) {
      return null;
    }
  }

  /// Helper method to deserialize JSON string to GoogleSearchResult
  static GoogleSearchResult? deserializeSearchResult(String? jsonData) {
    if (jsonData == null || jsonData.isEmpty) return null;

    try {
      final Map<String, dynamic> data = jsonDecode(jsonData);
      return GoogleSearchResult(
        items: (data['items'] as List<dynamic>?)
                ?.map((item) => GoogleSearchItem(
                      title: item['title'] ?? '',
                      link: item['link'] ?? '',
                      snippet: item['snippet'] ?? '',
                      displayLink: item['displayLink'],
                    ))
                .toList() ??
            [],
        searchInformation: data['searchInformation'],
        totalResults: data['totalResults'] ?? 0,
      );
    } catch (e) {
      return null;
    }
  }
}
