import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:tripwisego/screens/profile_screen.dart';
import 'package:tripwisego/config/supabase_config.dart';

void main() {
  group('ProfileScreen Tests', () {
    setUpAll(() async {
      // Initialize Supabase for testing
      try {
        await SupabaseConfig.initialize();
      } catch (e) {
        // Continue with tests even if Supabase initialization fails
        print('Supabase initialization failed in tests: $e');
      }
    });

    testWidgets('ProfileScreen should build without errors',
        (WidgetTester tester) async {
      // Build the ProfileScreen widget
      await tester.pumpWidget(
        const MaterialApp(
          home: ProfileScreen(),
        ),
      );

      // Wait for animations to complete
      await tester.pumpAndSettle();

      // Verify that the profile screen is displayed
      expect(find.text('Profile'), findsOneWidget);

      // Verify that key UI elements are present
      expect(find.byType(Container), findsWidgets);
      expect(find.text('Log out'), findsOneWidget);
      expect(find.text('Support & Feedback'), findsOneWidget);
    });

    testWidgets(
        'ProfileScreen should show guest user information for anonymous users',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: ProfileScreen(),
        ),
      );

      await tester.pumpAndSettle();

      // For guest users, we should see guest-specific text
      // Note: This test assumes no authenticated user, so it should show guest info
      expect(find.textContaining('Guest'), findsWidgets);
      expect(
          find.text(
              'Profile editing is not available for guest users. Sign up to customize your profile!'),
          findsOneWidget);
    });

    testWidgets('ProfileScreen should handle edit mode properly',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: ProfileScreen(),
        ),
      );

      await tester.pumpAndSettle();

      // For guest users, edit button should not be available
      expect(find.text('Edit Profile'), findsNothing);

      // Should show guest message instead
      expect(find.textContaining('Profile editing is not available'),
          findsOneWidget);
    });

    testWidgets('ProfileScreen should have proper navigation structure',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: ProfileScreen(),
        ),
      );

      await tester.pumpAndSettle();

      // Check for menu items
      expect(find.text('Plan'), findsOneWidget);
      expect(find.text('Terms of Service'), findsOneWidget);
      expect(find.text('Language'), findsOneWidget);
      expect(find.text('Privacy Policy'), findsOneWidget);
      expect(find.text('Version Information'), findsOneWidget);
      expect(find.text('Log out'), findsOneWidget);
    });

    testWidgets('ProfileScreen should have support section',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: ProfileScreen(),
        ),
      );

      await tester.pumpAndSettle();

      // Check for support section
      expect(find.text('Support & Feedback'), findsOneWidget);
      expect(find.text('Leave a Review'), findsOneWidget);
      expect(find.text('Feature Request'), findsOneWidget);
      expect(find.text('How can we help you?'), findsOneWidget);
    });

    testWidgets('ProfileScreen should handle logout dialog',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: ProfileScreen(),
        ),
      );

      await tester.pumpAndSettle();

      // Tap on logout
      await tester.tap(find.text('Log out'));
      await tester.pumpAndSettle();

      // Should show logout dialog
      expect(find.byType(AlertDialog), findsOneWidget);
      expect(find.text('Cancel'), findsOneWidget);
    });

    testWidgets('ProfileScreen should have proper styling and colors',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: ProfileScreen(),
        ),
      );

      await tester.pumpAndSettle();

      // Check that the scaffold has the correct background color
      final scaffold = tester.widget<Scaffold>(find.byType(Scaffold));
      expect(scaffold.backgroundColor, const Color(0xFFF7F9FC));
    });
  });
}
