import 'dart:math';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';
import '../data/destinations_data.dart';

class DestinationRandomizationService {
  static const String _lastRandomizationKey = 'last_destination_randomization';
  static const String _randomizedOrderKey = 'randomized_destination_order';
  static const String _swipeProgressKey = 'destination_swipe_progress';
  static const String _swipeProgressDateKey = 'destination_swipe_progress_date';
  static List<Map<String, dynamic>>? _cachedRandomizedDestinations;
  static int? _cachedSwipeProgress;

  /// Get destinations with daily randomization
  static Future<List<Map<String, dynamic>>> getRandomizedDestinations() async {
    try {
      // Always try to load from cache first
      if (_cachedRandomizedDestinations != null &&
          _cachedRandomizedDestinations!.isNotEmpty) {
        if (kDebugMode) {
          print(
              'Destination Randomization Service: Using cached destinations (${_cachedRandomizedDestinations!.length} items)');
        }
        return _cachedRandomizedDestinations!;
      }

      if (kDebugMode) {
        print(
            'Destination Randomization Service: Cache empty, loading from storage or randomizing');
      }

      // Try to load existing randomized order from storage first
      await _loadRandomizedOrder();

      // If we successfully loaded from storage, return it
      if (_cachedRandomizedDestinations != null &&
          _cachedRandomizedDestinations!.isNotEmpty) {
        if (kDebugMode) {
          print(
              'Destination Randomization Service: Loaded from storage (${_cachedRandomizedDestinations!.length} items)');
        }
        return _cachedRandomizedDestinations!;
      }

      // Only randomize if we don't have cached data and it's a new day
      final shouldRandomize = await _shouldRandomizeToday();
      if (shouldRandomize) {
        if (kDebugMode) {
          print('Destination Randomization Service: Randomizing for new day');
        }
        await _randomizeDestinations();
      }

      return _cachedRandomizedDestinations ??
          DestinationsData.getDestinationsOriginalOrder();
    } catch (e) {
      if (kDebugMode) {
        print(
            'Destination Randomization Service: Error getting randomized destinations - $e');
      }
      // Fallback to original order if randomization fails
      return DestinationsData.getDestinationsOriginalOrder();
    }
  }

  /// Check if we should randomize destinations today
  static Future<bool> _shouldRandomizeToday() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastRandomizationString = prefs.getString(_lastRandomizationKey);

      if (lastRandomizationString == null) {
        // Never randomized before
        return true;
      }

      final lastRandomization = DateTime.parse(lastRandomizationString);
      final now = DateTime.now();

      // Check if it's a new day (different date)
      final isNewDay = lastRandomization.year != now.year ||
          lastRandomization.month != now.month ||
          lastRandomization.day != now.day;

      if (kDebugMode) {
        print(
            'Destination Randomization Service: Last randomization: $lastRandomizationString, Is new day: $isNewDay');
      }

      return isNewDay;
    } catch (e) {
      if (kDebugMode) {
        print(
            'Destination Randomization Service: Error checking randomization date - $e');
      }
      return true; // Default to randomizing if there's an error
    }
  }

  /// Randomize destinations and save the order
  static Future<void> _randomizeDestinations() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final originalDestinations =
          DestinationsData.getDestinationsOriginalOrder();

      // Create a copy and shuffle it
      final randomizedDestinations =
          List<Map<String, dynamic>>.from(originalDestinations);
      randomizedDestinations.shuffle(Random());

      // Cache the randomized list
      _cachedRandomizedDestinations = randomizedDestinations;

      // Save the randomized order (just the IDs to save space)
      final randomizedIds = randomizedDestinations
          .map((dest) => dest['id']?.toString() ?? '')
          .where((id) => id.isNotEmpty)
          .toList();

      await prefs.setStringList(_randomizedOrderKey, randomizedIds);
      await prefs.setString(
          _lastRandomizationKey, DateTime.now().toIso8601String());

      if (kDebugMode) {
        print(
            'Destination Randomization Service: Randomized ${randomizedDestinations.length} destinations');
        print(
            'Destination Randomization Service: First destination is now: ${randomizedDestinations.first['name']}');
      }
    } catch (e) {
      if (kDebugMode) {
        print(
            'Destination Randomization Service: Error randomizing destinations - $e');
      }
      // Fallback to original order
      _cachedRandomizedDestinations =
          DestinationsData.getDestinationsOriginalOrder();
    }
  }

  /// Load previously randomized order from storage
  static Future<void> _loadRandomizedOrder() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final randomizedIds = prefs.getStringList(_randomizedOrderKey);

      if (randomizedIds == null || randomizedIds.isEmpty) {
        return;
      }

      final originalDestinations =
          DestinationsData.getDestinationsOriginalOrder();
      final randomizedDestinations = <Map<String, dynamic>>[];

      // Reconstruct the randomized order
      for (final id in randomizedIds) {
        final destination = originalDestinations.firstWhere(
          (dest) => dest['id']?.toString() == id,
          orElse: () => <String, dynamic>{},
        );
        if (destination.isNotEmpty) {
          randomizedDestinations.add(destination);
        }
      }

      // Add any destinations that might be missing (new destinations added)
      for (final destination in originalDestinations) {
        final id = destination['id']?.toString() ?? '';
        if (id.isNotEmpty && !randomizedIds.contains(id)) {
          randomizedDestinations.add(destination);
        }
      }

      _cachedRandomizedDestinations = randomizedDestinations;

      if (kDebugMode) {
        print(
            'Destination Randomization Service: Loaded randomized order with ${randomizedDestinations.length} destinations');
      }
    } catch (e) {
      if (kDebugMode) {
        print(
            'Destination Randomization Service: Error loading randomized order - $e');
      }
    }
  }

  /// Initialize the service
  static Future<void> initialize() async {
    try {
      if (kDebugMode) {
        print('Destination Randomization Service: Starting initialization');
      }

      // Try to load existing randomized order from storage first
      await _loadRandomizedOrder();

      // If we have cached data, we're done
      if (_cachedRandomizedDestinations != null &&
          _cachedRandomizedDestinations!.isNotEmpty) {
        if (kDebugMode) {
          print(
              'Destination Randomization Service: Initialized with cached data (${_cachedRandomizedDestinations!.length} items)');
        }
        return;
      }

      // Only check for randomization if we don't have cached data
      final shouldRandomize = await _shouldRandomizeToday();
      if (shouldRandomize) {
        if (kDebugMode) {
          print(
              'Destination Randomization Service: No cached data, randomizing for today');
        }
        await _randomizeDestinations();
      }

      if (kDebugMode) {
        final count = _cachedRandomizedDestinations?.length ?? 0;
        print(
            'Destination Randomization Service: Initialized with $count destinations');
      }
    } catch (e) {
      if (kDebugMode) {
        print(
            'Destination Randomization Service: Error during initialization - $e');
      }
    }
  }

  /// Check if the service is properly initialized with cached data
  static bool isInitialized() {
    return _cachedRandomizedDestinations != null &&
        _cachedRandomizedDestinations!.isNotEmpty;
  }

  /// Force randomization (for testing purposes)
  static Future<void> forceRandomization() async {
    await _randomizeDestinations();
  }

  /// Clear randomization data (for testing purposes)
  static Future<void> clearRandomizationData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_lastRandomizationKey);
      await prefs.remove(_randomizedOrderKey);
      _cachedRandomizedDestinations = null;

      if (kDebugMode) {
        print('Destination Randomization Service: Cleared randomization data');
      }
    } catch (e) {
      if (kDebugMode) {
        print(
            'Destination Randomization Service: Error clearing randomization data - $e');
      }
    }
  }

  /// Get the last randomization date
  static Future<DateTime?> getLastRandomizationDate() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastRandomizationString = prefs.getString(_lastRandomizationKey);

      if (lastRandomizationString != null) {
        return DateTime.parse(lastRandomizationString);
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print(
            'Destination Randomization Service: Error getting last randomization date - $e');
      }
      return null;
    }
  }

  /// Save swipe progress for the current day
  static Future<void> saveSwipeProgress(int swipeIndex) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final today =
          DateTime.now().toIso8601String().split('T')[0]; // YYYY-MM-DD format

      await prefs.setInt(_swipeProgressKey, swipeIndex);
      await prefs.setString(_swipeProgressDateKey, today);
      _cachedSwipeProgress = swipeIndex;

      if (kDebugMode) {
        print(
            'Destination Randomization Service: Saved swipe progress: $swipeIndex for date: $today');
      }
    } catch (e) {
      if (kDebugMode) {
        print(
            'Destination Randomization Service: Error saving swipe progress - $e');
      }
    }
  }

  /// Get swipe progress for the current day
  static Future<int> getSwipeProgress() async {
    try {
      if (_cachedSwipeProgress != null) {
        return _cachedSwipeProgress!;
      }

      final prefs = await SharedPreferences.getInstance();
      final today =
          DateTime.now().toIso8601String().split('T')[0]; // YYYY-MM-DD format
      final savedDate = prefs.getString(_swipeProgressDateKey);

      // Only return saved progress if it's from today
      if (savedDate == today) {
        final progress = prefs.getInt(_swipeProgressKey) ?? 0;
        _cachedSwipeProgress = progress;

        if (kDebugMode) {
          print(
              'Destination Randomization Service: Loaded swipe progress: $progress for today');
        }

        return progress;
      } else {
        // Different day, reset progress
        if (kDebugMode) {
          print(
              'Destination Randomization Service: New day detected, resetting swipe progress');
        }
        await _resetSwipeProgress();
        return 0;
      }
    } catch (e) {
      if (kDebugMode) {
        print(
            'Destination Randomization Service: Error getting swipe progress - $e');
      }
      return 0;
    }
  }

  /// Reset swipe progress (called when starting a new day)
  static Future<void> _resetSwipeProgress() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_swipeProgressKey);
      await prefs.remove(_swipeProgressDateKey);
      _cachedSwipeProgress = null;

      if (kDebugMode) {
        print('Destination Randomization Service: Reset swipe progress');
      }
    } catch (e) {
      if (kDebugMode) {
        print(
            'Destination Randomization Service: Error resetting swipe progress - $e');
      }
    }
  }

  /// Get the full randomized destination list and current progress index
  static Future<({List<Map<String, dynamic>> destinations, int progressIndex})>
      getDestinationsWithProgress() async {
    try {
      final allDestinations = await getRandomizedDestinations();
      final swipeProgress = await getSwipeProgress();

      if (kDebugMode) {
        print(
            'Destination Randomization Service: Returning ${allDestinations.length} destinations with progress index: $swipeProgress');
      }

      return (destinations: allDestinations, progressIndex: swipeProgress);
    } catch (e) {
      if (kDebugMode) {
        print(
            'Destination Randomization Service: Error getting destinations with progress - $e');
      }
      final fallbackDestinations = await getRandomizedDestinations();
      return (destinations: fallbackDestinations, progressIndex: 0);
    }
  }

  /// Get destinations starting from the user's current progress (legacy method)
  static Future<List<Map<String, dynamic>>>
      getDestinationsFromProgress() async {
    try {
      final result = await getDestinationsWithProgress();
      final allDestinations = result.destinations;
      final swipeProgress = result.progressIndex;

      if (swipeProgress > 0 && swipeProgress < allDestinations.length) {
        final remainingDestinations = allDestinations.sublist(swipeProgress);

        if (kDebugMode) {
          print(
              'Destination Randomization Service: Returning ${remainingDestinations.length} destinations starting from index $swipeProgress');
        }

        return remainingDestinations;
      }

      if (kDebugMode) {
        print(
            'Destination Randomization Service: Returning all ${allDestinations.length} destinations (no progress or progress complete)');
      }

      return allDestinations;
    } catch (e) {
      if (kDebugMode) {
        print(
            'Destination Randomization Service: Error getting destinations from progress - $e');
      }
      return await getRandomizedDestinations();
    }
  }

  /// Clear all progress and randomization data (for testing)
  static Future<void> clearAllData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_lastRandomizationKey);
      await prefs.remove(_randomizedOrderKey);
      await prefs.remove(_swipeProgressKey);
      await prefs.remove(_swipeProgressDateKey);
      _cachedRandomizedDestinations = null;
      _cachedSwipeProgress = null;

      if (kDebugMode) {
        print('Destination Randomization Service: Cleared all data');
      }
    } catch (e) {
      if (kDebugMode) {
        print(
            'Destination Randomization Service: Error clearing all data - $e');
      }
    }
  }
}
