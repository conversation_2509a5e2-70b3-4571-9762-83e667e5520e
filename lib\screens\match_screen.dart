import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:lottie/lottie.dart';
import '../generated/l10n/app_localizations.dart';
import '../widgets/optimized_network_image.dart';

class MatchScreen extends StatefulWidget {
  final Map<String, dynamic> destination;
  final List<String> matchedCategories;
  final VoidCallback? onAddToItinerary;
  final VoidCallback? onKeepSwiping;

  const MatchScreen({
    super.key,
    required this.destination,
    required this.matchedCategories,
    this.onAddToItinerary,
    this.onKeepSwiping,
  });

  @override
  State<MatchScreen> createState() => _MatchScreenState();
}

class _MatchScreenState extends State<MatchScreen>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _celebrationController;
  late AnimationController _heartController;
  late AnimationController _confettiController;

  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _heartAnimation;
  late Animation<double> _confettiAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimations();
  }

  void _initializeAnimations() {
    // Main slide-in animation
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // Celebration effects
    _celebrationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    // Heart pulse animation
    _heartController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    // Confetti animation
    _confettiController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    // Slide animation (bottom to top)
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.elasticOut,
    ));

    // Fade animation
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
    ));

    // Scale animation for celebration
    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _celebrationController,
      curve: Curves.elasticOut,
    ));

    // Heart pulse animation
    _heartAnimation = Tween<double>(
      begin: 1.0,
      end: 1.3,
    ).animate(CurvedAnimation(
      parent: _heartController,
      curve: Curves.elasticInOut,
    ));

    // Confetti animation
    _confettiAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _confettiController,
      curve: Curves.easeOut,
    ));
  }

  void _startAnimations() async {
    // Start slide-in animation
    await _slideController.forward();

    // Start celebration effects
    _celebrationController.forward();
    _confettiController.forward();

    // Start heart pulse (repeating)
    _heartController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _slideController.dispose();
    _celebrationController.dispose();
    _heartController.dispose();
    _confettiController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0D76FF),
      body: Stack(
        children: [
          // Confetti background
          Padding(
            padding: const EdgeInsets.only(bottom: 350),
            child: Center(child: _buildConfettiBackground()),
          ),

          // Main content
          SlideTransition(
            position: _slideAnimation,
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: _buildMainContent(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConfettiBackground() {
    return AnimatedBuilder(
      animation: _confettiAnimation,
      builder: (context, child) {
        return Opacity(
          opacity: _confettiAnimation.value,
          child: Lottie.asset(
            'assets/lottie/confetti.json',
            width: 350,
            height: 350,
            fit: BoxFit.cover,
            repeat: true,
            animate: _confettiAnimation.value > 0,
          ),
        );
      },
    );
  }

  Widget _buildMainContent() {
    return SafeArea(
      child: SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            children: [
              // Add some top spacing
              SizedBox(height: MediaQuery.of(context).size.height * 0.01),

              // Title
              _buildTitle(),

              const SizedBox(height: 70),

              // Heart with celebration
              _buildCelebrationHeart(),

              const SizedBox(height: 70),

              // Subtitle
              _buildSubtitle(),

              const SizedBox(height: 40),

              // Destination info
              _buildDestinationInfo(),

              // const SizedBox(height: 30),

              // // Matched categories
              // _buildMatchedCategories(),

              const SizedBox(height: 40),

              // Action buttons
              _buildActionButtons(),

              const SizedBox(
                height: 10,
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTitle() {
    return Column(
      children: [
        Text(
          AppLocalizations.of(context).congratulations,
          style: GoogleFonts.instrumentSans(
            fontSize: 32,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          AppLocalizations.of(context).itsAMatch,
          style: GoogleFonts.instrumentSans(
            fontSize: 28,
            fontWeight: FontWeight.w500,
            color: Colors.white.withOpacity(0.9),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildCelebrationHeart() {
    return ScaleTransition(
      scale: _scaleAnimation,
      child: AnimatedBuilder(
        animation: _heartAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _heartAnimation.value,
            child: Container(
              width: 80,
              height: 80,
              decoration: const BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.favorite,
                color: Colors.red,
                size: 60,
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildSubtitle() {
    return Text(
      AppLocalizations.of(context).travelVibesAligned,
      style: GoogleFonts.instrumentSans(
        fontSize: 18,
        fontWeight: FontWeight.w400,
        color: Colors.white.withOpacity(0.9),
        height: 1.4,
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildDestinationInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: OptimizedNetworkImage(
              imageUrl: widget.destination['image'] ?? '',
              height: 120,
              width: double.infinity,
              fit: BoxFit.cover,
              borderRadius: BorderRadius.circular(12),
              enableFadeIn: true,
              fadeInDuration: const Duration(milliseconds: 300),
              cacheWidth: 400,
              cacheHeight: 120,
              errorWidget: Container(
                height: 120,
                color: Colors.white.withOpacity(0.1),
                child: const Icon(
                  Icons.image,
                  color: Colors.white,
                  size: 40,
                ),
              ),
            ),
          ),
          const SizedBox(height: 12),
          Text(
            widget.destination['name'] ??
                AppLocalizations.of(context).unknownDestination,
            style: GoogleFonts.instrumentSans(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            widget.destination['location'] ?? '',
            style: GoogleFonts.instrumentSans(
              fontSize: 14,
              fontWeight: FontWeight.w400,
              color: Colors.white.withOpacity(0.8),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildMatchedCategories() {
    return Column(
      children: [
        Text(
          AppLocalizations.of(context).matchedPreferences,
          style: GoogleFonts.instrumentSans(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.white.withOpacity(0.9),
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: widget.matchedCategories.map((category) {
            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: Colors.white.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Text(
                category,
                style: GoogleFonts.instrumentSans(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Colors.white,
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        // Add to Itinerary button
        SizedBox(
          width: double.infinity,
          height: 56,
          child: ElevatedButton(
            onPressed: widget.onAddToItinerary,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFF7F9FC),
              foregroundColor: const Color(0xFF0D76FF),
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
            ),
            child: Text(
              AppLocalizations.of(context).addToItinerary,
              style: GoogleFonts.instrumentSans(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),

        const SizedBox(height: 16),

        // Keep Swiping button
        SizedBox(
          width: double.infinity,
          height: 56,
          child: TextButton(
            onPressed: widget.onKeepSwiping,
            style: TextButton.styleFrom(
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
            ),
            child: Text(
              AppLocalizations.of(context).keepSwiping,
              style: GoogleFonts.instrumentSans(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
