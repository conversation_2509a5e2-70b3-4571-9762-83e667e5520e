# Google Sheets Feedback Integration Setup

This document explains how to set up the Google Sheets integration for storing AI feedback data.

## Overview

The feedback service has been updated to store feedback directly to Google Sheets instead of local storage. The system uses a service account for authentication and writes data to your specified spreadsheet.

## Setup Instructions

### 1. Service Account Credentials

You need to replace the placeholder values in `assets/credentials/service_account.json` with your actual service account credentials:

**Current placeholders to replace:**
- `REPLACE_WITH_ACTUAL_PRIVATE_KEY_ID` - Replace with your service account's private key ID
- `REPLACE_WITH_ACTUAL_PRIVATE_KEY` - Replace with your service account's private key (including the `-----BEGIN PRIVATE KEY-----` and `-----<PERSON><PERSON> PRIVATE KEY-----` lines)

### 2. Google Sheets Configuration

**Spreadsheet ID:** `18_dYxomtS1cjaKi6hWTiNHuELOcjnunf-2qOotPML4s`
**Service Account Email:** `<EMAIL>`

### 3. Required Permissions

Make sure your service account has the following permissions:
- **Google Sheets API** access
- **Editor** access to the target spreadsheet

To grant access to the spreadsheet:
1. Open your Google Sheets document
2. Click "Share" 
3. Add the service account email: `<EMAIL>`
4. Give it "Editor" permissions

### 4. Sheet Structure

The system will automatically create a sheet named "Feedback" with the following columns:
- ID
- User ID  
- User Message
- AI Response
- Feedback Type
- Feedback Reason
- Custom Feedback
- Timestamp
- Conversation Context
- Session ID
- Is Synced
- Message ID

## How It Works

### Primary Storage: Google Sheets
- Feedback is submitted directly to Google Sheets when possible
- Real-time data storage with immediate availability
- No local storage dependency for primary functionality

### Backup Storage: Local Hive Database
- Local storage serves as a backup mechanism
- Stores feedback locally if Google Sheets submission fails
- Automatic retry mechanism syncs unsynced feedback in the background

### Error Handling
- If Google Sheets is unavailable, feedback is stored locally
- Background sync attempts to upload unsynced feedback
- Graceful degradation ensures no feedback is lost

## Testing the Integration

1. Replace the placeholder credentials in `assets/credentials/service_account.json`
2. Ensure the service account has access to your spreadsheet
3. Run the app and submit some feedback
4. Check your Google Sheets document to verify data is being stored

## Troubleshooting

### Common Issues:

1. **"Google Sheets Service: Initialization failed"**
   - Check that credentials file exists and has valid JSON
   - Verify private key format (should include BEGIN/END lines)
   - Ensure service account has Sheets API access

2. **"Failed to submit feedback"**
   - Verify service account has Editor access to the spreadsheet
   - Check spreadsheet ID is correct
   - Ensure internet connectivity

3. **"Sheet setup failed"**
   - Service account may not have permission to create sheets
   - Manually create a sheet named "Feedback" in your spreadsheet
   - Add the column headers as listed above

### Debug Information

The service provides detailed debug logs when running in debug mode. Check the console for:
- Initialization status
- Submission success/failure
- Sync operations
- Error details

## Security Notes

- Keep your service account credentials secure
- Do not commit the actual credentials to version control
- Consider using environment variables for production deployments
- Regularly rotate service account keys for security
