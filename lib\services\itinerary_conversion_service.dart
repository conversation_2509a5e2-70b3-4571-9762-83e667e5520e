import 'package:flutter/foundation.dart';
import '../models/itinerary.dart';
import '../services/ai_itinerary_parser.dart';

/// Service to convert AI-generated content into editable itinerary objects
class ItineraryConversionService {
  /// Convert ParsedItinerary to editable Itinerary model
  static Itinerary convertParsedItinerary(ParsedItinerary parsedItinerary) {
    try {
      // Generate default activity times if not provided
      final activityTimes = _generateDefaultActivityTimes(parsedItinerary);
      
      return Itinerary(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        title: parsedItinerary.title ?? 'AI-Generated Itinerary',
        startDate: parsedItinerary.startDate ?? _getDefaultStartDate(),
        endDate: parsedItinerary.endDate ?? _getDefaultEndDate(),
        destinations: parsedItinerary.destinations,
        hasPhoto: false,
        imagePath: null,
        dailyActivities: parsedItinerary.activities,
        daySpecificActivities: parsedItinerary.daySpecificActivities.isNotEmpty 
            ? parsedItinerary.daySpecificActivities 
            : null,
        activityTimes: activityTimes,
        activityImages: null,
        accommodation: '',
        additionalNotes: 'Generated from AI chat response\n\n${parsedItinerary.rawResponse}',
        createdAt: DateTime.now(),
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error converting parsed itinerary: $e');
      }
      rethrow;
    }
  }

  /// Convert enhanced content to editable itinerary if it contains itinerary data
  static Itinerary? convertEnhancedContentToItinerary(ParsedContent content) {
    if (content.type == ContentType.itinerary && content.itinerary != null) {
      return convertParsedItinerary(content.itinerary!);
    }
    return null;
  }

  /// Generate default activity times for timeline display
  static Map<String, Map<String, Map<String, String>>> _generateDefaultActivityTimes(
      ParsedItinerary parsedItinerary) {
    final activityTimes = <String, Map<String, Map<String, String>>>{};
    
    // If using day-specific activities
    if (parsedItinerary.daySpecificActivities.isNotEmpty) {
      for (final dayEntry in parsedItinerary.daySpecificActivities.entries) {
        final day = dayEntry.key;
        final dayActivities = dayEntry.value;
        
        for (final destinationEntry in dayActivities.entries) {
          final destination = destinationEntry.key;
          final activities = destinationEntry.value;
          
          if (!activityTimes.containsKey(destination)) {
            activityTimes[destination] = {};
          }
          
          // Generate times for each activity (2-hour slots starting at 9 AM)
          for (int i = 0; i < activities.length; i++) {
            final activity = activities[i];
            final startHour = 9 + (i * 2);
            final endHour = startHour + 2;
            
            activityTimes[destination]![activity] = {
              'startTime': '${startHour.toString().padLeft(2, '0')}:00',
              'endTime': '${endHour.toString().padLeft(2, '0')}:00',
              'day': day.toString(),
            };
          }
        }
      }
    } else {
      // Use legacy activities format
      for (final destinationEntry in parsedItinerary.activities.entries) {
        final destination = destinationEntry.key;
        final activities = destinationEntry.value;
        
        activityTimes[destination] = {};
        
        // Generate times for each activity (2-hour slots starting at 9 AM)
        for (int i = 0; i < activities.length; i++) {
          final activity = activities[i];
          final startHour = 9 + (i * 2);
          final endHour = startHour + 2;
          
          activityTimes[destination]![activity] = {
            'startTime': '${startHour.toString().padLeft(2, '0')}:00',
            'endTime': '${endHour.toString().padLeft(2, '0')}:00',
            'day': '1',
          };
        }
      }
    }
    
    return activityTimes;
  }

  /// Get default start date (today)
  static String _getDefaultStartDate() {
    final now = DateTime.now();
    return '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';
  }

  /// Get default end date (tomorrow)
  static String _getDefaultEndDate() {
    final tomorrow = DateTime.now().add(const Duration(days: 1));
    return '${tomorrow.year}-${tomorrow.month.toString().padLeft(2, '0')}-${tomorrow.day.toString().padLeft(2, '0')}';
  }

  /// Create a copy of an itinerary with updated activity times
  static Itinerary updateItineraryActivityTimes(
    Itinerary itinerary,
    Map<String, Map<String, Map<String, String>>> newActivityTimes,
  ) {
    return Itinerary(
      id: itinerary.id,
      title: itinerary.title,
      startDate: itinerary.startDate,
      endDate: itinerary.endDate,
      destinations: itinerary.destinations,
      hasPhoto: itinerary.hasPhoto,
      imagePath: itinerary.imagePath,
      dailyActivities: itinerary.dailyActivities,
      daySpecificActivities: itinerary.daySpecificActivities,
      activityTimes: newActivityTimes,
      activityImages: itinerary.activityImages,
      accommodation: itinerary.accommodation,
      additionalNotes: itinerary.additionalNotes,
      createdAt: itinerary.createdAt,
    );
  }

  /// Create a copy of an itinerary with updated day-specific activities
  static Itinerary updateItineraryDayActivities(
    Itinerary itinerary,
    Map<int, Map<String, List<String>>> newDayActivities,
  ) {
    return Itinerary(
      id: itinerary.id,
      title: itinerary.title,
      startDate: itinerary.startDate,
      endDate: itinerary.endDate,
      destinations: itinerary.destinations,
      hasPhoto: itinerary.hasPhoto,
      imagePath: itinerary.imagePath,
      dailyActivities: itinerary.dailyActivities,
      daySpecificActivities: newDayActivities,
      activityTimes: itinerary.activityTimes,
      activityImages: itinerary.activityImages,
      accommodation: itinerary.accommodation,
      additionalNotes: itinerary.additionalNotes,
      createdAt: itinerary.createdAt,
    );
  }

  /// Validate that an itinerary has the required structure for interactive editing
  static bool isItineraryEditable(Itinerary itinerary) {
    // Check if it has destinations
    if (itinerary.destinations.isEmpty) {
      return false;
    }

    // Check if it has activities (either format)
    final hasLegacyActivities = itinerary.dailyActivities.isNotEmpty;
    final hasDaySpecificActivities = itinerary.daySpecificActivities?.isNotEmpty == true;
    
    return hasLegacyActivities || hasDaySpecificActivities;
  }

  /// Get the total number of days in an itinerary
  static int getItineraryDayCount(Itinerary itinerary) {
    if (itinerary.daySpecificActivities?.isNotEmpty == true) {
      return itinerary.daySpecificActivities!.keys.length;
    }
    
    // For legacy format, calculate from dates
    try {
      final startDate = DateTime.parse(itinerary.startDate);
      final endDate = DateTime.parse(itinerary.endDate);
      return endDate.difference(startDate).inDays + 1;
    } catch (e) {
      return 1; // Default to 1 day
    }
  }

  /// Ensure an itinerary has proper day-specific structure
  static Itinerary ensureDaySpecificStructure(Itinerary itinerary) {
    if (itinerary.daySpecificActivities?.isNotEmpty == true) {
      return itinerary; // Already has day-specific structure
    }

    // Convert legacy format to day-specific
    final daySpecificActivities = <int, Map<String, List<String>>>{
      1: Map<String, List<String>>.from(itinerary.dailyActivities),
    };

    return Itinerary(
      id: itinerary.id,
      title: itinerary.title,
      startDate: itinerary.startDate,
      endDate: itinerary.endDate,
      destinations: itinerary.destinations,
      hasPhoto: itinerary.hasPhoto,
      imagePath: itinerary.imagePath,
      dailyActivities: itinerary.dailyActivities,
      daySpecificActivities: daySpecificActivities,
      activityTimes: itinerary.activityTimes,
      activityImages: itinerary.activityImages,
      accommodation: itinerary.accommodation,
      additionalNotes: itinerary.additionalNotes,
      createdAt: itinerary.createdAt,
    );
  }
}
