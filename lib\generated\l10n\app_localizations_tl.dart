/// Generated file. Do not edit.
///
/// This file contains the localized strings for the TripWiseGo app.
/// To add a new localized string, add it to the appropriate .arb file
/// in the lib/l10n directory and run `flutter gen-l10n`.


import 'app_localizations.dart';

/// The translations for Tagalog (`tl`).
class AppLocalizationsTl extends AppLocalizations {
  AppLocalizationsTl([String locale = 'tl']) : super(locale);

  @override
  String get appTitle => 'TripWiseGo';

  @override
  String get welcome => 'Maligayang pagdating';

  @override
  String get guestUser => 'Guest na user';

  @override
  String get readyForAdventure => 'Handa na para sa susunod na adventure';

  @override
  String get exploringAsGuest => 'Naggagalugad ng mundo bilang guest';

  @override
  String get editProfile => 'I-edit ang profile';

  @override
  String get saveChanges => 'I-save ang mga pagbabago';

  @override
  String get cancel => 'Kanselahin';

  @override
  String get username => 'Userna<PERSON>';

  @override
  String get email => 'Email';

  @override
  String get profileUpdatedSuccessfully => 'Matagumpay na na-update ang profile!';

  @override
  String failedToUpdateProfile(String error) {
    return 'Hindi na-update ang profile: $error';
  }

  @override
  String get profilePictureUpdatedSuccessfully => 'Matagumpay na na-update ang profile picture!';

  @override
  String failedToUploadImage(String error) {
    return 'Hindi na-upload ang larawan: $error';
  }

  @override
  String get profileEditingNotAvailableForGuests => 'Hindi available ang pag-edit ng profile para sa mga guest user';

  @override
  String get profilePictureEditingNotAvailableForGuests => 'Hindi available ang pag-edit ng profile picture para sa mga guest user';

  @override
  String get usernameCannotBeEmpty => 'Hindi pwedeng walang laman ang username';

  @override
  String get usernameMustBeBetween2And30Characters => 'Dapat 2 hanggang 30 character ang username';

  @override
  String get plan => 'Plano';

  @override
  String get termsOfService => 'Terms of Service';

  @override
  String get language => 'Wika';

  @override
  String get privacyPolicy => 'Privacy Policy';

  @override
  String get support => 'Suporta';

  @override
  String get helpCenter => 'Help Center';

  @override
  String get contactUs => 'Makipag-ugnayan sa amin';

  @override
  String get helpSupport => 'Help & Support';

  @override
  String get howCanWeHelpYou => 'How can we help you?';

  @override
  String get helpSupportGreeting => 'Hi! I\'m here to help you with TripwiseGO. You can ask me about app features, troubleshooting, or report any issues you\'re experiencing.';

  @override
  String get helpSupportWelcome => 'Welcome to TripwiseGO Support! Here are some things I can help you with:';

  @override
  String get helpSupportFeatures => '• App features and how to use them\n• Navigation and account help\n• Troubleshooting common issues\n• Reporting bugs or problems';

  @override
  String get helpSupportAskQuestion => 'Feel free to ask me anything or describe any issues you\'re having!';

  @override
  String get reportIssue => 'Report Issue';

  @override
  String get reportBug => 'Report Bug';

  @override
  String get reportProblem => 'Report Problem';

  @override
  String get issueCategory => 'Issue Category';

  @override
  String get bugReport => 'Bug Report';

  @override
  String get featureRequest => 'Feature Request';

  @override
  String get generalFeedback => 'General Feedback';

  @override
  String get accountIssue => 'Account Issue';

  @override
  String get technicalProblem => 'Technical Problem';

  @override
  String get yourName => 'Your Name';

  @override
  String get yourEmail => 'Your Email';

  @override
  String get issueDescription => 'Issue Description';

  @override
  String get describeIssueDetail => 'Please describe the issue in detail';

  @override
  String get optionalScreenshot => 'Screenshot (Optional)';

  @override
  String get submitReport => 'Submit Report';

  @override
  String get reportSubmitted => 'Report Submitted';

  @override
  String get reportSubmittedSuccess => 'Thank you! Your report has been submitted successfully. We\'ll look into it and get back to you if needed.';

  @override
  String get reportSubmissionFailed => 'Failed to submit report. Please try again later.';

  @override
  String get nameRequired => 'Name is required';

  @override
  String get emailRequired => 'Email is required';

  @override
  String get validEmailRequired => 'Please enter a valid email address';

  @override
  String get descriptionRequired => 'Issue description is required';

  @override
  String get selectCategory => 'Please select a category';

  @override
  String get subscription => 'Subscription';

  @override
  String get subscriptionActive => 'Subscription Active!';

  @override
  String get welcomeToPremium => 'Welcome to TripwiseGO Premium! Enjoy unlimited access to all features.';

  @override
  String get currentPlan => 'Current Plan';

  @override
  String get trial => 'Trial';

  @override
  String get active => 'Active';

  @override
  String trialEndsIn(int days) {
    return 'Trial ends in $days days';
  }

  @override
  String renewsIn(int days) {
    return 'Renews in $days days';
  }

  @override
  String get yourBenefits => 'Your Benefits';

  @override
  String get startPlanningTrip => 'Start Planning Your Trip';

  @override
  String get manageSubscription => 'Manage Subscription';

  @override
  String get basic => 'Basic';

  @override
  String get premium => 'Premium';

  @override
  String get basicPlaceRecommendations => 'Basic Place Recommendations';

  @override
  String get locationRecommendationSwiping => 'Location Recommendation Swiping (20 swipes/day)';

  @override
  String get aiPoweredTravelPlanner => 'AI-Powered Travel Planner';

  @override
  String get unlimitedQuizzes => 'Unlimited Quizzes & Fun Facts';

  @override
  String get noAds => 'No-Ads';

  @override
  String get oneMonth => '1 Month';

  @override
  String get threeMonths => '3 Months';

  @override
  String get fiveMonths => '5 Months';

  @override
  String dayFreeTrial(int days) {
    return '$days-day free trial';
  }

  @override
  String get billedMonthly => 'Billed monthly';

  @override
  String get billedTwiceAnnually => 'Billed twice annually';

  @override
  String get billedAnnually => 'Billed annually';

  @override
  String get save45Percent => 'SAVE 45%';

  @override
  String get continueButton => 'Continue';

  @override
  String get termsAndConditions => 'Terms and Conditions';

  @override
  String get failedToSubscribe => 'Failed to subscribe. Please try again.';

  @override
  String get signOut => 'Mag-sign out';

  @override
  String get selectLanguage => 'Pumili ng wika';

  @override
  String get chooseYourPreferredLanguage => 'Piliin ang gusto mong wika';

  @override
  String get languageUpdatedSuccessfully => 'Matagumpay na na-update ang wika!';

  @override
  String get home => 'Home';

  @override
  String get match => 'Match';

  @override
  String get chat => 'Chat';

  @override
  String get profile => 'Profile';

  @override
  String get loading => 'Naglo-load...';

  @override
  String get error => 'Error';

  @override
  String get retry => 'Subukan ulit';

  @override
  String get ok => 'OK';

  @override
  String get yes => 'Oo';

  @override
  String get no => 'Hindi';

  @override
  String get save => 'I-save';

  @override
  String get delete => 'I-delete';

  @override
  String get edit => 'I-edit';

  @override
  String get add => 'Magdagdag';

  @override
  String get remove => 'Alisin';

  @override
  String get close => 'Isara';

  @override
  String get back => 'Bumalik';

  @override
  String get next => 'Susunod';

  @override
  String get previous => 'Nakaraan';

  @override
  String get done => 'Tapos na';

  @override
  String get search => 'Maghanap';

  @override
  String get noResultsFound => 'Walang nahanap na resulta';

  @override
  String get tryAgain => 'Subukan ulit';

  @override
  String get somethingWentWrong => 'May nangyaring mali';

  @override
  String get networkError => 'Network error. Tingnan ang inyong connection.';

  @override
  String get serverError => 'Server error. Subukan ulit mamaya.';

  @override
  String get invalidInput => 'Hindi valid na input';

  @override
  String get required => 'Kailangan';

  @override
  String get optional => 'Opsyonal';

  @override
  String get itinerary => 'Itinerary';

  @override
  String get yourItineraries => 'Your Itineraries';

  @override
  String get startPlanningYourTrip => 'Start Planning Your Trip';

  @override
  String get tripPlanningFeaturesWillAppearHere => 'Your trip planning features will appear here. The persistent authentication is now working!';

  @override
  String get forYou => 'For You';

  @override
  String get liked => 'Liked';

  @override
  String get collab => 'Collab';

  @override
  String get collaborativeItinerary => 'Collaborative';

  @override
  String get endSession => 'End Session';

  @override
  String get logout => 'Logout';

  @override
  String logoutFailed(String error) {
    return 'Logout failed: $error';
  }

  @override
  String get noItineraryFound => 'Walang Nahanap na Itinerary';

  @override
  String get askAiToCreateTravelPlan => 'Hilingin sa aming AI na gumawa ng travel plan para sa iyo!';

  @override
  String get saturday => 'Sabado';

  @override
  String get tuesday => 'Martes';

  @override
  String dayNumber(int number) {
    return 'Araw $number';
  }

  @override
  String get itineraryOverview => 'Buod ng Itinerary';

  @override
  String daysAndNights(int days, int nights) {
    return '${days}a ${nights}g';
  }

  @override
  String get hiImWanderlyAi => 'Kumusta, ako si Wanderly AI 🌏';

  @override
  String get yourTravelAiAssistant => 'Ang inyong AI assistant sa paglalakbay, paano ko kayo matutulungan ngayon?';

  @override
  String get useThisBubbleChat => 'Gamitin ang bubble chat na ito';

  @override
  String get aiAssistant => 'AI Assistant';

  @override
  String get chatHistory => 'Kasaysayan ng Chat';

  @override
  String get newChat => 'Bagong Chat';

  @override
  String get addImage => 'Magdagdag ng Larawan';

  @override
  String get camera => 'Camera';

  @override
  String get gallery => 'Gallery';

  @override
  String get microphonePermissionRequired => 'Kailangan ng pahintulot sa mikropono para sa voice input';

  @override
  String get speechRecognitionNotAvailable => 'Hindi available ang speech recognition sa device na ito';

  @override
  String get listening => 'Nakikinig...';

  @override
  String get deleteChat => 'Burahin ang Chat';

  @override
  String get deleteChatConfirmation => 'Sigurado ka bang gusto mong burahin ang chat na ito? Hindi na ito mababalik.';

  @override
  String get chatDeletedSuccessfully => 'Matagumpay na nabura ang chat';

  @override
  String get pleaseEnterSearchQuery => 'Pakitype ang search query';

  @override
  String get dailySearchLimitReached => 'Naabot na ang araw-araw na search limit. Pwede kang mag-search ng 5 beses sa isang araw.';

  @override
  String get searchingTheWeb => 'Naghahanap sa Web...';

  @override
  String get webSearchModeActive => 'Aktibo ang Web Search Mode';

  @override
  String get pleaseWaitWhileSearching => 'Pakihintay habang naghahanap ako ng impormasyon';

  @override
  String get yourNextMessageWillSearch => 'Ang susunod mong mensahe ay maghahanap sa web';

  @override
  String get disableWebSearch => 'I-disable ang Web Search';

  @override
  String get enableWebSearch => 'I-enable ang Web Search';

  @override
  String get switchBackToAiChatMode => 'Bumalik sa AI chat mode';

  @override
  String get searchWebForCurrentInfo => 'Maghanap sa web ng kasalukuyang impormasyon';

  @override
  String get pickImageFromGallery => 'Pumili ng Larawan sa Gallery';

  @override
  String get uploadImageForAiAnalysis => 'Mag-upload ng larawan para sa AI analysis';

  @override
  String get yourMessage => 'Ang Inyong Mensahe';

  @override
  String get wanderlyAi => 'Wanderly AI';

  @override
  String get webSearch => 'Web Search:';

  @override
  String get like => 'Like';

  @override
  String get dislike => 'Dislike';

  @override
  String get copy => 'Copy';

  @override
  String get regenerate => 'I-regenerate';

  @override
  String get failedToSubmitFeedback => 'Hindi na-submit ang feedback. Subukan ulit.';

  @override
  String get thankYouForFeedback => 'Salamat sa inyong feedback! 🙏';

  @override
  String get feedbackReceivedThanks => 'Natanggap ang feedback. Salamat sa pagtulong sa amin na mag-improve! 🚀';

  @override
  String get responseCopiedToClipboard => 'Na-copy ang response sa clipboard';

  @override
  String get wanderlyAiIsTyping => 'Nag-type si Wanderly AI';

  @override
  String get stopGeneration => 'Itigil ang Generation';

  @override
  String get youHaveChatsLeft => 'May 10 chats ka pang natitira';

  @override
  String get enterSearchQuery => 'Itype ang inyong search query...';

  @override
  String get askMeAnythingOrLongPress => 'Tanungin ako ng kahit ano o i-long press para magsalita...';

  @override
  String failedToPickImage(String error) {
    return 'Hindi napili ang larawan: $error';
  }

  @override
  String get failedToAnalyzeImage => 'Hindi na-analyze ang larawan. Subukan ulit.';

  @override
  String get responseGenerationStopped => 'Natigil ang response generation.';

  @override
  String get unknownDestination => 'Hindi Kilalang Destinasyon';

  @override
  String get congratulations => 'Congratulations';

  @override
  String get itsAMatch => 'It\'s a match';

  @override
  String get travelVibesAligned => 'Travel vibes aligned pack\nyour bags, you\'ve got a match!';

  @override
  String get matchedPreferences => 'Matched Preferences:';

  @override
  String get addToItinerary => 'Add to Itinerary';

  @override
  String get keepSwiping => 'Keep swiping';

  @override
  String get versionInfo => 'Version Info';

  @override
  String get appVersion => 'App Version';

  @override
  String get buildNumber => 'Build Number';

  @override
  String get packageName => 'Package Name';

  @override
  String get appName => 'App Name';

  @override
  String get buildSignature => 'Build Signature';

  @override
  String get installerStore => 'Installer Store';

  @override
  String get deviceInfo => 'Device Information';

  @override
  String get operatingSystem => 'Operating System';

  @override
  String get flutterVersion => 'Flutter Version';

  @override
  String get dartVersion => 'Dart Version';

  @override
  String get leaveAReview => 'Leave a Review';

  @override
  String get rateOurApp => 'Rate Our App';

  @override
  String get enjoyingTripWiseGo => 'Enjoying TripWiseGo?';

  @override
  String get helpUsImproveByLeavingReview => 'Help us improve by leaving a review on the app store. Your feedback means a lot to us!';

  @override
  String get rateNow => 'Rate Now';

  @override
  String get maybeLater => 'Maybe Later';

  @override
  String get reviewFeatureNotAvailable => 'Review feature is only available in production builds';

  @override
  String get unableToOpenStore => 'Unable to open app store. Please try again later.';

  @override
  String get thankYouForReview => 'Thank you for taking the time to review our app!';
}
