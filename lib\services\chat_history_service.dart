import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/chat_history_models.dart';
import '../database/chat_history_database.dart';
import '../database/chat_history_migration.dart';
import '../services/feedback_service.dart';

class ChatHistoryService {
  static const String _activeSessionKey = 'active_session_id';
  static bool _isInitialized = false;

  /// Initialize SQLite database
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialize SQLite database
      await ChatHistoryDatabase.database;

      // Check and perform migration from Hive if needed
      await ChatHistoryMigration.checkAndMigrate();

      // Initialize feedback service
      await FeedbackService.initialize();

      _isInitialized = true;

      if (kDebugMode) {
        final stats = await ChatHistoryDatabase.getStatistics();
        print('Chat History Service: Initialized successfully');
        print(
            'Chat History Service: Found ${stats['sessions']} existing chat sessions with ${stats['messages']} messages');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Chat History Service: Initialization failed: $e');
      }

      // Mark as initialized even if initialization fails to prevent blocking the app
      _isInitialized = true;
    }
  }

  /// Ensure service is initialized
  static Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  /// Clear active session data
  static Future<void> _clearActiveSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_activeSessionKey);
    } catch (e) {
      if (kDebugMode) {
        print('Chat History Service: Could not clear active session: $e');
      }
    }
  }

  /// Check if the service is properly initialized and functional
  static bool get isReady => _isInitialized;

  /// Create a new chat session
  static Future<ChatSession> createNewSession({String? firstMessage}) async {
    await _ensureInitialized();

    if (!isReady) {
      if (kDebugMode) {
        print(
            'Chat History Service: Service not ready, creating session without persistence');
      }
      // Return a session without saving it if storage is not available
      return ChatSession(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        title: firstMessage != null
            ? ChatSession.generateTitle(firstMessage)
            : 'New Chat',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        messages: [],
        isActive: true,
      );
    }

    final sessionId = DateTime.now().millisecondsSinceEpoch.toString();
    final title = firstMessage != null
        ? ChatSession.generateTitle(firstMessage)
        : 'New Chat';

    final session = ChatSession(
      id: sessionId,
      title: title,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      messages: [],
      isActive: true,
    );

    try {
      // Deactivate all other sessions
      await ChatHistoryDatabase.deactivateAllSessions();

      // Save the new session
      await ChatHistoryDatabase.insertChatSession(session);

      // Set as active session
      await ChatHistoryDatabase.setActiveSession(sessionId);

      // Store active session ID in SharedPreferences for compatibility
      await _setActiveSession(sessionId);

      if (kDebugMode) {
        print(
            'Chat History Service: Created new session: $sessionId with title: $title');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Chat History Service: Failed to save new session: $e');
      }
      // Return the session even if saving failed
    }

    return session;
  }

  /// Get all chat sessions sorted by last message time (newest first)
  static Future<List<ChatSession>> getAllSessions() async {
    await _ensureInitialized();

    if (!isReady) {
      if (kDebugMode) {
        print(
            'Chat History Service: Service not ready, returning empty sessions list');
      }
      return [];
    }

    try {
      final sessions = await ChatHistoryDatabase.getAllChatSessions();

      if (kDebugMode) {
        print('Chat History Service: Retrieved ${sessions.length} sessions');
      }

      return sessions;
    } catch (e) {
      if (kDebugMode) {
        print('Chat History Service: Failed to get sessions: $e');
      }
      return [];
    }
  }

  /// Get a specific chat session by ID
  static Future<ChatSession?> getSession(String sessionId) async {
    await _ensureInitialized();

    try {
      final session = await ChatHistoryDatabase.getChatSession(sessionId);
      if (kDebugMode && session != null) {
        print(
            'Chat History Service: Retrieved session $sessionId with ${session.messages.length} messages');
      }
      return session;
    } catch (e) {
      if (kDebugMode) {
        print(
            'Chat History Service Error: Failed to get session $sessionId - $e');
      }
      return null;
    }
  }

  /// Get the currently active session
  static Future<ChatSession?> getActiveSession() async {
    await _ensureInitialized();

    try {
      // First try to get from database
      final session = await ChatHistoryDatabase.getActiveChatSession();
      if (session != null) {
        if (kDebugMode) {
          print(
              'Chat History Service: Active session found: ${session.id} with ${session.messages.length} messages');
        }
        return session;
      }

      // Fallback to SharedPreferences for compatibility
      final activeSessionId = await _getActiveSessionId();
      if (activeSessionId != null) {
        final fallbackSession = await getSession(activeSessionId);
        if (fallbackSession != null) {
          // Update database to mark as active
          await ChatHistoryDatabase.setActiveSession(activeSessionId);
          if (kDebugMode) {
            print(
                'Chat History Service: Active session found via fallback: $activeSessionId with ${fallbackSession.messages.length} messages');
          }
          return fallbackSession;
        }
      }

      if (kDebugMode) {
        print('Chat History Service: No active session found');
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Chat History Service Error: Failed to get active session - $e');
      }
      return null;
    }
  }

  /// Set a session as active
  static Future<void> setActiveSession(String sessionId) async {
    await _ensureInitialized();

    try {
      // Set active in database
      await ChatHistoryDatabase.setActiveSession(sessionId);

      // Also store in SharedPreferences for compatibility
      await _setActiveSession(sessionId);

      if (kDebugMode) {
        print('Chat History Service: Set active session: $sessionId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Chat History Service: Failed to set active session: $e');
      }
      rethrow;
    }
  }

  /// Clear the active session (for starting fresh)
  static Future<void> clearActiveSession() async {
    await _ensureInitialized();

    try {
      // Deactivate all sessions in database
      await ChatHistoryDatabase.deactivateAllSessions();

      // Clear the active session preference
      await _clearActiveSession();

      if (kDebugMode) {
        print('Chat History Service: Cleared active session - starting fresh');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Chat History Service: Failed to clear active session: $e');
      }
    }
  }

  /// Add a message to a session
  static Future<void> addMessageToSession(
      String sessionId, Map<String, dynamic> messageMap) async {
    await _ensureInitialized();

    if (!isReady) {
      if (kDebugMode) {
        print(
            'Chat History Service: Service not ready, cannot add message to session');
      }
      return; // Fail silently to prevent app crashes
    }

    try {
      final messageModel = ChatMessageModel.fromMap(messageMap);

      // Insert message into database
      await ChatHistoryDatabase.insertChatMessage(sessionId, messageModel);

      // Update session timestamp
      final session = await getSession(sessionId);
      if (session != null) {
        session.updateTimestamp();
        await ChatHistoryDatabase.updateChatSession(session);

        if (kDebugMode) {
          print(
              'Chat History Service: Added message to session $sessionId (total: ${session.messages.length + 1})');
        }
      } else {
        if (kDebugMode) {
          print(
              'Chat History Service Error: Session $sessionId not found when adding message');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print(
            'Chat History Service Error: Failed to add message to session $sessionId - $e');
      }
      // Don't rethrow to prevent breaking the chat flow
    }
  }

  /// Add a message to the active session
  static Future<void> addMessageToActiveSession(
      Map<String, dynamic> messageMap) async {
    final activeSession = await getActiveSession();
    if (activeSession != null) {
      await addMessageToSession(activeSession.id, messageMap);
    }
  }

  /// Update session title (usually after first message)
  static Future<void> updateSessionTitle(
      String sessionId, String newTitle) async {
    await _ensureInitialized();

    try {
      final session = await getSession(sessionId);
      if (session != null) {
        session.title = ChatSession.generateTitle(newTitle);
        session.updateTimestamp();
        await ChatHistoryDatabase.updateChatSession(session);

        if (kDebugMode) {
          print(
              'Chat History Service: Updated session title: ${session.title}');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Chat History Service: Failed to update session title: $e');
      }
    }
  }

  /// Rename session title (for user-initiated renames)
  static Future<void> renameSession(
      String sessionId, String customTitle) async {
    await _ensureInitialized();

    try {
      final session = await getSession(sessionId);
      if (session != null) {
        session.title = customTitle.trim().isNotEmpty
            ? customTitle.trim()
            : 'Untitled Chat';
        session.updateTimestamp();
        await ChatHistoryDatabase.updateChatSession(session);

        if (kDebugMode) {
          print('Chat History Service: Renamed session to: ${session.title}');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Chat History Service: Failed to rename session: $e');
      }
    }
  }

  /// Delete a chat session
  static Future<void> deleteSession(String sessionId) async {
    await _ensureInitialized();

    try {
      await ChatHistoryDatabase.deleteChatSession(sessionId);

      // If this was the active session, clear the active session
      final activeSessionId = await _getActiveSessionId();
      if (activeSessionId == sessionId) {
        await _clearActiveSession();
      }

      if (kDebugMode) {
        print('Chat History Service: Deleted session: $sessionId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Chat History Service: Failed to delete session: $e');
      }
      rethrow;
    }
  }

  /// Clear all chat history
  static Future<void> clearAllHistory() async {
    await _ensureInitialized();

    try {
      await ChatHistoryDatabase.clearAllHistory();
      await _clearActiveSession();

      if (kDebugMode) {
        print('Chat History Service: Cleared all chat history');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Chat History Service: Failed to clear all history: $e');
      }
      rethrow;
    }
  }

  /// Get total number of sessions
  static Future<int> getSessionCount() async {
    await _ensureInitialized();
    try {
      final stats = await ChatHistoryDatabase.getStatistics();
      return stats['sessions'] ?? 0;
    } catch (e) {
      if (kDebugMode) {
        print('Chat History Service: Failed to get session count: $e');
      }
      return 0;
    }
  }

  /// Private helper methods

  static Future<void> _setActiveSession(String sessionId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_activeSessionKey, sessionId);
    } catch (e) {
      if (kDebugMode) {
        print(
            'Chat History Service: Failed to set active session in preferences: $e');
      }
    }
  }

  static Future<String?> _getActiveSessionId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_activeSessionKey);
    } catch (e) {
      if (kDebugMode) {
        print(
            'Chat History Service: Failed to get active session from preferences: $e');
      }
      return null;
    }
  }

  /// Close database connection (call this when app is closing)
  static Future<void> dispose() async {
    try {
      await ChatHistoryDatabase.close();
      _isInitialized = false;
      if (kDebugMode) {
        print('Chat History Service: Disposed successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Chat History Service: Error during disposal: $e');
      }
    }
  }
}
