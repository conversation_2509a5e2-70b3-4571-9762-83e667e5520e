/// Configuration for app store URLs and IDs
class AppStoreConfig {
  // iOS App Store ID - Replace with your actual App Store ID when published
  static const String iosAppStoreId = 'YOUR_APP_ID';
  
  // Package name for Android (automatically retrieved from package_info_plus)
  // This is used to construct the Google Play Store URL
  
  // Web store URLs (fallback for web platform)
  static const String webPlayStoreUrl = 'https://play.google.com/store/apps/details?id=';
  static const String webAppStoreUrl = 'https://apps.apple.com/app/id$iosAppStoreId';
  
  // Custom store URLs (if you have your own distribution)
  static const String customStoreUrl = '';
  
  /// Returns the appropriate store URL based on platform and package name
  static String getAndroidStoreUrl(String packageName) {
    return 'https://play.google.com/store/apps/details?id=$packageName';
  }
  
  /// Returns the iOS App Store URL
  static String getIosStoreUrl() {
    return 'https://apps.apple.com/app/id$iosAppStoreId';
  }
  
  /// Returns the web store URL (defaults to Play Store)
  static String getWebStoreUrl(String packageName) {
    return '$webPlayStoreUrl$packageName';
  }
}
