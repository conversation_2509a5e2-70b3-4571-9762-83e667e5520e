import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import '../models/subscription.dart';
import '../services/auth_service.dart';

class RevenueCatService {
  // RevenueCat API Keys (replace with your actual keys)
  static const String _appleApiKey = 'appl_YOUR_APPLE_API_KEY_HERE';
  static const String _googleApiKey = 'goog_FwXuYhVgSLxhGiDfLOjzChNcgMG';

  static bool _isInitialized = false;
  static CustomerInfo? _currentCustomerInfo;

  /// Initialize RevenueCat
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Configure RevenueCat
      late PurchasesConfiguration configuration;

      if (Platform.isIOS) {
        configuration = PurchasesConfiguration(_appleApiKey);
      } else if (Platform.isAndroid) {
        configuration = PurchasesConfiguration(_googleApiKey);
      } else {
        throw UnsupportedError('Platform not supported');
      }

      await Purchases.configure(configuration);

      // Set user ID if authenticated
      final user = AuthService.currentUser;
      if (user != null) {
        await Purchases.logIn(user.id);
      }

      // Get initial customer info
      _currentCustomerInfo = await Purchases.getCustomerInfo();

      _isInitialized = true;

      if (kDebugMode) {
        print('RevenueCat Service: Initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('RevenueCat Service: Initialization error - $e');
      }
      rethrow;
    }
  }

  /// Ensure service is initialized
  static Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  /// Login user to RevenueCat
  static Future<void> loginUser(String userId) async {
    try {
      await _ensureInitialized();
      final loginResult = await Purchases.logIn(userId);
      _currentCustomerInfo = loginResult.customerInfo;

      if (kDebugMode) {
        print('RevenueCat Service: User logged in - $userId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('RevenueCat Service: Login error - $e');
      }
      rethrow;
    }
  }

  /// Logout user from RevenueCat
  static Future<void> logoutUser() async {
    try {
      await _ensureInitialized();
      _currentCustomerInfo = await Purchases.logOut();

      if (kDebugMode) {
        print('RevenueCat Service: User logged out');
      }
    } catch (e) {
      if (kDebugMode) {
        print('RevenueCat Service: Logout error - $e');
      }
    }
  }

  /// Get available offerings
  static Future<Offerings?> getOfferings() async {
    try {
      await _ensureInitialized();
      final offerings = await Purchases.getOfferings();

      if (kDebugMode) {
        print('RevenueCat Service: Retrieved offerings');
      }

      return offerings;
    } catch (e) {
      if (kDebugMode) {
        print('RevenueCat Service: Error getting offerings - $e');
      }
      return null;
    }
  }

  /// Purchase a subscription
  static Future<CustomerInfo?> purchaseSubscription(String productId) async {
    try {
      await _ensureInitialized();

      final offerings = await getOfferings();
      if (offerings == null) {
        throw Exception('No offerings available');
      }

      // Find the product in offerings
      Package? targetPackage;
      for (final offering in offerings.all.values) {
        for (final package in offering.availablePackages) {
          if (package.storeProduct.identifier == productId) {
            targetPackage = package;
            break;
          }
        }
        if (targetPackage != null) break;
      }

      if (targetPackage == null) {
        throw Exception('Product not found: $productId');
      }

      // Make the purchase
      final customerInfo = await Purchases.purchasePackage(targetPackage);
      _currentCustomerInfo = customerInfo;

      if (kDebugMode) {
        print('RevenueCat Service: Purchase successful - $productId');
      }

      return _currentCustomerInfo;
    } catch (e) {
      if (kDebugMode) {
        print('RevenueCat Service: Purchase error - $e');
      }
      rethrow;
    }
  }

  /// Restore purchases
  static Future<CustomerInfo?> restorePurchases() async {
    try {
      await _ensureInitialized();
      _currentCustomerInfo = await Purchases.restorePurchases();

      if (kDebugMode) {
        print('RevenueCat Service: Purchases restored');
      }

      return _currentCustomerInfo;
    } catch (e) {
      if (kDebugMode) {
        print('RevenueCat Service: Restore error - $e');
      }
      rethrow;
    }
  }

  /// Get current customer info
  static Future<CustomerInfo?> getCustomerInfo() async {
    try {
      await _ensureInitialized();
      _currentCustomerInfo = await Purchases.getCustomerInfo();
      return _currentCustomerInfo;
    } catch (e) {
      if (kDebugMode) {
        print('RevenueCat Service: Error getting customer info - $e');
      }
      return null;
    }
  }

  /// Check if user has active subscription
  static Future<bool> hasActiveSubscription() async {
    try {
      final customerInfo = await getCustomerInfo();
      if (customerInfo == null) return false;

      // Check if user has any active entitlements
      return customerInfo.entitlements.active.isNotEmpty;
    } catch (e) {
      if (kDebugMode) {
        print('RevenueCat Service: Error checking subscription - $e');
      }
      return false;
    }
  }

  /// Get active subscription info
  static Future<EntitlementInfo?> getActiveSubscription() async {
    try {
      final customerInfo = await getCustomerInfo();
      if (customerInfo == null) return null;

      // Return the first active entitlement
      if (customerInfo.entitlements.active.isNotEmpty) {
        return customerInfo.entitlements.active.values.first;
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('RevenueCat Service: Error getting active subscription - $e');
      }
      return null;
    }
  }

  /// Convert RevenueCat product to SubscriptionTier
  static SubscriptionTier? productToSubscriptionTier(StoreProduct product) {
    try {
      // Find matching tier by product ID
      for (final tier in SubscriptionTier.availableTiers) {
        if (tier.revenueCatProductId == product.identifier) {
          return tier;
        }
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('RevenueCat Service: Error converting product - $e');
      }
      return null;
    }
  }

  /// Get subscription expiration date
  static Future<DateTime?> getSubscriptionExpirationDate() async {
    try {
      final activeSubscription = await getActiveSubscription();
      if (activeSubscription == null) return null;

      // Parse the expiration date string to DateTime
      if (activeSubscription.expirationDate != null) {
        return DateTime.tryParse(activeSubscription.expirationDate!);
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('RevenueCat Service: Error getting expiration date - $e');
      }
      return null;
    }
  }

  /// Check if subscription is in trial period
  static Future<bool> isInTrialPeriod() async {
    try {
      final customerInfo = await getCustomerInfo();
      if (customerInfo == null) return false;

      // Check if any active entitlement is in trial period
      for (final entitlement in customerInfo.entitlements.active.values) {
        if (entitlement.periodType == PeriodType.trial) {
          return true;
        }
      }

      return false;
    } catch (e) {
      if (kDebugMode) {
        print('RevenueCat Service: Error checking trial period - $e');
      }
      return false;
    }
  }

  /// Get subscription status summary
  static Future<Map<String, dynamic>> getSubscriptionStatus() async {
    try {
      final hasSubscription = await hasActiveSubscription();
      final isInTrial = await isInTrialPeriod();
      final expirationDate = await getSubscriptionExpirationDate();
      final activeSubscription = await getActiveSubscription();

      return {
        'hasActiveSubscription': hasSubscription,
        'isInTrialPeriod': isInTrial,
        'expirationDate': expirationDate?.toIso8601String(),
        'productIdentifier': activeSubscription?.productIdentifier,
        'willRenew': activeSubscription?.willRenew ?? false,
        'periodType': activeSubscription?.periodType.toString(),
      };
    } catch (e) {
      if (kDebugMode) {
        print('RevenueCat Service: Error getting subscription status - $e');
      }
      return {
        'hasActiveSubscription': false,
        'isInTrialPeriod': false,
        'expirationDate': null,
        'productIdentifier': null,
        'willRenew': false,
        'periodType': null,
      };
    }
  }

  /// Set debug logs enabled
  static Future<void> setDebugLogsEnabled(bool enabled) async {
    await Purchases.setLogLevel(enabled ? LogLevel.debug : LogLevel.info);
  }
}
