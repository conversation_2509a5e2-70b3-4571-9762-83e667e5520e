import 'package:flutter_test/flutter_test.dart';
import 'package:tripwisego/services/ai_itinerary_parser.dart';

void main() {
  group('Enhanced Content Parsing Tests', () {
    test('should parse table content correctly', () {
      const tableResponse = '''
Here's a quick comparison table for your trip:

| Destination | Best Time | Budget | Duration |
|-------------|-----------|--------|----------|
| Paris | Spring | \$2000 | 5 days |
| Tokyo | Fall | \$3000 | 7 days |
| Bali | Summer | \$1500 | 6 days |

This table shows the key details for each destination.
''';

      final parsedContent =
          AIItineraryParser.parseStructuredContent(tableResponse);

      // The content might be detected as table, mixed, or itinerary depending on parsing logic
      expect(parsedContent.type,
          isIn([ContentType.table, ContentType.mixed, ContentType.itinerary]));
      expect(parsedContent.rawContent, contains('Destination'));
      expect(parsedContent.rawContent, contains('Paris'));
      expect(parsedContent.rawContent, contains('Tokyo'));
      expect(parsedContent.rawContent, contains('Bali'));
    });

    test('should parse itinerary content correctly', () {
      const itineraryResponse = '''
# 5-Day Paris Itinerary

**Day 1: Arrival and City Center**
- Check into hotel
- Visit Notre-Dame Cathedral
- Walk along the Seine River
- Dinner at a local bistro

**Day 2: Museums and Culture**
- Morning at the Louvre Museum
- Afternoon at Musée d'Orsay
- Evening stroll through Montmartre

**Day 3: Iconic Landmarks**
- Visit the Eiffel Tower
- Explore Champs-Élysées
- Arc de Triomphe
- Shopping at Galeries Lafayette
''';

      final parsedContent =
          AIItineraryParser.parseStructuredContent(itineraryResponse);

      expect(parsedContent.type, ContentType.itinerary);
      expect(parsedContent.itinerary, isNotNull);

      final itinerary = parsedContent.itinerary!;
      expect(itinerary.title, contains('Paris'));
      expect(itinerary.daySpecificActivities, isNotNull);
      expect(itinerary.daySpecificActivities!.keys.length, greaterThan(0));
    });

    test('should parse list content correctly', () {
      const listResponse = '''
Here are the top travel tips for your trip:

• Pack light and bring versatile clothing
• Always carry a portable charger
• Download offline maps before traveling
• Keep copies of important documents
• Learn basic phrases in the local language

Additional recommendations:
- Book accommodations in advance
- Research local customs and etiquette
- Consider travel insurance
- Exchange currency before departure
''';

      final parsedContent =
          AIItineraryParser.parseStructuredContent(listResponse);

      expect(parsedContent.type, ContentType.list);
      expect(parsedContent.lists, isNotNull);
      expect(parsedContent.lists!.length, greaterThan(0));
    });

    test('should handle mixed content correctly', () {
      const mixedResponse = '''
# Travel Planning Guide

Here's a comparison of destinations:

| City | Cost | Rating |
|------|------|--------|
| Paris | High | 5/5 |
| Rome | Medium | 4/5 |

**Packing Checklist:**
• Passport and visa
• Travel insurance documents
• Comfortable walking shoes
• Weather-appropriate clothing

**Day 1 Itinerary:**
- Morning: Arrival and hotel check-in
- Afternoon: City walking tour
- Evening: Welcome dinner
''';

      final parsedContent =
          AIItineraryParser.parseStructuredContent(mixedResponse);

      // Should detect as mixed content or the most prominent type
      expect(parsedContent.type,
          isIn([ContentType.mixed, ContentType.table, ContentType.itinerary]));
      expect(parsedContent.title, isNotEmpty);
    });

    test('should handle plain content correctly', () {
      const plainResponse = '''
Paris is a beautiful city with rich history and culture. 
The best time to visit is during spring when the weather is mild.
You should definitely try the local cuisine and visit the major landmarks.
''';

      final parsedContent =
          AIItineraryParser.parseStructuredContent(plainResponse);

      expect(parsedContent.type, ContentType.plain);
      expect(parsedContent.title, isNotEmpty);
      expect(parsedContent.rawContent, equals(plainResponse));
    });
  });

  group('Integration Tests', () {
    test('should handle empty content', () {
      const emptyResponse = '';

      final parsedContent =
          AIItineraryParser.parseStructuredContent(emptyResponse);

      expect(parsedContent.type, ContentType.plain);
      expect(parsedContent.rawContent, equals(emptyResponse));
    });

    test('should handle content with special characters', () {
      const specialResponse = '''
Travel costs in €:
• Paris: €100/day
• Rome: €80/day
• Barcelona: €70/day
''';

      final parsedContent =
          AIItineraryParser.parseStructuredContent(specialResponse);

      expect(parsedContent.type, isIn([ContentType.list, ContentType.plain]));
      expect(parsedContent.rawContent, contains('€'));
    });

    test('should parse realistic itinerary example', () {
      const realisticItineraryExample = '''
# 5-Day Bali Itinerary

Alright, a 5-day Bali itinerary! Let's craft something amazing for you. This itinerary balances cultural immersion, natural beauty, and a touch of relaxation, with options to tailor it to your interests.

## Day 1: Arrival in Ubud
* Arrive at Ngurah Rai International Airport (DPS) in Denpasar
* Arrange a pre-booked airport transfer to Ubud (approx. 1.5 - 2 hours)
* Check into your Ubud accommodation
* Explore Ubud's center and visit the Ubud Art Market for souvenirs
* Dinner at a local warung - try Babi Guling or Nasi Goreng
* Consider a traditional Balinese dance performance

## Day 2: Ubud Cultural Exploration
* Visit the Tegalalang Rice Terraces - take photos and enjoy the views
* Explore Tirta Empul Temple, a significant Hindu water temple
* Head to the Sacred Monkey Forest Sanctuary
* Cooking class to learn authentic Balinese dishes
* Evening relaxation at your accommodation

## Day 3: Mount Batur Adventure
* Early start! Hire a driver to visit Mount Batur
* Hike to the summit for sunrise (challenging, approx. 2 hours)
* Coffee plantation tour - sample Kopi Luwak and different coffees
* Relax and rejuvenate with a Balinese massage
* Dinner at a local warung or restaurant

## Day 4: Transfer to Seminyak
* Transfer from Ubud to Seminyak (approx. 1.5 - 2 hours)
* Check into your Seminyak accommodation
* Relax on Seminyak Beach - rent a sunbed, swim, or try surfing
* Explore Seminyak's shops and boutiques
* Sunset cocktails at a beach club (Potato Head, Ku De Ta, or La Plancha)

## Day 5: Final Day
* Relax on the beach or enjoy a final Balinese breakfast
* Visit Uluwatu Temple, perched on a cliff overlooking the ocean
* Witness the Kecak Fire Dance performance at sunset
* Transfer to Ngurah Rai International Airport for departure
''';

      final parsedContent =
          AIItineraryParser.parseStructuredContent(realisticItineraryExample);

      // Should be detected as itinerary content
      expect(parsedContent.type, ContentType.itinerary);
      expect(parsedContent.rawContent, contains('5-Day Bali Itinerary'));
      expect(parsedContent.itinerary, isNotNull);
      if (parsedContent.itinerary != null) {
        expect(parsedContent.itinerary!.destinations, isNotEmpty);
      }
    });

    test('should parse realistic table example', () {
      const realisticTableExample = '''
# Best Time to Visit Thailand

Okay, let's figure out the best time for your Thailand adventure! Thailand has a wonderfully diverse climate, so "best" really depends on what you want to experience and which regions you're planning to visit. Here's a breakdown, keeping in mind Thailand generally has three seasons: hot, rainy, and cool.

## Thailand Weather by Season

| Season | Months      | Temperature | Rainfall | Crowds | Notes                                  |
| :----- | :---------- | :---------- | :------- | :----- | :------------------------------------- |
| Hot    | March-May   | 30-35°C      | Low      | Moderate | Hottest, best for beaches              |
| Rainy  | June-October | 27-32°C      | High     | Low    | Lush landscapes, fewer tourists         |
| Cool   | Nov-Feb     | 20-30°C      | Low      | High    | Pleasant, popular, festivals, peak season |

The cool season (November to February) is generally considered the best time to visit Thailand, with pleasant temperatures and minimal rainfall.
''';

      final parsedContent =
          AIItineraryParser.parseStructuredContent(realisticTableExample);

      // Should be detected as table content
      expect(parsedContent.type, ContentType.table);
      expect(parsedContent.rawContent, contains('Thailand'));
      expect(parsedContent.tables, isNotNull);
      expect(parsedContent.tables!.length, greaterThan(0));
    });
  });
}
