import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:lottie/lottie.dart';
import '../generated/l10n/app_localizations.dart';
import '../screens/paywall_screen.dart';

class SwipeLimitDialog extends StatefulWidget {
  final bool isSubscribed;
  final int currentCount;
  final int dailyLimit;
  final VoidCallback? onUpgrade;
  final VoidCallback? onDismiss;

  const SwipeLimitDialog({
    super.key,
    required this.isSubscribed,
    required this.currentCount,
    required this.dailyLimit,
    this.onUpgrade,
    this.onDismiss,
  });

  @override
  State<SwipeLimitDialog> createState() => _SwipeLimitDialogState();
}

class _SwipeLimitDialogState extends State<SwipeLimitDialog>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _scaleController;
  late AnimationController _fadeController;
  
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    
    // Initialize animation controllers
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // Initialize animations
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeInOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    // Start animations
    _startAnimations();
  }

  void _startAnimations() async {
    await Future.delayed(const Duration(milliseconds: 50));
    _fadeController.forward();
    _slideController.forward();
    _scaleController.forward();
  }

  @override
  void dispose() {
    _slideController.dispose();
    _scaleController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  void _handleDismiss() async {
    await _slideController.reverse();
    await _scaleController.reverse();
    await _fadeController.reverse();
    
    if (mounted) {
      Navigator.of(context).pop();
      widget.onDismiss?.call();
    }
  }

  void _handleUpgrade() async {
    await _slideController.reverse();
    await _scaleController.reverse();
    await _fadeController.reverse();
    
    if (mounted) {
      Navigator.of(context).pop();
      
      // Navigate to paywall screen
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => const PaywallScreen(),
        ),
      );
      
      widget.onUpgrade?.call();
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_slideController, _scaleController, _fadeController]),
      builder: (context, child) {
        return Material(
          color: Colors.black.withOpacity(0.5 * _fadeAnimation.value),
          child: Center(
            child: SlideTransition(
              position: _slideAnimation,
              child: ScaleTransition(
                scale: _scaleAnimation,
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: _buildDialogContent(),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildDialogContent() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 24),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: const Color(0xFFF7F9FC),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Animation/Icon
          _buildIcon(),
          
          const SizedBox(height: 20),
          
          // Title
          _buildTitle(),
          
          const SizedBox(height: 12),
          
          // Description
          _buildDescription(),
          
          const SizedBox(height: 24),
          
          // Progress indicator
          _buildProgressIndicator(),
          
          const SizedBox(height: 24),
          
          // Action buttons
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildIcon() {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        color: widget.isSubscribed 
            ? const Color(0xFF0D76FF).withOpacity(0.1)
            : Colors.orange.withOpacity(0.1),
        borderRadius: BorderRadius.circular(40),
      ),
      child: Icon(
        widget.isSubscribed ? Icons.check_circle : Icons.warning,
        size: 40,
        color: widget.isSubscribed 
            ? const Color(0xFF0D76FF)
            : Colors.orange,
      ),
    );
  }

  Widget _buildTitle() {
    return Text(
      widget.isSubscribed 
          ? 'Daily Limit Reached'
          : 'Free Swipes Used Up!',
      style: GoogleFonts.instrumentSans(
        fontSize: 24,
        fontWeight: FontWeight.w700,
        color: const Color(0xFF2D3748),
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildDescription() {
    return Text(
      widget.isSubscribed
          ? 'You\'ve reached your daily limit of ${widget.dailyLimit} swipes. Come back tomorrow for more!'
          : 'You\'ve used all ${widget.dailyLimit} free swipes today. Upgrade to get ${100} swipes per day!',
      style: GoogleFonts.instrumentSans(
        fontSize: 16,
        color: const Color(0xFF718096),
        height: 1.5,
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildProgressIndicator() {
    final progress = widget.currentCount / widget.dailyLimit;
    
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Today\'s Swipes',
              style: GoogleFonts.instrumentSans(
                fontSize: 14,
                color: const Color(0xFF718096),
              ),
            ),
            Text(
              '${widget.currentCount}/${widget.dailyLimit}',
              style: GoogleFonts.instrumentSans(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF2D3748),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: Colors.grey.withOpacity(0.2),
          valueColor: AlwaysStoppedAnimation<Color>(
            widget.isSubscribed 
                ? const Color(0xFF0D76FF)
                : Colors.orange,
          ),
          borderRadius: BorderRadius.circular(4),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    if (widget.isSubscribed) {
      return SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: _handleDismiss,
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF0D76FF),
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            elevation: 0,
          ),
          child: Text(
            'Got It',
            style: GoogleFonts.instrumentSans(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      );
    }

    return Column(
      children: [
        // Upgrade button
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _handleUpgrade,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF0D76FF),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 0,
            ),
            child: Text(
              'Upgrade Now',
              style: GoogleFonts.instrumentSans(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
        
        const SizedBox(height: 12),
        
        // Dismiss button
        SizedBox(
          width: double.infinity,
          child: TextButton(
            onPressed: _handleDismiss,
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Text(
              'Maybe Later',
              style: GoogleFonts.instrumentSans(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: const Color(0xFF718096),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

/// Show the swipe limit dialog
Future<void> showSwipeLimitDialog(
  BuildContext context, {
  required bool isSubscribed,
  required int currentCount,
  required int dailyLimit,
  VoidCallback? onUpgrade,
  VoidCallback? onDismiss,
}) {
  return showDialog(
    context: context,
    barrierDismissible: false,
    barrierColor: Colors.transparent,
    builder: (context) => SwipeLimitDialog(
      isSubscribed: isSubscribed,
      currentCount: currentCount,
      dailyLimit: dailyLimit,
      onUpgrade: onUpgrade,
      onDismiss: onDismiss,
    ),
  );
}
