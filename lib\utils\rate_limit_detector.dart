import 'package:flutter/foundation.dart';
import 'package:google_generative_ai/google_generative_ai.dart';
import 'package:http/http.dart' as http;

/// Utility class for detecting rate limiting and quota exceeded errors
/// from Gemini API responses
class RateLimitDetector {
  // Common rate limit error patterns
  static const List<String> _rateLimitErrorMessages = [
    'quota exceeded',
    'rate limit exceeded',
    'too many requests',
    'requests per minute exceeded',
    'requests per day exceeded',
    'api quota exceeded',
    'resource exhausted',
    'rate_limit_exceeded',
    'quota_exceeded',
    'too_many_requests',
  ];

  // HTTP status codes that indicate rate limiting
  static const List<int> _rateLimitStatusCodes = [
    429, // Too Many Requests
    503, // Service Unavailable (sometimes used for rate limiting)
  ];

  /// Check if an exception indicates rate limiting
  static bool isRateLimitError(dynamic error) {
    if (error == null) return false;

    try {
      // Check for GenerativeAIException (Gemini SDK specific)
      if (error is GenerativeAIException) {
        return _checkGenerativeAIException(error);
      }

      // Check for HTTP client exceptions
      if (error is http.ClientException) {
        return _checkHttpClientException(error);
      }

      // Check for general exceptions with error messages
      if (error is Exception) {
        return _checkExceptionMessage(error.toString());
      }

      // Check string error messages
      if (error is String) {
        return _checkExceptionMessage(error);
      }

      // Check error objects with message property
      if (error.toString().isNotEmpty) {
        return _checkExceptionMessage(error.toString());
      }

      return false;
    } catch (e) {
      if (kDebugMode) {
        print('RateLimitDetector: Error checking rate limit: $e');
      }
      return false;
    }
  }

  /// Check if an HTTP response indicates rate limiting
  static bool isRateLimitResponse(http.Response response) {
    try {
      // Check status code
      if (_rateLimitStatusCodes.contains(response.statusCode)) {
        if (kDebugMode) {
          print('RateLimitDetector: Rate limit detected - HTTP ${response.statusCode}');
        }
        return true;
      }

      // Check response body for rate limit messages
      final body = response.body.toLowerCase();
      for (final pattern in _rateLimitErrorMessages) {
        if (body.contains(pattern)) {
          if (kDebugMode) {
            print('RateLimitDetector: Rate limit detected in response body: $pattern');
          }
          return true;
        }
      }

      // Check response headers for rate limit indicators
      final headers = response.headers;
      if (headers.containsKey('x-ratelimit-remaining')) {
        final remaining = int.tryParse(headers['x-ratelimit-remaining'] ?? '');
        if (remaining != null && remaining <= 0) {
          if (kDebugMode) {
            print('RateLimitDetector: Rate limit detected - no remaining requests');
          }
          return true;
        }
      }

      return false;
    } catch (e) {
      if (kDebugMode) {
        print('RateLimitDetector: Error checking HTTP response: $e');
      }
      return false;
    }
  }

  /// Get a user-friendly error message for rate limiting
  static String getRateLimitErrorMessage() {
    return '''I'm currently experiencing high demand and have reached the request limit for this API key. I'm automatically switching to another key to continue serving you. Please try your request again in a moment.''';
  }

  /// Get retry delay based on error type (in seconds)
  static int getRetryDelay(dynamic error) {
    // Base delay of 1 second
    int baseDelay = 1;

    try {
      final errorMessage = error.toString().toLowerCase();

      // Longer delay for quota exceeded errors
      if (errorMessage.contains('quota exceeded') || 
          errorMessage.contains('daily limit')) {
        return 300; // 5 minutes for quota issues
      }

      // Medium delay for rate limit exceeded
      if (errorMessage.contains('rate limit') || 
          errorMessage.contains('too many requests')) {
        return 60; // 1 minute for rate limits
      }

      // Short delay for other errors
      return baseDelay;
    } catch (e) {
      return baseDelay;
    }
  }

  /// Check GenerativeAIException for rate limiting indicators
  static bool _checkGenerativeAIException(GenerativeAIException exception) {
    try {
      // Check the exception message
      final message = exception.message.toLowerCase();
      
      for (final pattern in _rateLimitErrorMessages) {
        if (message.contains(pattern)) {
          if (kDebugMode) {
            print('RateLimitDetector: Rate limit detected in GenerativeAIException: $pattern');
          }
          return true;
        }
      }

      // Check if it's a specific rate limit exception type
      // (This may vary based on the Gemini SDK version)
      if (exception.toString().toLowerCase().contains('rate') ||
          exception.toString().toLowerCase().contains('quota')) {
        if (kDebugMode) {
          print('RateLimitDetector: Rate limit detected in GenerativeAIException type');
        }
        return true;
      }

      return false;
    } catch (e) {
      if (kDebugMode) {
        print('RateLimitDetector: Error checking GenerativeAIException: $e');
      }
      return false;
    }
  }

  /// Check HTTP client exception for rate limiting indicators
  static bool _checkHttpClientException(http.ClientException exception) {
    try {
      final message = exception.message.toLowerCase();
      
      for (final pattern in _rateLimitErrorMessages) {
        if (message.contains(pattern)) {
          if (kDebugMode) {
            print('RateLimitDetector: Rate limit detected in HTTP ClientException: $pattern');
          }
          return true;
        }
      }

      return false;
    } catch (e) {
      if (kDebugMode) {
        print('RateLimitDetector: Error checking HTTP ClientException: $e');
      }
      return false;
    }
  }

  /// Check exception message for rate limiting indicators
  static bool _checkExceptionMessage(String message) {
    try {
      final lowerMessage = message.toLowerCase();
      
      for (final pattern in _rateLimitErrorMessages) {
        if (lowerMessage.contains(pattern)) {
          if (kDebugMode) {
            print('RateLimitDetector: Rate limit detected in message: $pattern');
          }
          return true;
        }
      }

      return false;
    } catch (e) {
      if (kDebugMode) {
        print('RateLimitDetector: Error checking exception message: $e');
      }
      return false;
    }
  }

  /// Extract retry-after value from headers (if available)
  static int? getRetryAfterFromHeaders(Map<String, String> headers) {
    try {
      final retryAfter = headers['retry-after'] ?? headers['Retry-After'];
      if (retryAfter != null) {
        return int.tryParse(retryAfter);
      }
      return null;
    } catch (e) {
      return null;
    }
  }
}
