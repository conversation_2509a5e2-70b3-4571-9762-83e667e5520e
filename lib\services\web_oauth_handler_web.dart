// Web-specific implementation using dart:html
// ignore: avoid_web_libraries_in_flutter
import 'dart:html' as html;

// Export the html window for web platforms
html.Window get window => html.window;

// Web-specific functions for OAuth callback handling
String? getOAuthCallbackData() {
  try {
    final storage = html.window.sessionStorage;
    return storage['oauth_callback'];
  } catch (error) {
    return null;
  }
}

void removeOAuthCallbackData() {
  try {
    final storage = html.window.sessionStorage;
    storage.remove('oauth_callback');
  } catch (error) {
    // Ignore errors
  }
}

void setOAuthCallbackData(String data) {
  try {
    final storage = html.window.sessionStorage;
    storage['oauth_callback'] = data;
  } catch (error) {
    // Ignore errors
  }
}
