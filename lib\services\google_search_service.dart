import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/network_helper.dart';

class GoogleSearchService {
  // Google Custom Search API configuration
  static const String _searchEngineId = '43c1b4436c0b14c2d';
  static const String _baseUrl = 'https://www.googleapis.com/customsearch/v1';

  // Daily usage tracking
  static const String _usageCountKey = 'google_search_usage_count';
  static const String _lastUsageDateKey = 'google_search_last_usage_date';
  static const int _maxDailyUsage = 5;

  // TODO: Replace with actual Google Custom Search API key
  // Get your API key from: https://developers.google.com/custom-search/v1/introduction
  // In production, store this securely (environment variables, secure storage, etc.)
  static const String _apiKey =
      'AIzaSyBotLHd_nSloUY8_bw2Rk_O3H-I3dezYkU'; // Replace with actual key

  /// Check if user has remaining searches for today
  static Future<bool> hasRemainingSearches() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final today = DateTime.now().toIso8601String().split('T')[0];
      final lastUsageDate = prefs.getString(_lastUsageDateKey) ?? '';

      // Reset count if it's a new day
      if (lastUsageDate != today) {
        await prefs.setInt(_usageCountKey, 0);
        await prefs.setString(_lastUsageDateKey, today);
        return true;
      }

      final usageCount = prefs.getInt(_usageCountKey) ?? 0;
      return usageCount < _maxDailyUsage;
    } catch (error) {
      if (kDebugMode) {
        print(
            'Google Search Service: Error checking remaining searches: $error');
      }
      return false;
    }
  }

  /// Get remaining search count for today
  static Future<int> getRemainingSearchCount() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final today = DateTime.now().toIso8601String().split('T')[0];
      final lastUsageDate = prefs.getString(_lastUsageDateKey) ?? '';

      // Reset count if it's a new day
      if (lastUsageDate != today) {
        await prefs.setInt(_usageCountKey, 0);
        await prefs.setString(_lastUsageDateKey, today);
        return _maxDailyUsage;
      }

      final usageCount = prefs.getInt(_usageCountKey) ?? 0;
      return _maxDailyUsage - usageCount;
    } catch (error) {
      if (kDebugMode) {
        print(
            'Google Search Service: Error getting remaining search count: $error');
      }
      return 0;
    }
  }

  /// Increment usage count
  static Future<void> _incrementUsageCount() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final today = DateTime.now().toIso8601String().split('T')[0];
      final usageCount = prefs.getInt(_usageCountKey) ?? 0;

      await prefs.setInt(_usageCountKey, usageCount + 1);
      await prefs.setString(_lastUsageDateKey, today);

      if (kDebugMode) {
        print(
            'Google Search Service: Usage count incremented to ${usageCount + 1}');
      }
    } catch (error) {
      if (kDebugMode) {
        print('Google Search Service: Error incrementing usage count: $error');
      }
    }
  }

  /// Perform web search using Google Custom Search API
  static Future<GoogleSearchResult> searchWeb(String query) async {
    try {
      // Check if user has remaining searches
      if (!await hasRemainingSearches()) {
        throw GoogleSearchException(
          'Daily search limit reached. You can perform $_maxDailyUsage searches per day.',
          GoogleSearchErrorType.dailyLimitReached,
        );
      }

      // Check internet connectivity
      if (!await NetworkHelper.hasInternetConnection()) {
        throw GoogleSearchException(
          'No internet connection. Please check your network and try again.',
          GoogleSearchErrorType.networkError,
        );
      }

      if (kDebugMode) {
        print('Google Search Service: Searching for: $query');
      }

      // Build search URL
      final uri = Uri.parse(_baseUrl).replace(queryParameters: {
        'key': _apiKey,
        'cx': _searchEngineId,
        'q': query,
        'num': '5', // Limit to 5 results
        'safe': 'active', // Safe search
      });

      // Make API request with retry mechanism
      final response = await NetworkHelper.retryNetworkOperation(
        () async {
          return await http.get(
            uri,
            headers: {
              'Accept': 'application/json',
              'User-Agent': 'TripWiseGO/1.0',
            },
          ).timeout(const Duration(seconds: 15));
        },
        maxRetries: 2,
        delay: const Duration(seconds: 1),
      );

      if (kDebugMode) {
        print(
            'Google Search Service: API response status: ${response.statusCode}');
      }

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        // Increment usage count on successful search
        await _incrementUsageCount();

        return GoogleSearchResult.fromJson(data);
      } else if (response.statusCode == 403) {
        throw GoogleSearchException(
          'Search quota exceeded. Please try again later.',
          GoogleSearchErrorType.quotaExceeded,
        );
      } else if (response.statusCode == 400) {
        throw GoogleSearchException(
          'Invalid search query. Please try a different search term.',
          GoogleSearchErrorType.invalidQuery,
        );
      } else {
        throw GoogleSearchException(
          'Search service temporarily unavailable. Please try again later.',
          GoogleSearchErrorType.serviceUnavailable,
        );
      }
    } catch (error) {
      if (error is GoogleSearchException) {
        rethrow;
      }

      if (kDebugMode) {
        print('Google Search Service: Unexpected error: $error');
      }

      throw GoogleSearchException(
        NetworkHelper.getNetworkErrorMessage(error),
        GoogleSearchErrorType.networkError,
      );
    }
  }
}

/// Google Search result model
class GoogleSearchResult {
  final List<GoogleSearchItem> items;
  final String? searchInformation;
  final int totalResults;

  GoogleSearchResult({
    required this.items,
    this.searchInformation,
    required this.totalResults,
  });

  factory GoogleSearchResult.fromJson(Map<String, dynamic> json) {
    final items = <GoogleSearchItem>[];

    if (json['items'] != null) {
      for (final item in json['items']) {
        items.add(GoogleSearchItem.fromJson(item));
      }
    }

    final searchInfo = json['searchInformation'];
    final totalResults = searchInfo != null
        ? int.tryParse(searchInfo['totalResults'] ?? '0') ?? 0
        : 0;

    return GoogleSearchResult(
      items: items,
      searchInformation: searchInfo?['formattedSearchTime'],
      totalResults: totalResults,
    );
  }
}

/// Individual search result item
class GoogleSearchItem {
  final String title;
  final String link;
  final String snippet;
  final String? displayLink;

  GoogleSearchItem({
    required this.title,
    required this.link,
    required this.snippet,
    this.displayLink,
  });

  factory GoogleSearchItem.fromJson(Map<String, dynamic> json) {
    return GoogleSearchItem(
      title: json['title'] ?? '',
      link: json['link'] ?? '',
      snippet: json['snippet'] ?? '',
      displayLink: json['displayLink'],
    );
  }
}

/// Google Search exception types
enum GoogleSearchErrorType {
  dailyLimitReached,
  quotaExceeded,
  networkError,
  invalidQuery,
  serviceUnavailable,
}

/// Custom exception for Google Search errors
class GoogleSearchException implements Exception {
  final String message;
  final GoogleSearchErrorType type;

  GoogleSearchException(this.message, this.type);

  @override
  String toString() => message;
}
