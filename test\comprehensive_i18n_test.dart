import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:tripwisego/services/localization_service.dart';
import 'package:tripwisego/generated/l10n/app_localizations.dart';
import 'package:tripwisego/screens/plan_page.dart';
import 'package:tripwisego/screens/wanderly_chat_screen.dart';
import 'package:tripwisego/screens/match_screen.dart';
import 'package:tripwisego/screens/profile_screen.dart';
import 'package:tripwisego/screens/homepage_screen.dart';

void main() {
  group('Comprehensive i18n Translation Coverage Tests', () {
    
    // Test all supported languages have basic strings
    for (final languageEntry in LocalizationService.supportedLanguages.entries) {
      final languageCode = languageEntry.key;
      final languageData = languageEntry.value;
      
      testWidgets('$languageCode (${languageData['nativeName']}) - Basic strings are available', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            locale: Locale(languageCode, languageData['countryCode']!),
            localizationsDelegates: [
              AppLocalizations.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: LocalizationService.supportedLocales,
            home: Builder(
              builder: (context) {
                final l10n = AppLocalizations.of(context);
                return Scaffold(
                  body: Column(
                    children: [
                      // Test basic app strings
                      Text(l10n.appTitle),
                      Text(l10n.welcome),
                      Text(l10n.home),
                      Text(l10n.profile),
                      Text(l10n.chat),
                      Text(l10n.match),
                      
                      // Test plan page strings
                      Text(l10n.noItineraryFound),
                      Text(l10n.askAiToCreateTravelPlan),
                      Text(l10n.itineraryOverview),
                      Text(l10n.dayNumber(1)),
                      
                      // Test chat screen strings
                      Text(l10n.hiImWanderlyAi),
                      Text(l10n.yourTravelAiAssistant),
                      Text(l10n.aiAssistant),
                      Text(l10n.chatHistory),
                      Text(l10n.newChat),
                      Text(l10n.addImage),
                      Text(l10n.camera),
                      Text(l10n.gallery),
                      Text(l10n.listening),
                      Text(l10n.deleteChat),
                      Text(l10n.like),
                      Text(l10n.dislike),
                      Text(l10n.copy),
                      Text(l10n.regenerate),
                      
                      // Test match screen strings
                      Text(l10n.congratulations),
                      Text(l10n.itsAMatch),
                      Text(l10n.matchedPreferences),
                      Text(l10n.addToItinerary),
                      Text(l10n.keepSwiping),
                      
                      // Test profile screen strings
                      Text(l10n.editProfile),
                      Text(l10n.saveChanges),
                      Text(l10n.cancel),
                      Text(l10n.username),
                      Text(l10n.email),
                      Text(l10n.language),
                    ],
                  ),
                );
              },
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Verify that all strings are displayed (not empty)
        expect(find.text('TripWiseGo'), findsOneWidget);
        expect(find.byType(Text), findsWidgets);
        
        // Verify no English text appears when other languages are selected
        if (languageCode != 'en') {
          expect(find.text('Welcome'), findsNothing);
          expect(find.text('Home'), findsNothing);
          expect(find.text('Profile'), findsNothing);
          expect(find.text('Chat'), findsNothing);
          expect(find.text('Match'), findsNothing);
          expect(find.text('No Itinerary Found'), findsNothing);
          expect(find.text('AI Assistant'), findsNothing);
          expect(find.text('Chat History'), findsNothing);
          expect(find.text('New Chat'), findsNothing);
          expect(find.text('Add Image'), findsNothing);
          expect(find.text('Camera'), findsNothing);
          expect(find.text('Gallery'), findsNothing);
          expect(find.text('Listening...'), findsNothing);
          expect(find.text('Delete Chat'), findsNothing);
          expect(find.text('Like'), findsNothing);
          expect(find.text('Dislike'), findsNothing);
          expect(find.text('Copy'), findsNothing);
          expect(find.text('Regenerate'), findsNothing);
          expect(find.text('Congratulations'), findsNothing);
          expect(find.text('It\'s a match'), findsNothing);
          expect(find.text('Matched Preferences:'), findsNothing);
          expect(find.text('Add to Itinerary'), findsNothing);
          expect(find.text('Keep swiping'), findsNothing);
          expect(find.text('Edit Profile'), findsNothing);
          expect(find.text('Save Changes'), findsNothing);
          expect(find.text('Cancel'), findsNothing);
          expect(find.text('Username'), findsNothing);
          expect(find.text('Email'), findsNothing);
          expect(find.text('Language'), findsNothing);
        }
      });
    }

    testWidgets('RTL layout works correctly for Arabic', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          locale: const Locale('ar', 'SA'),
          localizationsDelegates: [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: LocalizationService.supportedLocales,
          builder: (context, child) {
            final locale = Localizations.localeOf(context);
            final isRTL = LocalizationService.isRTL(locale.languageCode);
            return Directionality(
              textDirection: isRTL ? TextDirection.rtl : TextDirection.ltr,
              child: child!,
            );
          },
          home: Builder(
            builder: (context) {
              final l10n = AppLocalizations.of(context);
              return Scaffold(
                body: Column(
                  children: [
                    Text(l10n.welcome),
                    Text(l10n.hiImWanderlyAi),
                    Text(l10n.yourTravelAiAssistant),
                  ],
                ),
              );
            },
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify RTL text is displayed
      expect(find.text('مرحبا'), findsOneWidget);
      expect(find.byType(Text), findsWidgets);
      
      // Verify text direction is RTL
      final directionality = tester.widget<Directionality>(find.byType(Directionality));
      expect(directionality.textDirection, equals(TextDirection.rtl));
    });

    testWidgets('Language switching works without app restart', (WidgetTester tester) async {
      final localizationService = LocalizationService();
      
      await tester.pumpWidget(
        MaterialApp(
          locale: localizationService.currentLocale,
          localizationsDelegates: [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: LocalizationService.supportedLocales,
          home: Builder(
            builder: (context) {
              final l10n = AppLocalizations.of(context);
              return Scaffold(
                body: Column(
                  children: [
                    Text(l10n.welcome, key: const Key('welcome_text')),
                    ElevatedButton(
                      onPressed: () async {
                        await localizationService.setLanguage('fr');
                      },
                      child: const Text('Switch to French'),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Initially should show English
      expect(find.text('Welcome'), findsOneWidget);

      // Switch to French
      await tester.tap(find.text('Switch to French'));
      await tester.pumpAndSettle();

      // Should now show French (after rebuilding with new locale)
      // Note: In a real app, this would require listening to locale changes
      // and rebuilding the MaterialApp with the new locale
    });

    test('All required languages are supported', () {
      final supportedLanguages = LocalizationService.supportedLanguages;
      
      // Verify we have exactly 14 languages
      expect(supportedLanguages.length, equals(14));
      
      // Verify all required languages are present
      final requiredLanguages = [
        'en', 'fr', 'it', 'zh', 'ja', 'ko', 'id', 'tl', 
        'th', 'ar', 'hi', 'es', 'ru', 'pt'
      ];
      
      for (final lang in requiredLanguages) {
        expect(supportedLanguages.containsKey(lang), isTrue, 
               reason: 'Language $lang should be supported');
      }
    });

    test('RTL detection works correctly', () {
      expect(LocalizationService.isRTL('ar'), isTrue);
      expect(LocalizationService.isRTL('en'), isFalse);
      expect(LocalizationService.isRTL('fr'), isFalse);
      expect(LocalizationService.isRTL('zh'), isFalse);
      expect(LocalizationService.isRTL('ja'), isFalse);
      expect(LocalizationService.isRTL('ko'), isFalse);
      expect(LocalizationService.isRTL('id'), isFalse);
      expect(LocalizationService.isRTL('tl'), isFalse);
      expect(LocalizationService.isRTL('th'), isFalse);
      expect(LocalizationService.isRTL('hi'), isFalse);
      expect(LocalizationService.isRTL('es'), isFalse);
      expect(LocalizationService.isRTL('ru'), isFalse);
      expect(LocalizationService.isRTL('pt'), isFalse);
      expect(LocalizationService.isRTL('it'), isFalse);
    });
  });
}
