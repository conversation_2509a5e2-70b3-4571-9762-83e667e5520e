import 'package:flutter/foundation.dart';
import 'package:flutter/painting.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';

/// Custom cache manager for optimized image caching with memory management
class ImageCacheManager {
  static final ImageCacheManager _instance = ImageCacheManager._internal();
  factory ImageCacheManager() => _instance;
  ImageCacheManager._internal();

  // Cache configuration
  static const String _cacheKey = 'tripwisego_image_cache';
  static const Duration _maxAge = Duration(days: 7);
  static const int _maxNrOfCacheObjects = 200;
  static const int _maxMemoryCacheObjects = 50;

  late final CacheManager _cacheManager;

  /// Initialize the cache manager with optimized settings
  void initialize() {
    _cacheManager = CacheManager(
      Config(
        _cacheKey,
        stalePeriod: _maxAge,
        maxNrOfCacheObjects: _maxNrOfCacheObjects,
        repo: JsonCacheInfoRepository(databaseName: _cacheKey),
        fileService: HttpFileService(),
      ),
    );

    // Configure memory cache
    PaintingBinding.instance.imageCache.maximumSize = _maxMemoryCacheObjects;
    PaintingBinding.instance.imageCache.maximumSizeBytes = 50 << 20; // 50MB

    if (kDebugMode) {
      print(
          'ImageCacheManager: Initialized with $_maxNrOfCacheObjects disk cache objects and $_maxMemoryCacheObjects memory cache objects');
    }
  }

  /// Get the cache manager instance
  CacheManager get cacheManager => _cacheManager;

  /// Generate low-resolution URL for progressive loading
  String generateLowResUrl(String originalUrl) {
    if (originalUrl.isEmpty) return originalUrl;

    // For Unsplash URLs, create a low-res version
    if (originalUrl.contains('unsplash.com')) {
      return originalUrl.contains('?')
          ? '$originalUrl&q=30&w=200&h=300&blur=2'
          : '$originalUrl?q=30&w=200&h=300&blur=2';
    }

    // For other URLs, try to add low quality parameters
    return originalUrl.contains('?')
        ? '$originalUrl&q=30&w=200'
        : '$originalUrl?q=30&w=200';
  }

  /// Generate high-resolution URL based on target size
  String generateHighResUrl(String originalUrl,
      {int? targetWidth, int? targetHeight}) {
    if (originalUrl.isEmpty) return originalUrl;

    final width = targetWidth ?? 800;
    final height = targetHeight ?? 1000;

    // For Unsplash URLs, optimize based on target size
    if (originalUrl.contains('unsplash.com')) {
      return originalUrl.contains('?')
          ? '$originalUrl&q=85&w=$width&h=$height&fit=crop'
          : '$originalUrl?q=85&w=$width&h=$height&fit=crop';
    }

    // For other URLs, add quality parameters if possible
    return originalUrl.contains('?')
        ? '$originalUrl&q=85&w=$width'
        : '$originalUrl?q=85&w=$width';
  }

  /// Preload image into cache
  Future<void> preloadImage(String imageUrl) async {
    try {
      await _cacheManager.downloadFile(imageUrl);
      if (kDebugMode) {
        print('ImageCacheManager: Preloaded $imageUrl');
      }
    } catch (error) {
      if (kDebugMode) {
        print('ImageCacheManager: Failed to preload $imageUrl - $error');
      }
    }
  }

  /// Check if image is cached
  Future<bool> isImageCached(String imageUrl) async {
    try {
      final fileInfo = await _cacheManager.getFileFromCache(imageUrl);
      return fileInfo != null;
    } catch (error) {
      return false;
    }
  }

  /// Get cache statistics
  Map<String, dynamic> getCacheStats() {
    try {
      final memoryCache = PaintingBinding.instance.imageCache;

      return {
        'memory_cache_count': memoryCache.currentSize,
        'memory_cache_size_bytes': memoryCache.currentSizeBytes,
        'memory_cache_max_size': memoryCache.maximumSize,
        'memory_cache_max_bytes': memoryCache.maximumSizeBytes,
        'disk_cache_max_objects': _maxNrOfCacheObjects,
      };
    } catch (error) {
      if (kDebugMode) {
        print('ImageCacheManager: Error getting cache stats - $error');
      }
      return {};
    }
  }

  /// Clear memory cache
  void clearMemoryCache() {
    PaintingBinding.instance.imageCache.clear();
    PaintingBinding.instance.imageCache.clearLiveImages();

    if (kDebugMode) {
      print('ImageCacheManager: Memory cache cleared');
    }
  }

  /// Clear disk cache
  Future<void> clearDiskCache() async {
    try {
      await _cacheManager.emptyCache();
      if (kDebugMode) {
        print('ImageCacheManager: Disk cache cleared');
      }
    } catch (error) {
      if (kDebugMode) {
        print('ImageCacheManager: Error clearing disk cache - $error');
      }
    }
  }

  /// Clear all caches
  Future<void> clearAllCaches() async {
    clearMemoryCache();
    await clearDiskCache();
  }

  /// Optimize cache based on memory pressure
  void optimizeCache() {
    final memoryCache = PaintingBinding.instance.imageCache;

    // If memory usage is high, reduce cache size temporarily
    if (memoryCache.currentSizeBytes > (memoryCache.maximumSizeBytes * 0.8)) {
      memoryCache.maximumSize = (memoryCache.maximumSize * 0.7).round();

      if (kDebugMode) {
        print(
            'ImageCacheManager: Reduced memory cache size due to memory pressure');
      }
    }
  }

  /// Dispose of the cache manager
  void dispose() {
    // Cache manager will be disposed automatically
    if (kDebugMode) {
      print('ImageCacheManager: Disposed');
    }
  }
}
