# Google AdMob Integration Setup Guide

This guide provides complete step-by-step instructions for setting up Google AdMob interstitial ads in your TripWiseGo Flutter app.

## Overview

The implementation includes:
- Interstitial ads displayed every 7 swipes for free users
- No ads for users with active RevenueCat subscriptions
- Proper error handling and fallback behavior
- Test ads for development and production ads for release

## Prerequisites

- Google AdMob account
- Google Play Console account (for Android)
- Apple Developer account (for iOS)
- Flutter development environment set up

## Step 1: Create Google AdMob Account

1. Go to [Google AdMob](https://admob.google.com/)
2. Sign in with your Google account
3. Click "Get started" and follow the setup process
4. Accept the AdMob Terms & Conditions

## Step 2: Add Your App to AdMob

### For Android:
1. In AdMob console, click "Apps" → "Add app"
2. Select "Android" as the platform
3. Choose "Yes, it's listed on Google Play" (if published) or "No" (if not published yet)
4. Enter your app name: "<PERSON>WiseGo"
5. If published, enter your Google Play Store URL
6. Click "Add app"

### For iOS:
1. In AdMob console, click "Apps" → "Add app"
2. Select "iOS" as the platform
3. Choose "Yes, it's listed on the App Store" (if published) or "No" (if not published yet)
4. Enter your app name: "TripWiseGo"
5. If published, enter your App Store URL
6. Click "Add app"

## Step 3: Create Ad Units

### Create Interstitial Ad Unit:
1. In your app dashboard, click "Ad units" → "Add ad unit"
2. Select "Interstitial"
3. Enter ad unit name: "TripWiseGo Interstitial"
4. Click "Create ad unit"
5. **Copy the Ad unit ID** - you'll need this later

Repeat this process for both Android and iOS platforms.

## Step 4: Configure App-level Settings

### Android Configuration:

1. **Add AdMob App ID to AndroidManifest.xml:**
   ```xml
   <!-- android/app/src/main/AndroidManifest.xml -->
   <application>
       <!-- Add this meta-data tag -->
       <meta-data
           android:name="com.google.android.gms.ads.APPLICATION_ID"
           android:value="ca-app-pub-XXXXXXXXXXXXXXXX~XXXXXXXXXX"/>
       
       <!-- Your existing activity configurations -->
   </application>
   ```

2. **Update android/app/build.gradle:**
   ```gradle
   dependencies {
       // Add this line
       implementation 'com.google.android.gms:play-services-ads:22.6.0'
       // Your existing dependencies
   }
   ```

### iOS Configuration:

1. **Add AdMob App ID to Info.plist:**
   ```xml
   <!-- ios/Runner/Info.plist -->
   <dict>
       <!-- Add this key-value pair -->
       <key>GADApplicationIdentifier</key>
       <string>ca-app-pub-XXXXXXXXXXXXXXXX~XXXXXXXXXX</string>
       
       <!-- Your existing configurations -->
   </dict>
   ```

2. **Update ios/Podfile:**
   ```ruby
   # Uncomment this line to define a global platform for your project
   platform :ios, '12.0'
   
   target 'Runner' do
     use_frameworks!
     use_modular_headers!
   
     flutter_install_all_ios_pods File.dirname(File.realpath(__FILE__))
     
     # Add this line for AdMob
     pod 'Google-Mobile-Ads-SDK'
   end
   ```

## Step 5: Update Configuration Files

1. **Update lib/config/admob_config.dart:**
   ```dart
   // Replace these with your actual ad unit IDs from AdMob console
   static const String _androidInterstitialAdUnitId = 'ca-app-pub-YOUR_ANDROID_AD_UNIT_ID';
   static const String _iosInterstitialAdUnitId = 'ca-app-pub-YOUR_IOS_AD_UNIT_ID';
   ```

2. **Add your test device IDs (optional but recommended):**
   ```dart
   static const List<String> testDeviceIds = [
     'YOUR_ANDROID_TEST_DEVICE_ID',
     'YOUR_IOS_TEST_DEVICE_ID',
   ];
   ```

## Step 6: Install Dependencies

Run the following command in your project root:
```bash
flutter pub get
```

## Step 7: Platform-specific Setup

### Android:
1. Run `flutter clean`
2. Run `flutter pub get`
3. Run `cd android && ./gradlew clean` (or `gradlew clean` on Windows)

### iOS:
1. Run `flutter clean`
2. Run `flutter pub get`
3. Run `cd ios && pod install`

## Step 8: Testing

### Development Testing:
1. The app automatically uses test ads in debug mode
2. Test ads will show "Test Ad" label
3. You can click test ads without affecting your AdMob account

### Finding Your Test Device ID:
1. Run your app in debug mode
2. Check the console logs for a message like:
   ```
   I/Ads: Use RequestConfiguration.Builder().setTestDeviceIds(Arrays.asList("33BE2250B43518CCDA7DE426D04EE231"))
   ```
3. Add this ID to the `testDeviceIds` list in `admob_config.dart`

### Production Testing:
1. Build a release version of your app
2. Install on a test device
3. Verify that real ads are displayed (not test ads)
4. Test the ad frequency (every 7 swipes)
5. Test subscription logic (no ads for subscribers)

## Step 9: Verification Checklist

- [ ] AdMob account created and verified
- [ ] Android app added to AdMob with correct package name
- [ ] iOS app added to AdMob with correct bundle ID
- [ ] Interstitial ad units created for both platforms
- [ ] App IDs added to AndroidManifest.xml and Info.plist
- [ ] Ad unit IDs updated in admob_config.dart
- [ ] Dependencies installed and platforms configured
- [ ] Test ads working in debug mode
- [ ] Real ads working in release mode
- [ ] Ad frequency working (every 7 swipes)
- [ ] Subscription logic working (no ads for subscribers)

## Troubleshooting

### Common Issues:

1. **"No ad to show" error:**
   - Check internet connection
   - Verify ad unit IDs are correct
   - Ensure app is properly configured in AdMob console

2. **Test ads not showing:**
   - Verify test device ID is correct
   - Check that app is running in debug mode
   - Ensure AdMob SDK is properly initialized

3. **Real ads not showing in production:**
   - Verify ad unit IDs are correct for production
   - Check that app is approved in AdMob (may take 24-48 hours)
   - Ensure app is published or in testing track

4. **Ads showing for subscribed users:**
   - Verify RevenueCat integration is working
   - Check subscription status in logs
   - Test with a real subscription

### Debug Information:

You can check the ad configuration and status using:
```dart
// In your debug code
final adConfig = AdMobConfig.configSummary;
print('AdMob Config: $adConfig');

final adFrequencyInfo = await AdFrequencyService.getDebugInfo();
print('Ad Frequency Info: $adFrequencyInfo');
```

## Important Notes

1. **Revenue Sharing:** Google takes a percentage of ad revenue
2. **Policy Compliance:** Ensure your app complies with Google AdMob policies
3. **User Experience:** Ads are designed to be non-intrusive (every 7 swipes)
4. **Subscription Integration:** Ads respect user subscription status
5. **Testing:** Always test thoroughly before releasing to production

## Support

For additional help:
- [Google AdMob Help Center](https://support.google.com/admob/)
- [Flutter Google Mobile Ads Plugin Documentation](https://pub.dev/packages/google_mobile_ads)
- [AdMob Policy Center](https://support.google.com/admob/answer/6128543)
