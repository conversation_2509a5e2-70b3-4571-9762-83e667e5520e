import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../config/supabase_config.dart';

class ImageUploadService {
  static const String _bucketName = 'collaborative-itinerary-images';
  static final SupabaseClient _client = SupabaseConfig.client;

  /// Upload an image for a collaborative itinerary activity
  /// Returns the public URL of the uploaded image
  static Future<String> uploadActivityImage({
    required String itineraryId,
    required String destination,
    required String activityName,
    required XFile imageFile,
    Function(double)? onProgress,
  }) async {
    try {
      final user = _client.auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Validate file size (10MB limit)
      final fileSize = await imageFile.length();
      if (fileSize > 10 * 1024 * 1024) {
        throw Exception('Image size must be less than 10MB');
      }

      // Validate file type
      final fileName = imageFile.name.toLowerCase();
      if (!fileName.endsWith('.jpg') &&
          !fileName.endsWith('.jpeg') &&
          !fileName.endsWith('.png') &&
          !fileName.endsWith('.webp') &&
          !fileName.endsWith('.gif')) {
        throw Exception('Only JPG, PNG, WebP, and GIF images are supported');
      }

      // Generate unique file path
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileExtension = fileName.split('.').last;
      final sanitizedDestination = _sanitizeFileName(destination);
      final sanitizedActivity = _sanitizeFileName(activityName);
      final filePath =
          '$itineraryId/$sanitizedDestination/$sanitizedActivity-$timestamp.$fileExtension';

      // Read file bytes
      final Uint8List fileBytes;
      if (kIsWeb) {
        fileBytes = await imageFile.readAsBytes();
      } else {
        fileBytes = await File(imageFile.path).readAsBytes();
      }

      // Upload to Supabase Storage
      final uploadResponse =
          await _client.storage.from(_bucketName).uploadBinary(
                filePath,
                fileBytes,
                fileOptions: FileOptions(
                  contentType: _getContentType(fileExtension),
                  upsert: false,
                ),
              );

      if (uploadResponse.isEmpty) {
        throw Exception('Failed to upload image');
      }

      // Get public URL
      final publicUrl =
          _client.storage.from(_bucketName).getPublicUrl(filePath);

      if (kDebugMode) {
        print('Image uploaded successfully: $publicUrl');
      }

      return publicUrl;
    } catch (e) {
      if (kDebugMode) {
        print('Error uploading image: $e');
      }
      rethrow;
    }
  }

  /// Delete an image from Supabase Storage
  static Future<bool> deleteActivityImage(String imageUrl) async {
    try {
      // Extract file path from URL
      final uri = Uri.parse(imageUrl);
      final pathSegments = uri.pathSegments;

      // Find the bucket name in the path and extract the file path
      final bucketIndex = pathSegments.indexOf(_bucketName);
      if (bucketIndex == -1 || bucketIndex >= pathSegments.length - 1) {
        throw Exception('Invalid image URL format');
      }

      final filePath = pathSegments.sublist(bucketIndex + 1).join('/');

      // Delete from Supabase Storage
      await _client.storage.from(_bucketName).remove([filePath]);

      if (kDebugMode) {
        print('Image deleted successfully: $filePath');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting image: $e');
      }
      return false;
    }
  }

  /// Pick image from gallery or camera
  static Future<XFile?> pickImage({
    ImageSource source = ImageSource.gallery,
    int imageQuality = 85,
    double? maxWidth,
    double? maxHeight,
  }) async {
    try {
      final ImagePicker picker = ImagePicker();

      final XFile? image = await picker.pickImage(
        source: source,
        imageQuality: imageQuality,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
      );

      return image;
    } catch (e) {
      if (kDebugMode) {
        print('Error picking image: $e');
      }
      return null;
    }
  }

  /// Show image source selection dialog
  static Future<XFile?> showImageSourceDialog({
    required Function(ImageSource) onSourceSelected,
    int imageQuality = 85,
    double? maxWidth,
    double? maxHeight,
  }) async {
    // This will be called from the UI layer
    // The UI will handle showing the dialog and call pickImage with the selected source
    return null;
  }

  /// Sanitize file name to be safe for storage
  static String _sanitizeFileName(String fileName) {
    return fileName
        .replaceAll(RegExp(r'[^\w\s-]'), '') // Remove special characters
        .replaceAll(RegExp(r'\s+'), '-') // Replace spaces with hyphens
        .toLowerCase()
        .trim();
  }

  /// Get content type based on file extension
  static String _getContentType(String extension) {
    switch (extension.toLowerCase()) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'webp':
        return 'image/webp';
      case 'gif':
        return 'image/gif';
      default:
        return 'image/jpeg';
    }
  }

  /// Check if a URL is a Supabase Storage URL
  static bool isSupabaseStorageUrl(String url) {
    return url.contains(_bucketName) && url.contains('supabase');
  }

  /// Get file size from XFile
  static Future<int> getFileSize(XFile file) async {
    return await file.length();
  }

  /// Format file size for display
  static String formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }

  /// Upload local image file to Supabase Storage
  /// Used for migrating local images to cloud storage
  static Future<String> uploadLocalImageFile({
    required String itineraryId,
    required String destination,
    required String activityName,
    required String localFilePath,
  }) async {
    try {
      final user = _client.auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Check if file exists
      final file = File(localFilePath);
      if (!await file.exists()) {
        throw Exception('Local image file not found: $localFilePath');
      }

      // Validate file size (10MB limit)
      final fileSize = await file.length();
      if (fileSize > 10 * 1024 * 1024) {
        throw Exception('Image size must be less than 10MB');
      }

      // Get file extension
      final fileName = file.path.split('/').last.toLowerCase();
      final fileExtension = fileName.split('.').last;

      // Validate file type
      if (!fileName.endsWith('.jpg') &&
          !fileName.endsWith('.jpeg') &&
          !fileName.endsWith('.png') &&
          !fileName.endsWith('.webp') &&
          !fileName.endsWith('.gif')) {
        throw Exception('Only JPG, PNG, WebP, and GIF images are supported');
      }

      // Generate unique file path
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final sanitizedDestination = _sanitizeFileName(destination);
      final sanitizedActivity = _sanitizeFileName(activityName);
      final filePath =
          '$itineraryId/$sanitizedDestination/$sanitizedActivity-$timestamp.$fileExtension';

      // Read file bytes
      final fileBytes = await file.readAsBytes();

      // Upload to Supabase Storage
      final uploadResponse =
          await _client.storage.from(_bucketName).uploadBinary(
                filePath,
                fileBytes,
                fileOptions: FileOptions(
                  contentType: _getContentType(fileExtension),
                  upsert: false,
                ),
              );

      if (uploadResponse.isEmpty) {
        throw Exception('Failed to upload image');
      }

      // Get public URL
      final publicUrl =
          _client.storage.from(_bucketName).getPublicUrl(filePath);

      if (kDebugMode) {
        print(
            'Local image migrated successfully: $localFilePath -> $publicUrl');
      }

      return publicUrl;
    } catch (e) {
      if (kDebugMode) {
        print('Error uploading local image: $e');
      }
      rethrow;
    }
  }

  /// Migrate all local activity images to Supabase Storage
  /// Returns updated activity images map with cloud URLs
  static Future<Map<String, Map<String, String>>?> migrateLocalActivityImages({
    required String itineraryId,
    required Map<String, Map<String, String>>? localActivityImages,
  }) async {
    if (localActivityImages == null || localActivityImages.isEmpty) {
      return null;
    }

    try {
      final Map<String, Map<String, String>> migratedImages = {};

      for (final destinationEntry in localActivityImages.entries) {
        final destination = destinationEntry.key;
        final activities = destinationEntry.value;

        migratedImages[destination] = {};

        for (final activityEntry in activities.entries) {
          final activityName = activityEntry.key;
          final localImagePath = activityEntry.value;

          try {
            // Skip if already a cloud URL
            if (isSupabaseStorageUrl(localImagePath)) {
              migratedImages[destination]![activityName] = localImagePath;
              continue;
            }

            // Upload local image to cloud storage
            final cloudUrl = await uploadLocalImageFile(
              itineraryId: itineraryId,
              destination: destination,
              activityName: activityName,
              localFilePath: localImagePath,
            );

            migratedImages[destination]![activityName] = cloudUrl;

            if (kDebugMode) {
              print(
                  'Migrated image for $destination/$activityName: $localImagePath -> $cloudUrl');
            }
          } catch (e) {
            if (kDebugMode) {
              print(
                  'Failed to migrate image for $destination/$activityName: $e');
            }
            // Skip this image but continue with others
            continue;
          }
        }

        // Remove destination if no images were successfully migrated
        if (migratedImages[destination]!.isEmpty) {
          migratedImages.remove(destination);
        }
      }

      return migratedImages.isEmpty ? null : migratedImages;
    } catch (e) {
      if (kDebugMode) {
        print('Error migrating activity images: $e');
      }
      // Return original images if migration fails
      return localActivityImages;
    }
  }
}
