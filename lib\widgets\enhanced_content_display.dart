import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../services/ai_itinerary_parser.dart';
import '../widgets/enhanced_table_display.dart';
import '../widgets/enhanced_itinerary_display.dart';
import '../widgets/travel_enhanced_markdown.dart';

/// Main widget for displaying enhanced AI-generated content in the Plan tab
class EnhancedContentDisplay extends StatefulWidget {
  final ParsedContent content;
  final bool isCompact;
  final VoidCallback? onTap;

  const EnhancedContentDisplay({
    super.key,
    required this.content,
    this.isCompact = false,
    this.onTap,
  });

  @override
  State<EnhancedContentDisplay> createState() => _EnhancedContentDisplayState();
}

class _EnhancedContentDisplayState extends State<EnhancedContentDisplay>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: GestureDetector(
          onTap: widget.onTap,
          child: Container(
            margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
            decoration: BoxDecoration(
              color: const Color(0xFFF7F9FC),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildContentHeader(),
                _buildContentBody(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildContentHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          colors: [Color(0xFF0D76FF), Color(0xFF1E88E5)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          _buildContentTypeIcon(),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.content.title,
                  style: GoogleFonts.instrumentSans(
                    fontSize: widget.isCompact ? 14 : 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _getContentTypeDescription(),
                  style: GoogleFonts.instrumentSans(
                    fontSize: widget.isCompact ? 11 : 12,
                    color: Colors.white.withOpacity(0.8),
                  ),
                ),
              ],
            ),
          ),
          if (widget.onTap != null) ...[
            const Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Colors.white,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildContentTypeIcon() {
    IconData iconData;
    switch (widget.content.type) {
      case ContentType.itinerary:
        iconData = Icons.map;
        break;
      case ContentType.table:
        iconData = Icons.table_chart;
        break;
      case ContentType.list:
        iconData = Icons.list;
        break;
      case ContentType.mixed:
        iconData = Icons.dashboard;
        break;
      case ContentType.plain:
        iconData = Icons.article;
        break;
    }

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.2),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(
        iconData,
        size: widget.isCompact ? 16 : 20,
        color: Colors.white,
      ),
    );
  }

  String _getContentTypeDescription() {
    switch (widget.content.type) {
      case ContentType.itinerary:
        return 'Travel Itinerary';
      case ContentType.table:
        return 'Data Table';
      case ContentType.list:
        return 'Information List';
      case ContentType.mixed:
        return 'Mixed Content';
      case ContentType.plain:
        return 'Travel Information';
    }
  }

  Widget _buildContentBody() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: _buildContentByType(),
    );
  }

  Widget _buildContentByType() {
    switch (widget.content.type) {
      case ContentType.itinerary:
        if (widget.content.itinerary != null) {
          return EnhancedItineraryDisplay(
            itinerary: widget.content.itinerary!,
            isCompact: widget.isCompact,
          );
        }
        break;
      
      case ContentType.table:
        if (widget.content.tables?.isNotEmpty == true) {
          return MultiTableDisplay(
            tables: widget.content.tables!,
            isCompact: widget.isCompact,
          );
        }
        break;
      
      case ContentType.list:
        if (widget.content.lists?.isNotEmpty == true) {
          return _buildEnhancedLists();
        }
        break;
      
      case ContentType.mixed:
        return _buildMixedContent();
      
      case ContentType.plain:
        return _buildPlainContent();
    }
    
    // Fallback to markdown
    return TravelEnhancedMarkdown(
      data: widget.content.rawContent,
      isUserMessage: false,
      isCompact: widget.isCompact,
    );
  }

  Widget _buildEnhancedLists() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: widget.content.lists!.map((listContent) {
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: const Color(0xFFE2E8F0),
              width: 1,
            ),
          ),
          child: TravelEnhancedMarkdown(
            data: listContent,
            isUserMessage: false,
            isCompact: widget.isCompact,
          ),
        );
      }).toList(),
    );
  }

  Widget _buildMixedContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Display tables if available
        if (widget.content.tables?.isNotEmpty == true) ...[
          MultiTableDisplay(
            tables: widget.content.tables!,
            isCompact: widget.isCompact,
          ),
          const SizedBox(height: 16),
        ],
        
        // Display itinerary if available
        if (widget.content.itinerary != null) ...[
          EnhancedItineraryDisplay(
            itinerary: widget.content.itinerary!,
            isCompact: widget.isCompact,
          ),
          const SizedBox(height: 16),
        ],
        
        // Display lists if available
        if (widget.content.lists?.isNotEmpty == true) ...[
          _buildEnhancedLists(),
          const SizedBox(height: 16),
        ],
        
        // Display remaining content as markdown
        TravelEnhancedMarkdown(
          data: widget.content.rawContent,
          isUserMessage: false,
          isCompact: widget.isCompact,
        ),
      ],
    );
  }

  Widget _buildPlainContent() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFFE2E8F0),
          width: 1,
        ),
      ),
      child: TravelEnhancedMarkdown(
        data: widget.content.rawContent,
        isUserMessage: false,
        isCompact: widget.isCompact,
      ),
    );
  }
}

/// Compact version for use in smaller spaces
class CompactContentDisplay extends StatelessWidget {
  final ParsedContent content;
  final VoidCallback? onTap;

  const CompactContentDisplay({
    super.key,
    required this.content,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return EnhancedContentDisplay(
      content: content,
      isCompact: true,
      onTap: onTap,
    );
  }
}
