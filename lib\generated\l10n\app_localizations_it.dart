/// Generated file. Do not edit.
///
/// This file contains the localized strings for the TripWiseGo app.
/// To add a new localized string, add it to the appropriate .arb file
/// in the lib/l10n directory and run `flutter gen-l10n`.


import 'app_localizations.dart';

/// The translations for Italian (`it`).
class AppLocalizationsIt extends AppLocalizations {
  AppLocalizationsIt([String locale = 'it']) : super(locale);

  @override
  String get appTitle => 'TripWiseGo';

  @override
  String get welcome => 'Benvenuto';

  @override
  String get guestUser => 'Utente ospite';

  @override
  String get readyForAdventure => 'Pronto per la tua prossima avventura';

  @override
  String get exploringAsGuest => 'Esplorando il mondo come ospite';

  @override
  String get editProfile => 'Modifica profilo';

  @override
  String get saveChanges => 'Salva modifiche';

  @override
  String get cancel => 'Annulla';

  @override
  String get username => 'Nome utente';

  @override
  String get email => 'Email';

  @override
  String get profileUpdatedSuccessfully => 'Profilo aggiornato con successo!';

  @override
  String failedToUpdateProfile(String error) {
    return 'Aggiornamento profilo fallito: $error';
  }

  @override
  String get profilePictureUpdatedSuccessfully => 'Foto profilo aggiornata con successo!';

  @override
  String failedToUploadImage(String error) {
    return 'Caricamento immagine fallito: $error';
  }

  @override
  String get profileEditingNotAvailableForGuests => 'La modifica del profilo non è disponibile per gli utenti ospiti';

  @override
  String get profilePictureEditingNotAvailableForGuests => 'La modifica della foto profilo non è disponibile per gli utenti ospiti';

  @override
  String get usernameCannotBeEmpty => 'Il nome utente non può essere vuoto';

  @override
  String get usernameMustBeBetween2And30Characters => 'Il nome utente deve essere tra 2 e 30 caratteri';

  @override
  String get plan => 'Piano';

  @override
  String get termsOfService => 'Termini di servizio';

  @override
  String get language => 'Lingua';

  @override
  String get privacyPolicy => 'Politica sulla privacy';

  @override
  String get support => 'Supporto';

  @override
  String get helpCenter => 'Centro assistenza';

  @override
  String get contactUs => 'Contattaci';

  @override
  String get helpSupport => 'Help & Support';

  @override
  String get howCanWeHelpYou => 'How can we help you?';

  @override
  String get helpSupportGreeting => 'Hi! I\'m here to help you with TripwiseGO. You can ask me about app features, troubleshooting, or report any issues you\'re experiencing.';

  @override
  String get helpSupportWelcome => 'Welcome to TripwiseGO Support! Here are some things I can help you with:';

  @override
  String get helpSupportFeatures => '• App features and how to use them\n• Navigation and account help\n• Troubleshooting common issues\n• Reporting bugs or problems';

  @override
  String get helpSupportAskQuestion => 'Feel free to ask me anything or describe any issues you\'re having!';

  @override
  String get reportIssue => 'Report Issue';

  @override
  String get reportBug => 'Report Bug';

  @override
  String get reportProblem => 'Report Problem';

  @override
  String get issueCategory => 'Issue Category';

  @override
  String get bugReport => 'Bug Report';

  @override
  String get featureRequest => 'Feature Request';

  @override
  String get generalFeedback => 'General Feedback';

  @override
  String get accountIssue => 'Account Issue';

  @override
  String get technicalProblem => 'Technical Problem';

  @override
  String get yourName => 'Your Name';

  @override
  String get yourEmail => 'Your Email';

  @override
  String get issueDescription => 'Issue Description';

  @override
  String get describeIssueDetail => 'Please describe the issue in detail';

  @override
  String get optionalScreenshot => 'Screenshot (Optional)';

  @override
  String get submitReport => 'Submit Report';

  @override
  String get reportSubmitted => 'Report Submitted';

  @override
  String get reportSubmittedSuccess => 'Thank you! Your report has been submitted successfully. We\'ll look into it and get back to you if needed.';

  @override
  String get reportSubmissionFailed => 'Failed to submit report. Please try again later.';

  @override
  String get nameRequired => 'Name is required';

  @override
  String get emailRequired => 'Email is required';

  @override
  String get validEmailRequired => 'Please enter a valid email address';

  @override
  String get descriptionRequired => 'Issue description is required';

  @override
  String get selectCategory => 'Please select a category';

  @override
  String get subscription => 'Subscription';

  @override
  String get subscriptionActive => 'Subscription Active!';

  @override
  String get welcomeToPremium => 'Welcome to TripwiseGO Premium! Enjoy unlimited access to all features.';

  @override
  String get currentPlan => 'Current Plan';

  @override
  String get trial => 'Trial';

  @override
  String get active => 'Active';

  @override
  String trialEndsIn(int days) {
    return 'Trial ends in $days days';
  }

  @override
  String renewsIn(int days) {
    return 'Renews in $days days';
  }

  @override
  String get yourBenefits => 'Your Benefits';

  @override
  String get startPlanningTrip => 'Start Planning Your Trip';

  @override
  String get manageSubscription => 'Manage Subscription';

  @override
  String get basic => 'Basic';

  @override
  String get premium => 'Premium';

  @override
  String get basicPlaceRecommendations => 'Basic Place Recommendations';

  @override
  String get locationRecommendationSwiping => 'Location Recommendation Swiping (20 swipes/day)';

  @override
  String get aiPoweredTravelPlanner => 'AI-Powered Travel Planner';

  @override
  String get unlimitedQuizzes => 'Unlimited Quizzes & Fun Facts';

  @override
  String get noAds => 'No-Ads';

  @override
  String get oneMonth => '1 Month';

  @override
  String get threeMonths => '3 Months';

  @override
  String get fiveMonths => '5 Months';

  @override
  String dayFreeTrial(int days) {
    return '$days-day free trial';
  }

  @override
  String get billedMonthly => 'Billed monthly';

  @override
  String get billedTwiceAnnually => 'Billed twice annually';

  @override
  String get billedAnnually => 'Billed annually';

  @override
  String get save45Percent => 'SAVE 45%';

  @override
  String get continueButton => 'Continue';

  @override
  String get termsAndConditions => 'Terms and Conditions';

  @override
  String get failedToSubscribe => 'Failed to subscribe. Please try again.';

  @override
  String get signOut => 'Disconnetti';

  @override
  String get selectLanguage => 'Seleziona lingua';

  @override
  String get chooseYourPreferredLanguage => 'Scegli la tua lingua preferita';

  @override
  String get languageUpdatedSuccessfully => 'Lingua aggiornata con successo!';

  @override
  String get home => 'Home';

  @override
  String get match => 'Corrispondenza';

  @override
  String get chat => 'Chat';

  @override
  String get profile => 'Profilo';

  @override
  String get loading => 'Caricamento...';

  @override
  String get error => 'Errore';

  @override
  String get retry => 'Riprova';

  @override
  String get ok => 'OK';

  @override
  String get yes => 'Sì';

  @override
  String get no => 'No';

  @override
  String get save => 'Salva';

  @override
  String get delete => 'Elimina';

  @override
  String get edit => 'Modifica';

  @override
  String get add => 'Aggiungi';

  @override
  String get remove => 'Rimuovi';

  @override
  String get close => 'Chiudi';

  @override
  String get back => 'Indietro';

  @override
  String get next => 'Avanti';

  @override
  String get previous => 'Precedente';

  @override
  String get done => 'Fatto';

  @override
  String get search => 'Cerca';

  @override
  String get noResultsFound => 'Nessun risultato trovato';

  @override
  String get tryAgain => 'Riprova';

  @override
  String get somethingWentWrong => 'Qualcosa è andato storto';

  @override
  String get networkError => 'Errore di rete. Controlla la tua connessione.';

  @override
  String get serverError => 'Errore del server. Riprova più tardi.';

  @override
  String get invalidInput => 'Input non valido';

  @override
  String get required => 'Obbligatorio';

  @override
  String get optional => 'Opzionale';

  @override
  String get itinerary => 'Itinerary';

  @override
  String get yourItineraries => 'Your Itineraries';

  @override
  String get startPlanningYourTrip => 'Start Planning Your Trip';

  @override
  String get tripPlanningFeaturesWillAppearHere => 'Your trip planning features will appear here. The persistent authentication is now working!';

  @override
  String get forYou => 'For You';

  @override
  String get liked => 'Liked';

  @override
  String get collab => 'Collab';

  @override
  String get collaborativeItinerary => 'Collaborative';

  @override
  String get endSession => 'End Session';

  @override
  String get logout => 'Logout';

  @override
  String logoutFailed(String error) {
    return 'Logout failed: $error';
  }

  @override
  String get noItineraryFound => 'Nessun Itinerario Trovato';

  @override
  String get askAiToCreateTravelPlan => 'Chiedi alla nostra IA di creare un piano di viaggio per te!';

  @override
  String get saturday => 'Sabato';

  @override
  String get tuesday => 'Martedì';

  @override
  String dayNumber(int number) {
    return 'Giorno $number';
  }

  @override
  String get itineraryOverview => 'Panoramica dell\'Itinerario';

  @override
  String daysAndNights(int days, int nights) {
    return '${days}g ${nights}n';
  }

  @override
  String get hiImWanderlyAi => 'Ciao, sono Wanderly AI 🌏';

  @override
  String get yourTravelAiAssistant => 'Il tuo assistente di viaggio IA, come posso aiutarti oggi?';

  @override
  String get useThisBubbleChat => 'Usa questa chat a bolle';

  @override
  String get aiAssistant => 'Assistente IA';

  @override
  String get chatHistory => 'Cronologia Chat';

  @override
  String get newChat => 'Nuova Chat';

  @override
  String get addImage => 'Aggiungi Immagine';

  @override
  String get camera => 'Fotocamera';

  @override
  String get gallery => 'Galleria';

  @override
  String get microphonePermissionRequired => 'È richiesta l\'autorizzazione del microfono per l\'input vocale';

  @override
  String get speechRecognitionNotAvailable => 'Il riconoscimento vocale non è disponibile su questo dispositivo';

  @override
  String get listening => 'In ascolto...';

  @override
  String get deleteChat => 'Elimina Chat';

  @override
  String get deleteChatConfirmation => 'Sei sicuro di voler eliminare questa chat? Questa azione non può essere annullata.';

  @override
  String get chatDeletedSuccessfully => 'Chat eliminata con successo';

  @override
  String get pleaseEnterSearchQuery => 'Inserisci una query di ricerca';

  @override
  String get dailySearchLimitReached => 'Limite di ricerca giornaliero raggiunto. Puoi eseguire 5 ricerche al giorno.';

  @override
  String get searchingTheWeb => 'Ricerca nel Web...';

  @override
  String get webSearchModeActive => 'Modalità Ricerca Web Attiva';

  @override
  String get pleaseWaitWhileSearching => 'Attendi mentre cerco informazioni';

  @override
  String get yourNextMessageWillSearch => 'Il tuo prossimo messaggio cercherà nel web';

  @override
  String get disableWebSearch => 'Disabilita Ricerca Web';

  @override
  String get enableWebSearch => 'Abilita Ricerca Web';

  @override
  String get switchBackToAiChatMode => 'Torna alla modalità chat IA';

  @override
  String get searchWebForCurrentInfo => 'Cerca informazioni attuali nel web';

  @override
  String get pickImageFromGallery => 'Scegli Immagine dalla Galleria';

  @override
  String get uploadImageForAiAnalysis => 'Carica immagine per analisi IA';

  @override
  String get yourMessage => 'Il Tuo Messaggio';

  @override
  String get wanderlyAi => 'Wanderly AI';

  @override
  String get webSearch => 'Ricerca Web:';

  @override
  String get like => 'Mi Piace';

  @override
  String get dislike => 'Non Mi Piace';

  @override
  String get copy => 'Copia';

  @override
  String get regenerate => 'Rigenera';

  @override
  String get failedToSubmitFeedback => 'Invio feedback fallito. Riprova.';

  @override
  String get thankYouForFeedback => 'Grazie per il tuo feedback! 🙏';

  @override
  String get feedbackReceivedThanks => 'Feedback ricevuto. Grazie per aiutarci a migliorare! 🚀';

  @override
  String get responseCopiedToClipboard => 'Risposta copiata negli appunti';

  @override
  String get wanderlyAiIsTyping => 'Wanderly AI sta scrivendo';

  @override
  String get stopGeneration => 'Ferma Generazione';

  @override
  String get youHaveChatsLeft => 'Hai 10 chat rimanenti';

  @override
  String get enterSearchQuery => 'Inserisci la tua query di ricerca...';

  @override
  String get askMeAnythingOrLongPress => 'Chiedimi qualsiasi cosa o tieni premuto per parlare...';

  @override
  String failedToPickImage(String error) {
    return 'Selezione immagine fallita: $error';
  }

  @override
  String get failedToAnalyzeImage => 'Analisi immagine fallita. Riprova.';

  @override
  String get responseGenerationStopped => 'Generazione risposta interrotta.';

  @override
  String get unknownDestination => 'Destinazione Sconosciuta';

  @override
  String get congratulations => 'Congratulations';

  @override
  String get itsAMatch => 'It\'s a match';

  @override
  String get travelVibesAligned => 'Travel vibes aligned pack\nyour bags, you\'ve got a match!';

  @override
  String get matchedPreferences => 'Matched Preferences:';

  @override
  String get addToItinerary => 'Add to Itinerary';

  @override
  String get keepSwiping => 'Keep swiping';

  @override
  String get versionInfo => 'Version Info';

  @override
  String get appVersion => 'App Version';

  @override
  String get buildNumber => 'Build Number';

  @override
  String get packageName => 'Package Name';

  @override
  String get appName => 'App Name';

  @override
  String get buildSignature => 'Build Signature';

  @override
  String get installerStore => 'Installer Store';

  @override
  String get deviceInfo => 'Device Information';

  @override
  String get operatingSystem => 'Operating System';

  @override
  String get flutterVersion => 'Flutter Version';

  @override
  String get dartVersion => 'Dart Version';

  @override
  String get leaveAReview => 'Leave a Review';

  @override
  String get rateOurApp => 'Rate Our App';

  @override
  String get enjoyingTripWiseGo => 'Enjoying TripWiseGo?';

  @override
  String get helpUsImproveByLeavingReview => 'Help us improve by leaving a review on the app store. Your feedback means a lot to us!';

  @override
  String get rateNow => 'Rate Now';

  @override
  String get maybeLater => 'Maybe Later';

  @override
  String get reviewFeatureNotAvailable => 'Review feature is only available in production builds';

  @override
  String get unableToOpenStore => 'Unable to open app store. Please try again later.';

  @override
  String get thankYouForReview => 'Thank you for taking the time to review our app!';
}
