import 'package:flutter/foundation.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'subscription_service.dart';
import '../config/admob_config.dart';

/// Service for managing Google AdMob interstitial ads
class AdMobService {
  static bool _isInitialized = false;
  static InterstitialAd? _interstitialAd;
  static bool _isAdLoading = false;
  static bool _isAdReady = false;

  /// Initialize the AdMob service
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialize Mobile Ads SDK
      await MobileAds.instance.initialize();

      // Set request configuration for testing
      if (kDebugMode && AdMobConfig.testDeviceIds.isNotEmpty) {
        final configuration = RequestConfiguration(
          testDeviceIds: AdMobConfig.testDeviceIds,
        );
        MobileAds.instance.updateRequestConfiguration(configuration);
      }

      _isInitialized = true;

      // Preload the first interstitial ad
      await _loadInterstitialAd();

      if (kDebugMode) {
        print('AdMob Service: Initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('AdMob Service: Initialization error - $e');
      }
    }
  }

  /// Get the appropriate ad unit ID based on platform and build mode
  static String _getInterstitialAdUnitId() {
    return AdMobConfig.interstitialAdUnitId;
  }

  /// Load an interstitial ad
  static Future<void> _loadInterstitialAd() async {
    if (_isAdLoading || _isAdReady) return;

    _isAdLoading = true;

    try {
      await InterstitialAd.load(
        adUnitId: _getInterstitialAdUnitId(),
        request: const AdRequest(),
        adLoadCallback: InterstitialAdLoadCallback(
          onAdLoaded: (InterstitialAd ad) {
            _interstitialAd = ad;
            _isAdReady = true;
            _isAdLoading = false;

            if (kDebugMode) {
              print('AdMob Service: Interstitial ad loaded successfully');
            }

            // Set up ad event callbacks
            _setupAdCallbacks();
          },
          onAdFailedToLoad: (LoadAdError error) {
            _isAdLoading = false;
            _isAdReady = false;

            if (kDebugMode) {
              print('AdMob Service: Failed to load interstitial ad - $error');
            }
          },
        ),
      );
    } catch (e) {
      _isAdLoading = false;
      _isAdReady = false;

      if (kDebugMode) {
        print('AdMob Service: Error loading interstitial ad - $e');
      }
    }
  }

  /// Set up ad event callbacks
  static void _setupAdCallbacks() {
    _interstitialAd?.fullScreenContentCallback = FullScreenContentCallback(
      onAdShowedFullScreenContent: (InterstitialAd ad) {
        if (kDebugMode) {
          print('AdMob Service: Interstitial ad showed full screen content');
        }
      },
      onAdDismissedFullScreenContent: (InterstitialAd ad) {
        if (kDebugMode) {
          print('AdMob Service: Interstitial ad dismissed');
        }

        // Dispose the ad and load a new one
        ad.dispose();
        _interstitialAd = null;
        _isAdReady = false;

        // Preload the next ad
        _loadInterstitialAd();
      },
      onAdFailedToShowFullScreenContent: (InterstitialAd ad, AdError error) {
        if (kDebugMode) {
          print('AdMob Service: Failed to show interstitial ad - $error');
        }

        // Dispose the failed ad and load a new one
        ad.dispose();
        _interstitialAd = null;
        _isAdReady = false;

        // Try to load a new ad
        _loadInterstitialAd();
      },
      onAdClicked: (InterstitialAd ad) {
        if (kDebugMode) {
          print('AdMob Service: Interstitial ad clicked');
        }
      },
    );
  }

  /// Check if user should see ads (free users only)
  static Future<bool> shouldShowAds() async {
    try {
      // Only show ads to free users (non-subscribers)
      final hasActiveSubscription =
          await SubscriptionService.hasActiveSubscription();
      return !hasActiveSubscription;
    } catch (e) {
      if (kDebugMode) {
        print('AdMob Service: Error checking subscription status - $e');
      }
      // Default to showing ads if we can't determine subscription status
      return true;
    }
  }

  /// Show interstitial ad if conditions are met
  static Future<bool> showInterstitialAd() async {
    try {
      // Check if user should see ads
      final shouldShow = await shouldShowAds();
      if (!shouldShow) {
        if (kDebugMode) {
          print('AdMob Service: Skipping ad for subscribed user');
        }
        return false;
      }

      // Check if ad is ready
      if (!_isAdReady || _interstitialAd == null) {
        if (kDebugMode) {
          print('AdMob Service: No ad ready to show');
        }

        // Try to load an ad for next time
        if (!_isAdLoading) {
          _loadInterstitialAd();
        }

        return false;
      }

      // Show the ad
      await _interstitialAd!.show();

      if (kDebugMode) {
        print('AdMob Service: Interstitial ad shown successfully');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('AdMob Service: Error showing interstitial ad - $e');
      }
      return false;
    }
  }

  /// Check if an ad is ready to be shown
  static bool isAdReady() {
    return _isAdReady && _interstitialAd != null;
  }

  /// Preload an ad (useful for ensuring ads are ready when needed)
  static Future<void> preloadAd() async {
    if (!_isAdReady && !_isAdLoading) {
      await _loadInterstitialAd();
    }
  }

  /// Dispose of resources
  static void dispose() {
    _interstitialAd?.dispose();
    _interstitialAd = null;
    _isAdReady = false;
    _isAdLoading = false;
    _isInitialized = false;
  }

  /// Get ad loading status for UI feedback
  static bool get isLoading => _isAdLoading;

  /// Get ad ready status for UI feedback
  static bool get isReady => _isAdReady;
}
