class Itinerary {
  final String id;
  final String title;
  final String startDate;
  final String endDate;
  final List<String> destinations;
  final bool hasPhoto;
  final String? imagePath;
  final Map<String, List<String>>
      dailyActivities; // destination -> activities (legacy)
  final Map<int, Map<String, List<String>>>?
      daySpecificActivities; // day -> destination -> activities
  final Map<String, Map<String, Map<String, String>>>?
      activityTimes; // destination -> activity -> {startTime, endTime, day}
  final Map<String, Map<String, String>>?
      activityImages; // destination -> activity -> imagePath
  final String accommodation;
  final String additionalNotes;
  final DateTime createdAt;

  Itinerary({
    required this.id,
    required this.title,
    required this.startDate,
    required this.endDate,
    required this.destinations,
    required this.hasPhoto,
    this.imagePath,
    required this.dailyActivities,
    this.daySpecificActivities,
    this.activityTimes,
    this.activityImages,
    required this.accommodation,
    required this.additionalNotes,
    required this.createdAt,
  });

  // Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'startDate': startDate,
      'endDate': endDate,
      'destinations': destinations,
      'hasPhoto': hasPhoto,
      'imagePath': imagePath,
      'dailyActivities': dailyActivities,
      'daySpecificActivities': daySpecificActivities?.map(
        (day, activities) => MapEntry(
          day.toString(),
          activities,
        ),
      ),
      'activityTimes': activityTimes,
      'activityImages': activityImages,
      'accommodation': accommodation,
      'additionalNotes': additionalNotes,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  // Create from JSON
  factory Itinerary.fromJson(Map<String, dynamic> json) {
    // Parse day-specific activities if available
    Map<int, Map<String, List<String>>>? daySpecificActivities;
    if (json['daySpecificActivities'] != null) {
      final dayActivitiesJson =
          json['daySpecificActivities'] as Map<String, dynamic>;
      daySpecificActivities = dayActivitiesJson.map(
        (dayStr, activities) => MapEntry(
          int.parse(dayStr),
          Map<String, List<String>>.from(
            (activities as Map<String, dynamic>).map(
              (destination, activityList) => MapEntry(
                destination,
                List<String>.from(activityList as List),
              ),
            ),
          ),
        ),
      );
    }

    // Parse activity times if available
    Map<String, Map<String, Map<String, String>>>? activityTimes;
    if (json['activityTimes'] != null) {
      final activityTimesJson = json['activityTimes'] as Map<String, dynamic>;
      activityTimes = activityTimesJson.map(
        (destination, activities) => MapEntry(
          destination,
          Map<String, Map<String, String>>.from(
            (activities as Map<String, dynamic>).map(
              (activity, timeData) => MapEntry(
                activity,
                Map<String, String>.from(timeData as Map<String, dynamic>),
              ),
            ),
          ),
        ),
      );
    }

    // Parse activity images if available
    Map<String, Map<String, String>>? activityImages;
    if (json['activityImages'] != null) {
      final activityImagesJson = json['activityImages'] as Map<String, dynamic>;
      activityImages = activityImagesJson.map(
        (destination, activities) => MapEntry(
          destination,
          Map<String, String>.from(activities as Map<String, dynamic>),
        ),
      );
    }

    return Itinerary(
      id: json['id'] as String,
      title: json['title'] as String,
      startDate: json['startDate'] as String,
      endDate: json['endDate'] as String,
      destinations: List<String>.from(json['destinations'] as List),
      hasPhoto: json['hasPhoto'] as bool,
      imagePath: json['imagePath'] as String?,
      dailyActivities: Map<String, List<String>>.from(
        (json['dailyActivities'] as Map<String, dynamic>? ?? {}).map(
          (key, value) => MapEntry(key, List<String>.from(value as List)),
        ),
      ),
      daySpecificActivities: daySpecificActivities,
      activityTimes: activityTimes,
      activityImages: activityImages,
      accommodation: json['accommodation'] as String? ?? '',
      additionalNotes: json['additionalNotes'] as String? ?? '',
      createdAt: DateTime.parse(json['createdAt'] as String),
    );
  }

  // Helper method to get formatted date range
  String get dateRange {
    return '$startDate - $endDate';
  }

  // Helper method to get destination count
  int get destinationCount {
    return destinations.length;
  }

  // Helper method to get primary destination for display
  String get primaryDestination {
    return destinations.isNotEmpty ? destinations.first : 'Unknown';
  }

  // Helper method to get activities for a specific day
  Map<String, List<String>> getActivitiesForDay(int day) {
    if (daySpecificActivities != null &&
        daySpecificActivities!.containsKey(day)) {
      return daySpecificActivities![day]!;
    }
    // Fallback to legacy format for backward compatibility
    return dailyActivities;
  }

  // Helper method to check if this itinerary uses day-specific activities
  bool get usesDaySpecificActivities {
    return daySpecificActivities != null && daySpecificActivities!.isNotEmpty;
  }

  // Helper method to get total number of days in the trip
  int get totalDays {
    try {
      final startParts = startDate.split('/');
      final endParts = endDate.split('/');

      if (startParts.length == 3 && endParts.length == 3) {
        final startDateTime = DateTime(
          int.parse(startParts[2]), // year
          int.parse(startParts[1]), // month
          int.parse(startParts[0]), // day
        );
        final endDateTime = DateTime(
          int.parse(endParts[2]), // year
          int.parse(endParts[1]), // month
          int.parse(endParts[0]), // day
        );

        return endDateTime.difference(startDateTime).inDays + 1;
      }
    } catch (e) {
      // If parsing fails, return 1
    }
    return 1;
  }

  // Helper method to get all activities across all days
  List<String> getAllActivities() {
    final allActivities = <String>[];

    if (usesDaySpecificActivities) {
      for (final dayActivities in daySpecificActivities!.values) {
        for (final activities in dayActivities.values) {
          allActivities.addAll(activities);
        }
      }
    } else {
      for (final activities in dailyActivities.values) {
        allActivities.addAll(activities);
      }
    }

    return allActivities.toSet().toList(); // Remove duplicates
  }
}
