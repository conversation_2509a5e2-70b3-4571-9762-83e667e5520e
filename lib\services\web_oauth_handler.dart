import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../config/supabase_config.dart';
import '../utils/oauth_debug_helper.dart';

// Conditional import for web-specific functionality
import 'web_oauth_handler_stub.dart'
    if (dart.library.html) 'web_oauth_handler_web.dart' as web_impl;

class WebOAuthHandler {
  static final WebOAuthHandler _instance = WebOAuthHandler._internal();
  factory WebOAuthHandler() => _instance;
  WebOAuthHandler._internal();

  final SupabaseClient _supabase = SupabaseConfig.client;
  Timer? _pollTimer;

  // Initialize the web OAuth handler
  Future<void> initialize() async {
    if (!kIsWeb) return;

    try {
      if (kDebugMode) {
        print('Web OAuth Handler: Initializing...');
      }

      // Check for OAuth callback in URL parameters immediately
      await _checkForUrlCallback();

      // Check for OAuth callback in session storage
      await _checkForOAuthCallback();

      // Start polling for OAuth callbacks
      _startPolling();

      if (kDebugMode) {
        print('Web OAuth Handler: Initialization complete');
      }
    } catch (error) {
      if (kDebugMode) {
        print('Web OAuth Handler: Initialization error: $error');
      }
    }
  }

  // Start polling for OAuth callbacks
  void _startPolling() {
    _pollTimer?.cancel();
    _pollTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _checkForOAuthCallback();
    });
  }

  // Check for OAuth callback in URL parameters
  Future<void> _checkForUrlCallback() async {
    if (!kIsWeb) return;

    try {
      // Check if we're on web and can access the URL
      final currentUrl = web_impl.window.location.href;
      if (currentUrl.contains('#') || currentUrl.contains('?')) {
        final uri = Uri.parse(currentUrl);

        // Check for OAuth callback parameters
        Map<String, String> params = {};

        // Check fragment (after #) for OAuth parameters
        if (uri.fragment.isNotEmpty) {
          final fragmentParams = Uri.splitQueryString(uri.fragment);
          params.addAll(fragmentParams);
        }

        // Check query parameters (after ?)
        params.addAll(uri.queryParameters);

        // Look for OAuth callback indicators
        if (params.containsKey('access_token') ||
            params.containsKey('code') ||
            params.containsKey('error')) {
          if (kDebugMode) {
            print('Web OAuth Handler: Found OAuth callback in URL');
            print('Web OAuth Handler: URL: $currentUrl');
          }

          // Process the OAuth callback
          await _processOAuthCallback(params);

          // Clean up the URL to remove OAuth parameters
          _cleanupUrl();

          // Log final auth state for debugging
          if (kDebugMode) {
            OAuthDebugHelper.logAuthState();
          }
        }
      }
    } catch (error) {
      if (kDebugMode) {
        print('Web OAuth Handler: Error checking URL callback: $error');
      }
    }
  }

  // Check for OAuth callback in session storage
  Future<void> _checkForOAuthCallback() async {
    try {
      final callbackData = web_impl.getOAuthCallbackData();

      if (callbackData != null) {
        if (kDebugMode) {
          print('Web OAuth Handler: Found OAuth callback data');
        }

        // Parse the callback data
        final Map<String, dynamic> data = jsonDecode(callbackData);

        // Check if this is a recent callback (within last 5 minutes)
        final timestamp = data['timestamp'] as int?;
        if (timestamp != null) {
          final age = DateTime.now().millisecondsSinceEpoch - timestamp;
          if (age > 300000) {
            // 5 minutes
            if (kDebugMode) {
              print(
                  'Web OAuth Handler: OAuth callback data is too old, ignoring');
            }
            web_impl.removeOAuthCallbackData();
            return;
          }
        }

        // Remove the callback data to prevent reprocessing
        web_impl.removeOAuthCallbackData();

        // Process the callback
        await _processOAuthCallback(data);
      }
    } catch (error) {
      if (kDebugMode) {
        print('Web OAuth Handler: Error checking for OAuth callback: $error');
      }
    }
  }

  // Clean up URL to remove OAuth parameters
  void _cleanupUrl() {
    if (!kIsWeb) return;

    try {
      final currentUrl = web_impl.window.location.href;
      final uri = Uri.parse(currentUrl);

      // Create a clean URL without OAuth parameters
      final cleanUrl =
          '${uri.scheme}://${uri.host}${uri.port != 80 && uri.port != 443 ? ':${uri.port}' : ''}${uri.path}';

      // Update the URL without reloading the page
      web_impl.window.history.replaceState(null, '', cleanUrl);

      if (kDebugMode) {
        print('Web OAuth Handler: Cleaned up URL: $cleanUrl');
      }
    } catch (error) {
      if (kDebugMode) {
        print('Web OAuth Handler: Error cleaning up URL: $error');
      }
    }
  }

  // Process OAuth callback data
  Future<void> _processOAuthCallback(Map<String, dynamic> data) async {
    try {
      if (kDebugMode) {
        print('Web OAuth Handler: Processing OAuth callback...');
        print('Web OAuth Handler: Callback data: $data');
      }

      // Check for error
      if (data['error'] != null) {
        final error = data['error'];
        final errorDescription = data['error_description'] ?? 'Unknown error';
        if (kDebugMode) {
          print('Web OAuth Handler: OAuth error: $error - $errorDescription');
        }
        return;
      }

      // Check for access token (implicit flow)
      final accessToken = data['access_token'] as String?;
      if (accessToken != null) {
        if (kDebugMode) {
          print('Web OAuth Handler: Received access token (implicit flow)');
        }

        // For implicit flow, the session should already be established
        // Just trigger auth state refresh
        await _refreshAuthState();
        return;
      }

      // Check for authorization code (PKCE flow)
      final code = data['code'] as String?;
      if (code != null) {
        if (kDebugMode) {
          print(
              'Web OAuth Handler: Received authorization code: ${code.substring(0, 10)}...');
        }

        // Exchange code for session
        await _exchangeCodeForSession(code);
      } else {
        if (kDebugMode) {
          print(
              'Web OAuth Handler: No authorization code or access token found in callback');
        }
      }
    } catch (error) {
      if (kDebugMode) {
        print('Web OAuth Handler: Error processing OAuth callback: $error');
      }
    }
  }

  // Exchange authorization code for session
  Future<void> _exchangeCodeForSession(String code) async {
    try {
      if (kDebugMode) {
        print('Web OAuth Handler: Exchanging code for session...');
      }

      // Use Supabase's session exchange method
      final AuthSessionUrlResponse response =
          await _supabase.auth.exchangeCodeForSession(code);

      // Session was established successfully
      final session = response.session;
      final user = session.user;

      if (kDebugMode) {
        print('Web OAuth Handler: Session established successfully');
        print('Web OAuth Handler: User ID: ${user.id}');
        print('Web OAuth Handler: User email: ${user.email}');
      }

      // Stop polling since we've successfully processed the callback
      _stopPolling();

      // Trigger auth state update
      await _refreshAuthState();
    } catch (error) {
      if (kDebugMode) {
        print('Web OAuth Handler: Error exchanging code for session: $error');
      }
    }
  }

  // Refresh authentication state
  Future<void> _refreshAuthState() async {
    try {
      if (kDebugMode) {
        print('Web OAuth Handler: Refreshing auth state...');
      }

      // Get the current session
      final session = _supabase.auth.currentSession;
      if (session != null) {
        if (kDebugMode) {
          print('Web OAuth Handler: Valid session found');
          print('Web OAuth Handler: User ID: ${session.user.id}');
          print('Web OAuth Handler: User email: ${session.user.email}');
        }

        // The AuthStateManager should automatically pick up the session change
        // through the Supabase auth state change listener
        // No need to manually trigger initialization
      } else {
        if (kDebugMode) {
          print('Web OAuth Handler: No valid session found');
        }
      }
    } catch (error) {
      if (kDebugMode) {
        print('Web OAuth Handler: Error refreshing auth state: $error');
      }
    }
  }

  // Stop polling for OAuth callbacks
  void _stopPolling() {
    _pollTimer?.cancel();
    _pollTimer = null;
  }

  // Check if there's a pending OAuth callback
  bool hasPendingCallback() {
    if (!kIsWeb) return false;

    try {
      final callbackData = web_impl.getOAuthCallbackData();
      return callbackData != null;
    } catch (error) {
      if (kDebugMode) {
        print('Web OAuth Handler: Error checking for pending callback: $error');
      }
      return false;
    }
  }

  // Dispose resources
  void dispose() {
    _stopPolling();
  }
}
