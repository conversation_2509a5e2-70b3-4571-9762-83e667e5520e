import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'auth_service.dart';
import 'network_aware_supabase.dart';
import 'oauth_callback_handler.dart';
import 'web_oauth_handler.dart';

enum AuthenticationStatus {
  unknown,
  authenticated,
  unauthenticated,
}

class AuthStateManager {
  static final AuthStateManager _instance = AuthStateManager._internal();
  factory AuthStateManager() => _instance;
  AuthStateManager._internal();

  final StreamController<AuthenticationStatus> _authStatusController =
      StreamController<AuthenticationStatus>.broadcast();

  StreamSubscription<AuthState>? _authSubscription;
  AuthenticationStatus _currentStatus = AuthenticationStatus.unknown;

  // OAuth handlers
  final OAuthCallbackHandler _oauthHandler = OAuthCallbackHandler();
  final WebOAuthHandler _webOAuthHandler = WebOAuthHandler();

  // Network-aware wrapper
  final NetworkAwareSupabase _networkWrapper = NetworkAwareSupabase();

  // Stream of authentication status
  Stream<AuthenticationStatus> get authStatusStream =>
      _authStatusController.stream;

  // Current authentication status
  AuthenticationStatus get currentStatus => _currentStatus;

  // Initialize the auth state manager
  Future<void> initialize() async {
    try {
      if (kDebugMode) {
        print('Auth State Manager: Initializing...');
      }

      // Initialize network-aware wrapper
      await _networkWrapper.initialize();

      // Initialize OAuth handlers
      await _oauthHandler.initialize();
      await _webOAuthHandler.initialize();

      // Check for existing session with network handling
      final hasValidSession = await _networkWrapper.executeWithNetworkHandling(
            () => AuthService.initializeSession(),
            operationName: 'session_initialization',
            fallbackValue: false,
          ) ??
          false;

      if (hasValidSession && AuthService.isSignedIn) {
        if (kDebugMode) {
          print('Auth State Manager: Valid session found, user authenticated');
        }
        _updateAuthStatus(AuthenticationStatus.authenticated);
      } else {
        if (kDebugMode) {
          print('Auth State Manager: No valid session, user unauthenticated');
        }
        _updateAuthStatus(AuthenticationStatus.unauthenticated);
      }

      // Listen to auth state changes with network error handling
      _authSubscription = _networkWrapper
          .wrapAuthStateChanges(AuthService.authStateChanges)
          .listen(
        (AuthState authState) {
          _handleAuthStateChange(authState);
        },
        onError: (error) {
          if (kDebugMode) {
            print('Auth state change error: $error');
          }
          // Check if it's a network error
          if (_networkWrapper.isNetworkError(error)) {
            if (kDebugMode) {
              print('Network error detected, maintaining current auth status');
            }
            // Don't change auth status for network errors
            return;
          }
          _updateAuthStatus(AuthenticationStatus.unauthenticated);
        },
        cancelOnError: false, // Don't cancel the subscription on error
      );

      if (kDebugMode) {
        print('Auth State Manager: Initialization complete');
      }
    } catch (error) {
      if (kDebugMode) {
        print('Auth state manager initialization error: $error');
      }
      _updateAuthStatus(AuthenticationStatus.unauthenticated);
    }
  }

  // Handle auth state changes from Supabase
  void _handleAuthStateChange(AuthState authState) {
    switch (authState.event) {
      case AuthChangeEvent.signedIn:
        _updateAuthStatus(AuthenticationStatus.authenticated);
        break;
      case AuthChangeEvent.signedOut:
        _updateAuthStatus(AuthenticationStatus.unauthenticated);
        break;
      case AuthChangeEvent.tokenRefreshed:
        // Session refreshed, user is still authenticated
        _updateAuthStatus(AuthenticationStatus.authenticated);
        break;
      case AuthChangeEvent.userUpdated:
        // User data updated, but still authenticated
        if (AuthService.isSignedIn) {
          _updateAuthStatus(AuthenticationStatus.authenticated);
        } else {
          _updateAuthStatus(AuthenticationStatus.unauthenticated);
        }
        break;
      case AuthChangeEvent.passwordRecovery:
        // Password recovery doesn't change auth status
        break;
      default:
        // For any other events, check current auth status
        if (AuthService.isSignedIn && AuthService.hasValidSession) {
          _updateAuthStatus(AuthenticationStatus.authenticated);
        } else {
          _updateAuthStatus(AuthenticationStatus.unauthenticated);
        }
        break;
    }
  }

  // Update authentication status
  void _updateAuthStatus(AuthenticationStatus status) {
    if (_currentStatus != status) {
      _currentStatus = status;
      _authStatusController.add(status);

      if (kDebugMode) {
        print('Auth status changed to: $status');
      }
    }
  }

  // Sign out and update status
  Future<void> signOut() async {
    try {
      if (kDebugMode) {
        print('Auth State Manager: Starting sign out...');
        print('Auth State Manager: Is guest user: ${AuthService.isGuestUser}');
      }

      await AuthService.signOut();
      _updateAuthStatus(AuthenticationStatus.unauthenticated);

      if (kDebugMode) {
        print('Auth State Manager: Sign out completed successfully');
      }
    } catch (error) {
      if (kDebugMode) {
        print('Sign out error: $error');
      }
      // Even if sign out fails, update status to unauthenticated
      _updateAuthStatus(AuthenticationStatus.unauthenticated);
      rethrow;
    }
  }

  // Check if current user is a guest
  bool get isGuestUser => AuthService.isGuestUser;

  // Check if user is authenticated
  bool get isAuthenticated =>
      _currentStatus == AuthenticationStatus.authenticated;

  // Check if authentication status is unknown (still loading)
  bool get isLoading => _currentStatus == AuthenticationStatus.unknown;

  // Get current user
  User? get currentUser => AuthService.currentUser;

  // Get current session
  Session? get currentSession => AuthService.currentSession;

  // Dispose resources
  void dispose() {
    _authSubscription?.cancel();
    _authStatusController.close();
    _oauthHandler.dispose();
    _webOAuthHandler.dispose();
    _networkWrapper.dispose();
  }
}
