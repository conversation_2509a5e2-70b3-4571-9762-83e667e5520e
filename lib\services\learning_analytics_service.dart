import 'dart:async';
import 'dart:convert';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/learning_models.dart';
import '../services/ai_learning_service.dart';
import '../services/adaptive_prompt_service.dart';
import '../services/response_optimization_service.dart';
import '../services/learning_configuration_service.dart';

/// Service for learning analytics and monitoring
class LearningAnalyticsService {
  static const String _analyticsDataKey = 'learning_analytics_data';
  static const String _performanceMetricsKey = 'learning_performance_metrics';
  static const String _healthCheckKey = 'learning_health_check';

  // Singleton pattern
  static LearningAnalyticsService? _instance;
  static LearningAnalyticsService get instance =>
      _instance ??= LearningAnalyticsService._();
  LearningAnalyticsService._();

  // Internal state
  bool _isInitialized = false;
  Map<String, dynamic> _analyticsData = {};
  Map<String, dynamic> _performanceMetrics = {};
  Timer? _healthCheckTimer;

  /// Initialize the learning analytics service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadAnalyticsData();
      await _loadPerformanceMetrics();
      _startHealthChecks();
      _isInitialized = true;

      if (kDebugMode) {
        print('Learning Analytics Service: Initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Learning Analytics Service: Initialization failed - $e');
      }
    }
  }

  /// Dispose resources
  void dispose() {
    _healthCheckTimer?.cancel();
    _isInitialized = false;
  }

  /// Get comprehensive learning dashboard data
  Future<LearningDashboard> getLearningDashboard() async {
    final dashboard = LearningDashboard();

    // Get data from all learning services
    final aiLearningStats = AILearningService.instance.getCurrentStatistics();
    final optimizationStats =
        ResponseOptimizationService.instance.getOptimizationStatistics();
    final promptVersions =
        AdaptivePromptService.instance.getAllPromptVersions();
    final configuration =
        LearningConfigurationService.instance.getConfiguration();

    // Overall system health
    dashboard.systemHealth = await _calculateSystemHealth();

    // Learning effectiveness metrics
    dashboard.learningEffectiveness =
        _calculateLearningEffectiveness(aiLearningStats, optimizationStats);

    // Prompt performance comparison
    dashboard.promptPerformance = _analyzePromptPerformance(promptVersions);

    // Quality trends
    dashboard.qualityTrends = _calculateQualityTrends();

    // Configuration status
    dashboard.configurationStatus = _analyzeConfigurationStatus(configuration);

    // Recent activity
    dashboard.recentActivity = _getRecentActivity();

    // Performance recommendations
    dashboard.recommendations = _generateRecommendations(dashboard);

    return dashboard;
  }

  /// Calculate overall system health score
  Future<SystemHealth> _calculateSystemHealth() async {
    final health = SystemHealth();

    try {
      // Check service availability
      health.servicesOnline = await _checkServicesHealth();

      // Check data integrity
      health.dataIntegrity = await _checkDataIntegrity();

      // Check performance metrics
      health.performanceScore = _calculatePerformanceScore();

      // Check error rates
      health.errorRate = _calculateErrorRate();

      // Calculate overall health score
      health.overallScore = (health.servicesOnline * 0.3 +
          health.dataIntegrity * 0.25 +
          health.performanceScore * 0.25 +
          (1.0 - health.errorRate) * 0.2);

      health.status = _getHealthStatus(health.overallScore);
      health.lastChecked = DateTime.now();
    } catch (e) {
      health.status = 'Error';
      health.overallScore = 0.0;
      health.lastChecked = DateTime.now();

      if (kDebugMode) {
        print('Learning Analytics Service: Health check failed - $e');
      }
    }

    return health;
  }

  /// Calculate learning effectiveness metrics
  LearningEffectiveness _calculateLearningEffectiveness(
    LearningStatistics aiStats,
    Map<String, dynamic> optimizationStats,
  ) {
    final effectiveness = LearningEffectiveness();

    // Quality improvement over time
    effectiveness.qualityImprovement = _calculateQualityImprovement();

    // Learning rate (how quickly the system improves)
    effectiveness.learningRate = _calculateLearningRate(aiStats);

    // Adaptation success rate
    effectiveness.adaptationSuccessRate = _calculateAdaptationSuccessRate();

    // User satisfaction trend
    effectiveness.userSatisfactionTrend = _calculateUserSatisfactionTrend();

    // Overall effectiveness score
    effectiveness.overallScore = (effectiveness.qualityImprovement * 0.3 +
        effectiveness.learningRate * 0.25 +
        effectiveness.adaptationSuccessRate * 0.25 +
        effectiveness.userSatisfactionTrend * 0.2);

    return effectiveness;
  }

  /// Analyze prompt performance across versions
  PromptPerformanceAnalysis _analyzePromptPerformance(
      List<PromptVersion> promptVersions) {
    final analysis = PromptPerformanceAnalysis();

    if (promptVersions.isEmpty) {
      return analysis;
    }

    // Sort by version number
    promptVersions.sort((a, b) => a.version.compareTo(b.version));

    // Calculate performance metrics for each version
    analysis.versionPerformance = promptVersions.map((version) {
      return VersionPerformance(
        version: version.version,
        performanceScore: version.performanceScore,
        usageCount: version.usageCount,
        isActive: version.isActive,
        createdAt: version.createdAt,
      );
    }).toList();

    // Find best performing version
    final bestVersion = promptVersions
        .reduce((a, b) => a.performanceScore > b.performanceScore ? a : b);
    analysis.bestPerformingVersion = bestVersion.version;
    analysis.bestPerformanceScore = bestVersion.performanceScore;

    // Calculate improvement trend
    if (promptVersions.length > 1) {
      final firstVersion = promptVersions.first;
      final lastVersion = promptVersions.last;
      analysis.improvementTrend =
          lastVersion.performanceScore - firstVersion.performanceScore;
    }

    // Calculate average performance
    analysis.averagePerformance = promptVersions.fold(
            0.0, (sum, version) => sum + version.performanceScore) /
        promptVersions.length;

    return analysis;
  }

  /// Calculate quality trends over time
  QualityTrends _calculateQualityTrends() {
    final trends = QualityTrends();

    // Get historical quality data
    final qualityHistory = _getQualityHistory();

    if (qualityHistory.isNotEmpty) {
      // Calculate trend direction
      trends.direction = _calculateTrendDirection(qualityHistory);

      // Calculate trend strength
      trends.strength = _calculateTrendStrength(qualityHistory);

      // Recent quality score
      trends.currentQuality = qualityHistory.last['quality'] ?? 0.0;

      // Quality change from previous period
      if (qualityHistory.length > 1) {
        final previousQuality =
            qualityHistory[qualityHistory.length - 2]['quality'] ?? 0.0;
        trends.changeFromPrevious = trends.currentQuality - previousQuality;
      }

      // Historical data for charting
      trends.historicalData = qualityHistory;
    }

    return trends;
  }

  /// Analyze configuration status
  ConfigurationStatus _analyzeConfigurationStatus(
      LearningConfiguration config) {
    final status = ConfigurationStatus();

    status.isOptimal = _isConfigurationOptimal(config);
    status.learningEnabled = config.isLearningEnabled;
    status.analysisInterval = config.analysisInterval;
    status.optimizationEnabled = config.enablePromptOptimization;
    status.abTestingEnabled = config.enableABTesting;

    // Generate configuration recommendations
    status.recommendations = _generateConfigurationRecommendations(config);

    return status;
  }

  /// Get recent learning activity
  List<ActivityItem> _getRecentActivity() {
    final activities = <ActivityItem>[];

    // Get recent activities from analytics data
    final recentActivities =
        _analyticsData['recent_activities'] as List<dynamic>? ?? [];

    for (final activity in recentActivities.take(10)) {
      activities.add(ActivityItem(
        type: activity['type'] ?? 'unknown',
        description: activity['description'] ?? '',
        timestamp:
            DateTime.fromMillisecondsSinceEpoch(activity['timestamp'] ?? 0),
        severity: activity['severity'] ?? 'info',
      ));
    }

    return activities;
  }

  /// Generate performance recommendations
  List<String> _generateRecommendations(LearningDashboard dashboard) {
    final recommendations = <String>[];

    // System health recommendations
    if (dashboard.systemHealth.overallScore < 0.7) {
      recommendations.add(
          'System health is below optimal. Consider reviewing error logs and service status.');
    }

    // Learning effectiveness recommendations
    if (dashboard.learningEffectiveness.overallScore < 0.6) {
      recommendations.add(
          'Learning effectiveness is low. Consider adjusting learning parameters or increasing feedback collection.');
    }

    // Prompt performance recommendations
    if (dashboard.promptPerformance.improvementTrend < 0) {
      recommendations.add(
          'Prompt performance is declining. Consider reviewing recent optimizations or rolling back to a better performing version.');
    }

    // Quality trend recommendations
    if (dashboard.qualityTrends.direction == 'declining') {
      recommendations.add(
          'Quality trends are declining. Review recent changes and consider prompt optimization.');
    }

    // Configuration recommendations
    if (!dashboard.configurationStatus.isOptimal) {
      recommendations.addAll(dashboard.configurationStatus.recommendations);
    }

    return recommendations;
  }

  /// Record learning activity
  Future<void> recordActivity(String type, String description,
      {String severity = 'info'}) async {
    final activity = {
      'type': type,
      'description': description,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      'severity': severity,
    };

    final activities =
        _analyticsData['recent_activities'] as List<dynamic>? ?? [];
    activities.add(activity);

    // Keep only last 100 activities
    if (activities.length > 100) {
      activities.removeRange(0, activities.length - 100);
    }

    _analyticsData['recent_activities'] = activities;
    await _saveAnalyticsData();
  }

  /// Update performance metrics
  Future<void> updatePerformanceMetrics(String metric, double value) async {
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    // Update current value
    _performanceMetrics[metric] = value;

    // Update historical data
    final historyKey = '${metric}_history';
    final history = _performanceMetrics[historyKey] as List<dynamic>? ?? [];

    history.add({
      'value': value,
      'timestamp': timestamp,
    });

    // Keep only last 100 data points
    if (history.length > 100) {
      history.removeRange(0, history.length - 100);
    }

    _performanceMetrics[historyKey] = history;
    await _savePerformanceMetrics();
  }

  /// Start periodic health checks
  void _startHealthChecks() {
    _healthCheckTimer?.cancel();
    _healthCheckTimer = Timer.periodic(const Duration(minutes: 15), (timer) {
      _performHealthCheck();
    });
  }

  /// Perform automated health check
  Future<void> _performHealthCheck() async {
    try {
      final health = await _calculateSystemHealth();

      // Record health check activity
      await recordActivity(
        'health_check',
        'System health: ${health.status} (${(health.overallScore * 100).toStringAsFixed(1)}%)',
        severity: health.overallScore < 0.7 ? 'warning' : 'info',
      );

      // Update health metrics
      await updatePerformanceMetrics('system_health', health.overallScore);
    } catch (e) {
      await recordActivity(
        'health_check_error',
        'Health check failed: $e',
        severity: 'error',
      );
    }
  }

  /// Check services health
  Future<double> _checkServicesHealth() async {
    int healthyServices = 0;
    int totalServices =
        4; // AI Learning, Adaptive Prompt, Response Optimization, Configuration

    try {
      // Check AI Learning Service
      final stats = AILearningService.instance.getCurrentStatistics();
      if (stats.totalAnalyses >= 0) healthyServices++;
    } catch (e) {
      // Service not healthy
    }

    try {
      // Check Adaptive Prompt Service
      final promptVersions =
          AdaptivePromptService.instance.getAllPromptVersions();
      if (promptVersions.isNotEmpty) healthyServices++;
    } catch (e) {
      // Service not healthy
    }

    try {
      // Check Response Optimization Service
      final stats =
          ResponseOptimizationService.instance.getOptimizationStatistics();
      if (stats.isNotEmpty) healthyServices++;
    } catch (e) {
      // Service not healthy
    }

    try {
      // Check Learning Configuration Service
      final config = LearningConfigurationService.instance.getConfiguration();
      healthyServices++; // Service is accessible
    } catch (e) {
      // Service not healthy
    }

    return healthyServices / totalServices;
  }

  /// Check data integrity
  Future<double> _checkDataIntegrity() async {
    // Simplified data integrity check
    // In a full implementation, this would validate data consistency across services
    return 1.0; // Assume data is intact for now
  }

  /// Calculate performance score
  double _calculatePerformanceScore() {
    // Get recent performance metrics
    final recentMetrics =
        _performanceMetrics['recent_performance'] as List<dynamic>? ?? [];

    if (recentMetrics.isEmpty) return 0.5; // Neutral score

    // Calculate average performance from recent metrics
    final avgPerformance = recentMetrics.fold(
            0.0, (sum, metric) => sum + (metric['value'] ?? 0.0)) /
        recentMetrics.length;

    return math.min(1.0, math.max(0.0, avgPerformance));
  }

  /// Calculate error rate
  double _calculateErrorRate() {
    final errorHistory =
        _analyticsData['error_history'] as List<dynamic>? ?? [];
    final totalActivities = _analyticsData['total_activities'] ?? 1;

    return errorHistory.length / totalActivities;
  }

  /// Get health status string
  String _getHealthStatus(double score) {
    if (score >= 0.9) return 'Excellent';
    if (score >= 0.8) return 'Good';
    if (score >= 0.7) return 'Fair';
    if (score >= 0.5) return 'Poor';
    return 'Critical';
  }

  /// Calculate quality improvement
  double _calculateQualityImprovement() {
    final qualityHistory = _getQualityHistory();

    if (qualityHistory.length < 2) return 0.0;

    final firstQuality = qualityHistory.first['quality'] ?? 0.0;
    final lastQuality = qualityHistory.last['quality'] ?? 0.0;

    return lastQuality - firstQuality;
  }

  /// Calculate learning rate
  double _calculateLearningRate(LearningStatistics stats) {
    // Learning rate based on how quickly quality improves
    final qualityHistory = _getQualityHistory();

    if (qualityHistory.length < 3) return 0.0;

    // Calculate rate of improvement over time
    double totalImprovement = 0.0;
    int improvements = 0;

    for (int i = 1; i < qualityHistory.length; i++) {
      final current = qualityHistory[i]['quality'] ?? 0.0;
      final previous = qualityHistory[i - 1]['quality'] ?? 0.0;
      final improvement = current - previous;

      if (improvement > 0) {
        totalImprovement += improvement;
        improvements++;
      }
    }

    return improvements > 0 ? totalImprovement / improvements : 0.0;
  }

  /// Calculate adaptation success rate
  double _calculateAdaptationSuccessRate() {
    // Success rate of prompt adaptations
    final adaptationHistory =
        _analyticsData['adaptation_history'] as List<dynamic>? ?? [];

    if (adaptationHistory.isEmpty) return 0.5; // Neutral

    final successfulAdaptations = adaptationHistory
        .where((adaptation) => adaptation['success'] == true)
        .length;

    return successfulAdaptations / adaptationHistory.length;
  }

  /// Calculate user satisfaction trend
  double _calculateUserSatisfactionTrend() {
    // Based on feedback trends
    final feedbackHistory =
        _analyticsData['feedback_history'] as List<dynamic>? ?? [];

    if (feedbackHistory.isEmpty) return 0.5; // Neutral

    final positiveFeedback = feedbackHistory
        .where((feedback) => feedback['type'] == 'positive')
        .length;

    return positiveFeedback / feedbackHistory.length;
  }

  /// Get quality history
  List<Map<String, dynamic>> _getQualityHistory() {
    final history =
        _performanceMetrics['quality_history'] as List<dynamic>? ?? [];
    return history.cast<Map<String, dynamic>>();
  }

  /// Calculate trend direction
  String _calculateTrendDirection(List<Map<String, dynamic>> data) {
    if (data.length < 2) return 'stable';

    final recent = data.length > 5 ? data.sublist(data.length - 5) : data;
    double trend = 0.0;

    for (int i = 1; i < recent.length; i++) {
      final current = recent[i]['quality'] ?? 0.0;
      final previous = recent[i - 1]['quality'] ?? 0.0;
      trend += current - previous;
    }

    if (trend > 0.05) return 'improving';
    if (trend < -0.05) return 'declining';
    return 'stable';
  }

  /// Calculate trend strength
  double _calculateTrendStrength(List<Map<String, dynamic>> data) {
    if (data.length < 2) return 0.0;

    final values = data.map((d) => d['quality'] as double? ?? 0.0).toList();

    // Calculate correlation coefficient for trend strength
    final n = values.length;
    final x = List.generate(n, (i) => i.toDouble());
    final y = values;

    final sumX = x.fold(0.0, (sum, val) => sum + val);
    final sumY = y.fold(0.0, (sum, val) => sum + val);
    final sumXY =
        List.generate(n, (i) => x[i] * y[i]).fold(0.0, (sum, val) => sum + val);
    final sumX2 = x.fold(0.0, (sum, val) => sum + val * val);
    final sumY2 = y.fold(0.0, (sum, val) => sum + val * val);

    final numerator = n * sumXY - sumX * sumY;
    final denominator =
        math.sqrt((n * sumX2 - sumX * sumX) * (n * sumY2 - sumY * sumY));

    return denominator != 0 ? (numerator / denominator).abs() : 0.0;
  }

  /// Check if configuration is optimal
  bool _isConfigurationOptimal(LearningConfiguration config) {
    // Check if configuration settings are within optimal ranges
    return config.isLearningEnabled &&
        config.analysisInterval.inHours >= 1 &&
        config.analysisInterval.inHours <= 24 &&
        config.minFeedbackThreshold >= 5 &&
        config.minFeedbackThreshold <= 50 &&
        config.negativeThreshold >= 0.2 &&
        config.negativeThreshold <= 0.4;
  }

  /// Generate configuration recommendations
  List<String> _generateConfigurationRecommendations(
      LearningConfiguration config) {
    final recommendations = <String>[];

    if (!config.isLearningEnabled) {
      recommendations
          .add('Enable learning to improve AI performance over time');
    }

    if (config.analysisInterval.inHours < 1) {
      recommendations.add('Increase analysis interval to reduce system load');
    } else if (config.analysisInterval.inHours > 24) {
      recommendations
          .add('Decrease analysis interval for more responsive learning');
    }

    if (config.minFeedbackThreshold < 5) {
      recommendations.add(
          'Increase minimum feedback threshold for more reliable learning');
    } else if (config.minFeedbackThreshold > 50) {
      recommendations.add(
          'Decrease minimum feedback threshold for more frequent learning');
    }

    if (!config.enablePromptOptimization) {
      recommendations
          .add('Enable prompt optimization for better response quality');
    }

    return recommendations;
  }

  /// Load analytics data from storage
  Future<void> _loadAnalyticsData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final dataJson = prefs.getString(_analyticsDataKey);

      if (dataJson != null) {
        _analyticsData = Map<String, dynamic>.from(json.decode(dataJson));
      }
    } catch (e) {
      if (kDebugMode) {
        print('Learning Analytics Service: Failed to load analytics data - $e');
      }
      _analyticsData = {};
    }
  }

  /// Save analytics data to storage
  Future<void> _saveAnalyticsData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_analyticsDataKey, json.encode(_analyticsData));
    } catch (e) {
      if (kDebugMode) {
        print('Learning Analytics Service: Failed to save analytics data - $e');
      }
    }
  }

  /// Load performance metrics from storage
  Future<void> _loadPerformanceMetrics() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final metricsJson = prefs.getString(_performanceMetricsKey);

      if (metricsJson != null) {
        _performanceMetrics =
            Map<String, dynamic>.from(json.decode(metricsJson));
      }
    } catch (e) {
      if (kDebugMode) {
        print(
            'Learning Analytics Service: Failed to load performance metrics - $e');
      }
      _performanceMetrics = {};
    }
  }

  /// Save performance metrics to storage
  Future<void> _savePerformanceMetrics() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
          _performanceMetricsKey, json.encode(_performanceMetrics));
    } catch (e) {
      if (kDebugMode) {
        print(
            'Learning Analytics Service: Failed to save performance metrics - $e');
      }
    }
  }
}
