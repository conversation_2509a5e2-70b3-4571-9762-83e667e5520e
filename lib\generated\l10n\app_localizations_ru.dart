/// Generated file. Do not edit.
///
/// This file contains the localized strings for the TripWiseGo app.
/// To add a new localized string, add it to the appropriate .arb file
/// in the lib/l10n directory and run `flutter gen-l10n`.


import 'app_localizations.dart';

/// The translations for Russian (`ru`).
class AppLocalizationsRu extends AppLocalizations {
  AppLocalizationsRu([String locale = 'ru']) : super(locale);

  @override
  String get appTitle => 'TripWiseGo';

  @override
  String get welcome => 'Добро пожаловать';

  @override
  String get guestUser => 'Гостевой пользователь';

  @override
  String get readyForAdventure => 'Готов к следующему приключению';

  @override
  String get exploringAsGuest => 'Исследование мира как гость';

  @override
  String get editProfile => 'Редактировать профиль';

  @override
  String get saveChanges => 'Сохранить изменения';

  @override
  String get cancel => 'Отмена';

  @override
  String get username => 'Имя пользователя';

  @override
  String get email => 'Электронная почта';

  @override
  String get profileUpdatedSuccessfully => 'Профиль успешно обновлен!';

  @override
  String failedToUpdateProfile(String error) {
    return 'Не удалось обновить профиль: $error';
  }

  @override
  String get profilePictureUpdatedSuccessfully => 'Фото профиля успешно обновлено!';

  @override
  String failedToUploadImage(String error) {
    return 'Не удалось загрузить изображение: $error';
  }

  @override
  String get profileEditingNotAvailableForGuests => 'Редактирование профиля недоступно для гостевых пользователей';

  @override
  String get profilePictureEditingNotAvailableForGuests => 'Редактирование фото профиля недоступно для гостевых пользователей';

  @override
  String get usernameCannotBeEmpty => 'Имя пользователя не может быть пустым';

  @override
  String get usernameMustBeBetween2And30Characters => 'Имя пользователя должно содержать от 2 до 30 символов';

  @override
  String get plan => 'План';

  @override
  String get termsOfService => 'Условия обслуживания';

  @override
  String get language => 'Язык';

  @override
  String get privacyPolicy => 'Политика конфиденциальности';

  @override
  String get support => 'Поддержка';

  @override
  String get helpCenter => 'Центр помощи';

  @override
  String get contactUs => 'Связаться с нами';

  @override
  String get helpSupport => 'Help & Support';

  @override
  String get howCanWeHelpYou => 'How can we help you?';

  @override
  String get helpSupportGreeting => 'Hi! I\'m here to help you with TripwiseGO. You can ask me about app features, troubleshooting, or report any issues you\'re experiencing.';

  @override
  String get helpSupportWelcome => 'Welcome to TripwiseGO Support! Here are some things I can help you with:';

  @override
  String get helpSupportFeatures => '• App features and how to use them\n• Navigation and account help\n• Troubleshooting common issues\n• Reporting bugs or problems';

  @override
  String get helpSupportAskQuestion => 'Feel free to ask me anything or describe any issues you\'re having!';

  @override
  String get reportIssue => 'Report Issue';

  @override
  String get reportBug => 'Report Bug';

  @override
  String get reportProblem => 'Report Problem';

  @override
  String get issueCategory => 'Issue Category';

  @override
  String get bugReport => 'Bug Report';

  @override
  String get featureRequest => 'Feature Request';

  @override
  String get generalFeedback => 'General Feedback';

  @override
  String get accountIssue => 'Account Issue';

  @override
  String get technicalProblem => 'Technical Problem';

  @override
  String get yourName => 'Your Name';

  @override
  String get yourEmail => 'Your Email';

  @override
  String get issueDescription => 'Issue Description';

  @override
  String get describeIssueDetail => 'Please describe the issue in detail';

  @override
  String get optionalScreenshot => 'Screenshot (Optional)';

  @override
  String get submitReport => 'Submit Report';

  @override
  String get reportSubmitted => 'Report Submitted';

  @override
  String get reportSubmittedSuccess => 'Thank you! Your report has been submitted successfully. We\'ll look into it and get back to you if needed.';

  @override
  String get reportSubmissionFailed => 'Failed to submit report. Please try again later.';

  @override
  String get nameRequired => 'Name is required';

  @override
  String get emailRequired => 'Email is required';

  @override
  String get validEmailRequired => 'Please enter a valid email address';

  @override
  String get descriptionRequired => 'Issue description is required';

  @override
  String get selectCategory => 'Please select a category';

  @override
  String get subscription => 'Subscription';

  @override
  String get subscriptionActive => 'Subscription Active!';

  @override
  String get welcomeToPremium => 'Welcome to TripwiseGO Premium! Enjoy unlimited access to all features.';

  @override
  String get currentPlan => 'Current Plan';

  @override
  String get trial => 'Trial';

  @override
  String get active => 'Active';

  @override
  String trialEndsIn(int days) {
    return 'Trial ends in $days days';
  }

  @override
  String renewsIn(int days) {
    return 'Renews in $days days';
  }

  @override
  String get yourBenefits => 'Your Benefits';

  @override
  String get startPlanningTrip => 'Start Planning Your Trip';

  @override
  String get manageSubscription => 'Manage Subscription';

  @override
  String get basic => 'Basic';

  @override
  String get premium => 'Premium';

  @override
  String get basicPlaceRecommendations => 'Basic Place Recommendations';

  @override
  String get locationRecommendationSwiping => 'Location Recommendation Swiping (20 swipes/day)';

  @override
  String get aiPoweredTravelPlanner => 'AI-Powered Travel Planner';

  @override
  String get unlimitedQuizzes => 'Unlimited Quizzes & Fun Facts';

  @override
  String get noAds => 'No-Ads';

  @override
  String get oneMonth => '1 Month';

  @override
  String get threeMonths => '3 Months';

  @override
  String get fiveMonths => '5 Months';

  @override
  String dayFreeTrial(int days) {
    return '$days-day free trial';
  }

  @override
  String get billedMonthly => 'Billed monthly';

  @override
  String get billedTwiceAnnually => 'Billed twice annually';

  @override
  String get billedAnnually => 'Billed annually';

  @override
  String get save45Percent => 'SAVE 45%';

  @override
  String get continueButton => 'Continue';

  @override
  String get termsAndConditions => 'Terms and Conditions';

  @override
  String get failedToSubscribe => 'Failed to subscribe. Please try again.';

  @override
  String get signOut => 'Выйти';

  @override
  String get selectLanguage => 'Выбрать язык';

  @override
  String get chooseYourPreferredLanguage => 'Выберите предпочитаемый язык';

  @override
  String get languageUpdatedSuccessfully => 'Язык успешно обновлен!';

  @override
  String get home => 'Главная';

  @override
  String get match => 'Совпадение';

  @override
  String get chat => 'Чат';

  @override
  String get profile => 'Профиль';

  @override
  String get loading => 'Загрузка...';

  @override
  String get error => 'Ошибка';

  @override
  String get retry => 'Повторить';

  @override
  String get ok => 'ОК';

  @override
  String get yes => 'Да';

  @override
  String get no => 'Нет';

  @override
  String get save => 'Сохранить';

  @override
  String get delete => 'Удалить';

  @override
  String get edit => 'Редактировать';

  @override
  String get add => 'Добавить';

  @override
  String get remove => 'Удалить';

  @override
  String get close => 'Закрыть';

  @override
  String get back => 'Назад';

  @override
  String get next => 'Далее';

  @override
  String get previous => 'Предыдущий';

  @override
  String get done => 'Готово';

  @override
  String get search => 'Поиск';

  @override
  String get noResultsFound => 'Результаты не найдены';

  @override
  String get tryAgain => 'Попробовать снова';

  @override
  String get somethingWentWrong => 'Что-то пошло не так';

  @override
  String get networkError => 'Ошибка сети. Проверьте подключение.';

  @override
  String get serverError => 'Ошибка сервера. Попробуйте позже.';

  @override
  String get invalidInput => 'Неверный ввод';

  @override
  String get required => 'Обязательно';

  @override
  String get optional => 'Необязательно';

  @override
  String get itinerary => 'Itinerary';

  @override
  String get yourItineraries => 'Your Itineraries';

  @override
  String get startPlanningYourTrip => 'Start Planning Your Trip';

  @override
  String get tripPlanningFeaturesWillAppearHere => 'Your trip planning features will appear here. The persistent authentication is now working!';

  @override
  String get forYou => 'For You';

  @override
  String get liked => 'Liked';

  @override
  String get collab => 'Collab';

  @override
  String get collaborativeItinerary => 'Collaborative';

  @override
  String get endSession => 'End Session';

  @override
  String get logout => 'Logout';

  @override
  String logoutFailed(String error) {
    return 'Logout failed: $error';
  }

  @override
  String get noItineraryFound => 'Маршрут не найден';

  @override
  String get askAiToCreateTravelPlan => 'Попросите наш ИИ создать план путешествия для вас!';

  @override
  String get saturday => 'Суббота';

  @override
  String get tuesday => 'Вторник';

  @override
  String dayNumber(int number) {
    return 'День $number';
  }

  @override
  String get itineraryOverview => 'Обзор маршрута';

  @override
  String daysAndNights(int days, int nights) {
    return '$daysд $nightsн';
  }

  @override
  String get hiImWanderlyAi => 'Привет, я Wanderly AI 🌏';

  @override
  String get yourTravelAiAssistant => 'Ваш ИИ-помощник по путешествиям, как я могу помочь вам сегодня?';

  @override
  String get useThisBubbleChat => 'Используйте этот пузырьковый чат';

  @override
  String get aiAssistant => 'ИИ-помощник';

  @override
  String get chatHistory => 'История чата';

  @override
  String get newChat => 'Новый чат';

  @override
  String get addImage => 'Добавить изображение';

  @override
  String get camera => 'Камера';

  @override
  String get gallery => 'Галерея';

  @override
  String get microphonePermissionRequired => 'Для голосового ввода требуется разрешение микрофона';

  @override
  String get speechRecognitionNotAvailable => 'Распознавание речи недоступно на этом устройстве';

  @override
  String get listening => 'Слушаю...';

  @override
  String get deleteChat => 'Удалить чат';

  @override
  String get deleteChatConfirmation => 'Вы уверены, что хотите удалить этот чат? Это действие нельзя отменить.';

  @override
  String get chatDeletedSuccessfully => 'Чат успешно удален';

  @override
  String get pleaseEnterSearchQuery => 'Пожалуйста, введите поисковый запрос';

  @override
  String get dailySearchLimitReached => 'Достигнут дневной лимит поиска. Вы можете выполнять 5 поисков в день.';

  @override
  String get searchingTheWeb => 'Поиск в интернете...';

  @override
  String get webSearchModeActive => 'Режим веб-поиска активен';

  @override
  String get pleaseWaitWhileSearching => 'Пожалуйста, подождите, пока я ищу информацию';

  @override
  String get yourNextMessageWillSearch => 'Ваше следующее сообщение будет искать в интернете';

  @override
  String get disableWebSearch => 'Отключить веб-поиск';

  @override
  String get enableWebSearch => 'Включить веб-поиск';

  @override
  String get switchBackToAiChatMode => 'Вернуться к режиму ИИ-чата';

  @override
  String get searchWebForCurrentInfo => 'Искать актуальную информацию в интернете';

  @override
  String get pickImageFromGallery => 'Выбрать изображение из галереи';

  @override
  String get uploadImageForAiAnalysis => 'Загрузить изображение для анализа ИИ';

  @override
  String get yourMessage => 'Ваше сообщение';

  @override
  String get wanderlyAi => 'Wanderly AI';

  @override
  String get webSearch => 'Веб-поиск:';

  @override
  String get like => 'Нравится';

  @override
  String get dislike => 'Не нравится';

  @override
  String get copy => 'Копировать';

  @override
  String get regenerate => 'Перегенерировать';

  @override
  String get failedToSubmitFeedback => 'Не удалось отправить отзыв. Пожалуйста, попробуйте еще раз.';

  @override
  String get thankYouForFeedback => 'Спасибо за ваш отзыв! 🙏';

  @override
  String get feedbackReceivedThanks => 'Отзыв получен. Спасибо за помощь в улучшении! 🚀';

  @override
  String get responseCopiedToClipboard => 'Ответ скопирован в буфер обмена';

  @override
  String get wanderlyAiIsTyping => 'Wanderly AI печатает';

  @override
  String get stopGeneration => 'Остановить генерацию';

  @override
  String get youHaveChatsLeft => 'У вас осталось 10 чатов';

  @override
  String get enterSearchQuery => 'Введите ваш поисковый запрос...';

  @override
  String get askMeAnythingOrLongPress => 'Спросите меня о чем угодно или долго нажмите, чтобы говорить...';

  @override
  String failedToPickImage(String error) {
    return 'Не удалось выбрать изображение: $error';
  }

  @override
  String get failedToAnalyzeImage => 'Не удалось проанализировать изображение. Пожалуйста, попробуйте еще раз.';

  @override
  String get responseGenerationStopped => 'Генерация ответа остановлена.';

  @override
  String get unknownDestination => 'Неизвестное направление';

  @override
  String get congratulations => 'Congratulations';

  @override
  String get itsAMatch => 'It\'s a match';

  @override
  String get travelVibesAligned => 'Travel vibes aligned pack\nyour bags, you\'ve got a match!';

  @override
  String get matchedPreferences => 'Matched Preferences:';

  @override
  String get addToItinerary => 'Add to Itinerary';

  @override
  String get keepSwiping => 'Keep swiping';

  @override
  String get versionInfo => 'Version Info';

  @override
  String get appVersion => 'App Version';

  @override
  String get buildNumber => 'Build Number';

  @override
  String get packageName => 'Package Name';

  @override
  String get appName => 'App Name';

  @override
  String get buildSignature => 'Build Signature';

  @override
  String get installerStore => 'Installer Store';

  @override
  String get deviceInfo => 'Device Information';

  @override
  String get operatingSystem => 'Operating System';

  @override
  String get flutterVersion => 'Flutter Version';

  @override
  String get dartVersion => 'Dart Version';

  @override
  String get leaveAReview => 'Leave a Review';

  @override
  String get rateOurApp => 'Rate Our App';

  @override
  String get enjoyingTripWiseGo => 'Enjoying TripWiseGo?';

  @override
  String get helpUsImproveByLeavingReview => 'Help us improve by leaving a review on the app store. Your feedback means a lot to us!';

  @override
  String get rateNow => 'Rate Now';

  @override
  String get maybeLater => 'Maybe Later';

  @override
  String get reviewFeatureNotAvailable => 'Review feature is only available in production builds';

  @override
  String get unableToOpenStore => 'Unable to open app store. Please try again later.';

  @override
  String get thankYouForReview => 'Thank you for taking the time to review our app!';
}
