import 'dart:async';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import '../models/itinerary.dart';
import '../services/itinerary_service.dart';
import '../generated/l10n/app_localizations.dart';

class PlanPage extends StatefulWidget {
  final Itinerary? initialItinerary;

  const PlanPage({
    super.key,
    this.initialItinerary,
  });

  @override
  State<PlanPage> createState() => _PlanPageState();
}

class _PlanPageState extends State<PlanPage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  Completer<GoogleMapController> _mapController = Completer();
  Set<Marker> _markers = {};
  Set<Polyline> _polylines = {};

  Itinerary? _currentItinerary;
  bool _isLoading = true;
  bool _hasLocationPermission = false;
  int _selectedDay = 1;
  bool _enablePolylines = true; // Flag to disable polylines if there are issues

  // Map state
  static const CameraPosition _defaultPosition = CameraPosition(
    target: LatLng(37.7749, -122.4194), // San Francisco default
    zoom: 10,
  );

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeData();
    _checkLocationPermission();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
  }

  Future<void> _initializeData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      if (widget.initialItinerary != null) {
        _currentItinerary = widget.initialItinerary;
      } else {
        // Load the most recent itinerary
        final itineraries = await ItineraryService.getAllItineraries();
        if (itineraries.isNotEmpty) {
          _currentItinerary = itineraries.first;
        }
      }

      // Validate that the itinerary has actual content
      if (_currentItinerary != null && !_isValidItinerary(_currentItinerary!)) {
        _currentItinerary = null;
      }

      if (_currentItinerary != null) {
        await _loadMapData();
      }
    } catch (e) {
      debugPrint('Error loading itinerary data: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Check if an itinerary has valid content (destinations and activities)
  bool _isValidItinerary(Itinerary itinerary) {
    // Check if itinerary has destinations
    if (itinerary.destinations.isEmpty) {
      return false;
    }

    // Check if itinerary has activities in dailyActivities
    bool hasActivities = false;
    for (final destination in itinerary.destinations) {
      if (itinerary.dailyActivities.containsKey(destination) &&
          itinerary.dailyActivities[destination]!.isNotEmpty) {
        hasActivities = true;
        break;
      }
    }

    // Also check day-specific activities
    if (!hasActivities &&
        itinerary.daySpecificActivities != null &&
        itinerary.daySpecificActivities!.isNotEmpty) {
      for (final dayActivities in itinerary.daySpecificActivities!.values) {
        for (final destinationActivities in dayActivities.values) {
          if (destinationActivities.isNotEmpty) {
            hasActivities = true;
            break;
          }
        }
        if (hasActivities) break;
      }
    }

    return hasActivities;
  }

  /// Refresh the itinerary data (useful when called from TabBarView)
  Future<void> refreshData() async {
    await _initializeData();
  }

  Future<void> _checkLocationPermission() async {
    try {
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
      }

      setState(() {
        _hasLocationPermission = permission == LocationPermission.whileInUse ||
            permission == LocationPermission.always;
      });
    } catch (e) {
      debugPrint('Error checking location permission: $e');
    }
  }

  Future<void> _loadMapData() async {
    if (_currentItinerary == null) return;

    try {
      final markers = <Marker>{};
      final coordinates = <LatLng>[];

      // Get coordinates for each destination
      for (int i = 0; i < _currentItinerary!.destinations.length; i++) {
        final destination = _currentItinerary!.destinations[i];

        try {
          final locations = await locationFromAddress(destination);
          if (locations.isNotEmpty) {
            final location = locations.first;
            final latLng = LatLng(location.latitude, location.longitude);
            coordinates.add(latLng);

            markers.add(
              Marker(
                markerId: MarkerId('destination_$i'),
                position: latLng,
                infoWindow: InfoWindow(
                  title: destination,
                  snippet: 'Day ${i + 1}',
                ),
                icon: BitmapDescriptor.defaultMarkerWithHue(
                  i == 0 ? BitmapDescriptor.hueGreen : BitmapDescriptor.hueBlue,
                ),
              ),
            );
          }
        } catch (e) {
          debugPrint('Error geocoding $destination: $e');
        }
      }

      // Create polyline connecting destinations with error handling
      final polylines = <Polyline>{};
      if (coordinates.length > 1 && _enablePolylines) {
        try {
          polylines.add(
            Polyline(
              polylineId: const PolylineId('route'),
              points: coordinates,
              color: const Color(0xFF0D76FF),
              width: 3,
              patterns: [PatternItem.dash(20), PatternItem.gap(10)],
            ),
          );
        } catch (e) {
          debugPrint('Error creating polyline: $e');
          // Disable polylines for future attempts if there's an error
          _enablePolylines = false;
        }
      }

      if (mounted) {
        setState(() {
          _markers = markers;
          _polylines = polylines;
        });
      }

      // Adjust camera to show all markers
      if (coordinates.isNotEmpty) {
        _fitMarkersInView(coordinates);
      }
    } catch (e) {
      debugPrint('Error loading map data: $e');
    }
  }

  Future<void> _fitMarkersInView(List<LatLng> coordinates) async {
    if (coordinates.isEmpty) return;

    final controller = await _mapController.future;

    if (coordinates.length == 1) {
      controller.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(
            target: coordinates.first,
            zoom: 12,
          ),
        ),
      );
    } else {
      // Calculate bounds
      double minLat = coordinates.first.latitude;
      double maxLat = coordinates.first.latitude;
      double minLng = coordinates.first.longitude;
      double maxLng = coordinates.first.longitude;

      for (final coord in coordinates) {
        minLat = minLat < coord.latitude ? minLat : coord.latitude;
        maxLat = maxLat > coord.latitude ? maxLat : coord.latitude;
        minLng = minLng < coord.longitude ? minLng : coord.longitude;
        maxLng = maxLng > coord.longitude ? maxLng : coord.longitude;
      }

      controller.animateCamera(
        CameraUpdate.newLatLngBounds(
          LatLngBounds(
            southwest: LatLng(minLat, minLng),
            northeast: LatLng(maxLat, maxLng),
          ),
          100.0, // padding
        ),
      );
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F9FC),
      body: SafeArea(
        child: _isLoading
            ? _buildLoadingState()
            : _currentItinerary == null
                ? _buildEmptyState()
                : _buildPlanContent(),
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF0D76FF)),
      ),
    );
  }

  Widget _buildEmptyState() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.map_outlined,
                size: 80,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                AppLocalizations.of(context).noItineraryFound,
                style: GoogleFonts.instrumentSans(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF2D3748),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                AppLocalizations.of(context).askAiToCreateTravelPlan,
                style: GoogleFonts.instrumentSans(
                  fontSize: 16,
                  color: const Color(0xFF718096),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPlanContent() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Column(
          children: [
            // Header with itinerary info
            _buildHeader(),

            // Day selector
            _buildDaySelector(),

            // Map and itinerary content
            Expanded(
              child: _buildMapAndItinerary(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    if (_currentItinerary == null) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                AppLocalizations.of(context).saturday,
                style: GoogleFonts.instrumentSans(
                  fontSize: 16,
                  color: const Color(0xFF718096),
                ),
              ),
              const Spacer(),
              Text(
                AppLocalizations.of(context).tuesday,
                style: GoogleFonts.instrumentSans(
                  fontSize: 16,
                  color: const Color(0xFF718096),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Text(
                _currentItinerary!.startDate,
                style: GoogleFonts.instrumentSans(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF2D3748),
                ),
              ),
              const Spacer(),
              Text(
                _currentItinerary!.endDate,
                style: GoogleFonts.instrumentSans(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF2D3748),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDaySelector() {
    if (_currentItinerary?.daySpecificActivities == null) {
      return const SizedBox.shrink();
    }

    final totalDays = _currentItinerary!.daySpecificActivities!.length;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: List.generate(totalDays, (index) {
          final day = index + 1;
          final isSelected = day == _selectedDay;

          return GestureDetector(
            onTap: () {
              setState(() {
                _selectedDay = day;
              });
            },
            child: Container(
              margin: const EdgeInsets.only(right: 12),
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              decoration: BoxDecoration(
                color: isSelected ? const Color(0xFF0D76FF) : Colors.white,
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: isSelected
                      ? const Color(0xFF0D76FF)
                      : const Color(0xFFE2E8F0),
                ),
              ),
              child: Text(
                AppLocalizations.of(context).dayNumber(day),
                style: GoogleFonts.instrumentSans(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: isSelected ? Colors.white : const Color(0xFF718096),
                ),
              ),
            ),
          );
        }),
      ),
    );
  }

  Widget _buildMapAndItinerary() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Column(
          children: [
            // Google Maps
            Expanded(
              flex: 2,
              child: _buildGoogleMap(),
            ),

            // Itinerary overview
            Expanded(
              flex: 1,
              child: _buildItineraryOverview(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGoogleMap() {
    return GoogleMap(
      onMapCreated: (GoogleMapController controller) {
        _mapController.complete(controller);
      },
      initialCameraPosition: _defaultPosition,
      markers: _markers,
      polylines: _polylines,
      myLocationEnabled: _hasLocationPermission,
      myLocationButtonEnabled: _hasLocationPermission,
      zoomControlsEnabled: false,
      mapToolbarEnabled: false,
      compassEnabled: true,
      buildingsEnabled: true,
      trafficEnabled: false,
    );
  }

  Widget _buildItineraryOverview() {
    if (_currentItinerary == null) return const SizedBox.shrink();

    final activitiesForDay =
        _currentItinerary!.daySpecificActivities?[_selectedDay] ??
            _currentItinerary!.dailyActivities;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context).itineraryOverview,
            style: GoogleFonts.instrumentSans(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF2D3748),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '${_currentItinerary!.title} - ${AppLocalizations.of(context).daysAndNights(_currentItinerary!.destinations.length, _currentItinerary!.destinations.length - 1)}',
            style: GoogleFonts.instrumentSans(
              fontSize: 14,
              color: const Color(0xFF718096),
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: _buildDestinationCards(activitiesForDay),
          ),
        ],
      ),
    );
  }

  Widget _buildDestinationCards(Map<String, List<String>> activities) {
    return ListView.builder(
      scrollDirection: Axis.horizontal,
      itemCount: _currentItinerary!.destinations.length,
      itemBuilder: (context, index) {
        final destination = _currentItinerary!.destinations[index];
        final destinationActivities = activities[destination] ?? [];

        return Container(
          width: 200,
          margin: const EdgeInsets.only(right: 12),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            image: const DecorationImage(
              image: NetworkImage(
                  'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop'),
              fit: BoxFit.cover,
            ),
          ),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.transparent,
                  Colors.black.withOpacity(0.7),
                ],
              ),
            ),
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        destination,
                        style: GoogleFonts.instrumentSans(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.orange,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            Icons.star,
                            size: 12,
                            color: Colors.white,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '4.8',
                            style: GoogleFonts.instrumentSans(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    const Icon(
                      Icons.location_on,
                      size: 14,
                      color: Colors.white70,
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        'Indonesia, Indonesia 123',
                        style: GoogleFonts.instrumentSans(
                          fontSize: 12,
                          color: Colors.white70,
                        ),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        color: const Color(0xFF0D76FF),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: const Icon(
                        Icons.arrow_forward,
                        size: 16,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
