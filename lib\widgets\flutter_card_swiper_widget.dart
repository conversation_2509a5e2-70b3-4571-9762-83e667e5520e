import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_card_swiper/flutter_card_swiper.dart';
import 'package:google_fonts/google_fonts.dart';
import '../data/destinations_data.dart';
import '../screens/likes_screen.dart';
import '../widgets/optimized_network_image.dart';
import '../services/image_preloading_service.dart';
import 'dart:math' as math; // Import the math library

class FlutterCardSwiperWidget extends StatefulWidget {
  final List<Map<String, dynamic>> destinations;
  final Function(Map<String, dynamic>, bool) onCardSwiped;
  final int? initialIndex;
  final Function(int)? onIndexChanged;
  final Function(Map<String, dynamic>)? onAddToItinerary;

  const FlutterCardSwiperWidget({
    super.key,
    required this.destinations,
    required this.onCardSwiped,
    this.initialIndex,
    this.onIndexChanged,
    this.onAddToItinerary,
  });

  @override
  State<FlutterCardSwiperWidget> createState() =>
      _FlutterCardSwiperWidgetState();
}

class _FlutterCardSwiperWidgetState extends State<FlutterCardSwiperWidget> {
  late CardSwiperController controller;
  late List<Map<String, dynamic>> currentDestinations;
  final ImagePreloadingService _imagePreloadingService =
      ImagePreloadingService();
  int _currentIndex = 0;
  int _currentCardIndex = 0; // Track the current card being displayed

  @override
  void initState() {
    super.initState();
    controller = CardSwiperController();
    currentDestinations = List.from(widget.destinations);
    _currentIndex = widget.initialIndex ?? 0;
    _currentCardIndex = 0; // Always start at 0 for the visible card stack

    // Filter out already swiped destinations if starting from a specific index
    if (_currentIndex > 0 && _currentIndex < currentDestinations.length) {
      currentDestinations = currentDestinations.sublist(_currentIndex);
      if (kDebugMode) {
        print(
            'FlutterCardSwiperWidget: Filtered to ${currentDestinations.length} destinations starting from index $_currentIndex');
      }
    }

    _initializeImagePreloading();
  }

  Future<void> _initializeImagePreloading() async {
    await _imagePreloadingService.initialize();
    if (mounted && currentDestinations.isNotEmpty) {
      // Preload initial set of images
      _imagePreloadingService.preloadMatchImages(
          context, currentDestinations, 0);
    }
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  bool _onSwipe(
    int previousIndex,
    int? currentIndex,
    CardSwiperDirection direction,
  ) {
    final swipedDestination = currentDestinations[previousIndex];
    // Invert the direction logic due to 180-degree rotation
    final isLike = direction == CardSwiperDirection.left;

    // Update current card index
    _currentCardIndex = currentIndex ?? 0;

    // Calculate the absolute index in the full destination list
    final absoluteIndex = (widget.initialIndex ?? 0) + previousIndex;
    widget.onIndexChanged?.call(absoluteIndex);

    if (kDebugMode) {
      print(
          'FlutterCardSwiperWidget: Swiped card at local index $previousIndex, absolute index $absoluteIndex');
    }

    // Add haptic feedback
    HapticFeedback.lightImpact();

    // Preload next batch of images for smooth swiping
    if (currentIndex != null && currentIndex < currentDestinations.length) {
      _imagePreloadingService.preloadMatchImages(
        context,
        currentDestinations,
        currentIndex,
      );
    }

    // Call the callback
    widget.onCardSwiped(swipedDestination, isLike);

    return true;
  }

  @override
  Widget build(BuildContext context) {
    if (currentDestinations.isEmpty) {
      return _buildEmptyState();
    }

    return Column(
      children: [
        // Card swiper
        Expanded(
          child: Transform.rotate(
            angle: math.pi,
            child: CardSwiper(
              controller: controller,
              cardsCount: currentDestinations.length,
              onSwipe: _onSwipe,
              onUndo: (int? previousIndex, int currentIndex,
                  CardSwiperDirection direction) {
                // Handle undo if needed
                return true;
              },
              numberOfCardsDisplayed: 2,
              backCardOffset: const Offset(0, 40),
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 40),
              cardBuilder: (context, index, horizontalThresholdPercentage,
                  verticalThresholdPercentage) {
                return _buildDestinationCard(
                  currentDestinations[index],
                  horizontalThresholdPercentage.toDouble(),
                  verticalThresholdPercentage.toDouble(),
                );
              },
            ),
          ),
        ),

        // Action buttons
        _buildActionButtons(),
        const SizedBox(height: 20),
      ],
    );
  }

  Widget _buildTabSelector() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: const Color(0xFF0D76FF),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                'For You',
                textAlign: TextAlign.center,
                style: GoogleFonts.instrumentSans(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const LikesScreen(),
                  ),
                );
              },
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                child: Text(
                  'Liked',
                  textAlign: TextAlign.center,
                  style: GoogleFonts.instrumentSans(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF718096),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDestinationCard(
    Map<String, dynamic> destination,
    double horizontalThresholdPercentage,
    double verticalThresholdPercentage,
  ) {
    // Calculate swipe direction for corner indicators
    // Invert the threshold logic due to 180-degree rotation
    bool showLikeIndicator = horizontalThresholdPercentage < -0.1;
    bool showPassIndicator = horizontalThresholdPercentage > 0.1;

    return Transform.rotate(
        angle: math.pi, // Counter-rotate the card content by 180 degrees
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(20),
            child: Stack(
              children: [
                // Background image
                OptimizedNetworkImage(
                  imageUrl: destination['image'] ?? '',
                  width: double.infinity,
                  height: double.infinity,
                  fit: BoxFit.cover,
                  borderRadius: BorderRadius.circular(20),
                  enableFadeIn: true,
                  fadeInDuration: const Duration(milliseconds: 300),
                  cacheWidth: 800,
                  cacheHeight: 1000,
                ),

                // Gradient overlay
                Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withOpacity(0.7),
                      ],
                    ),
                  ),
                ),

                // LIKE indicator (top-left corner)
                if (showLikeIndicator)
                  Positioned(
                    top: 16,
                    left: 16,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 8),
                      decoration: BoxDecoration(
                        color: Colors.green.withOpacity(0.9),
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.2),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            Icons.favorite,
                            color: Colors.white,
                            size: 20,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            'LIKE',
                            style: GoogleFonts.instrumentSans(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                // PASS indicator (top-right corner)
                if (showPassIndicator)
                  Positioned(
                    top: 16,
                    right: 16,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 8),
                      decoration: BoxDecoration(
                        color: Colors.red.withOpacity(0.9),
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.2),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            Icons.close,
                            color: Colors.white,
                            size: 20,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            'PASS',
                            style: GoogleFonts.instrumentSans(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                // Content
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                destination['name'],
                                style: GoogleFonts.instrumentSans(
                                  fontSize: 28,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 6,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.2),
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  const Icon(
                                    Icons.star,
                                    color: Colors.amber,
                                    size: 16,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    destination['rating'].toString(),
                                    style: GoogleFonts.instrumentSans(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w600,
                                      color: Colors.white,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            const Icon(
                              Icons.location_on,
                              color: Colors.white70,
                              size: 16,
                            ),
                            const SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                destination['location'],
                                style: GoogleFonts.instrumentSans(
                                  fontSize: 16,
                                  color: Colors.white70,
                                ),
                              ),
                            ),
                            Text(
                              '${destination['distance']} km',
                              style: GoogleFonts.instrumentSans(
                                fontSize: 14,
                                color: Colors.white70,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        Text(
                          destination['description'],
                          style: GoogleFonts.instrumentSans(
                            fontSize: 14,
                            color: Colors.white70,
                            height: 1.4,
                          ),
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 16),
                        Wrap(
                          spacing: 8,
                          runSpacing: 8,
                          children: (destination['tags'] as List<String>)
                              .map((tag) => Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 12,
                                      vertical: 6,
                                    ),
                                    decoration: BoxDecoration(
                                      color: const Color(0xFF0D76FF)
                                          .withOpacity(0.8),
                                      borderRadius: BorderRadius.circular(16),
                                    ),
                                    child: Text(
                                      tag,
                                      style: GoogleFonts.instrumentSans(
                                        fontSize: 12,
                                        fontWeight: FontWeight.w600,
                                        color: Colors.white,
                                      ),
                                    ),
                                  ))
                              .toList(),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ));
  }

  Widget _buildActionButtons() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 40),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // Pass button
          _buildActionButton(
            icon: Icons.close,
            color: Colors.red,
            iconColor: Colors.white,
            onTap: () => controller.swipe(CardSwiperDirection.right),
            size: 55,
          ),

          // Add to Itinerary button
          _buildActionButton(
            icon: Icons.add,
            color: Colors.white,
            iconColor: const Color(0xFF0D76FF),
            onTap: () {
              if (widget.onAddToItinerary != null &&
                  currentDestinations.isNotEmpty) {
                // Get the current destination based on the current card index
                final currentDestination =
                    currentDestinations.length > _currentCardIndex
                        ? currentDestinations[_currentCardIndex]
                        : currentDestinations.first;

                if (kDebugMode) {
                  print(
                      'FlutterCardSwiperWidget: Add to itinerary button tapped for: ${currentDestination['name']} (index: $_currentCardIndex)');
                }

                widget.onAddToItinerary!(currentDestination);
              }
            },
            size: 70,
          ),

          // Like button
          _buildActionButton(
            icon: Icons.check,
            color: Colors.green,
            iconColor: Colors.white,
            onTap: () => controller.swipe(CardSwiperDirection.left),
            size: 55,
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required Color color,
    required Color iconColor,
    required VoidCallback onTap,
    required double size,
  }) {
    return GestureDetector(
      onTap: currentDestinations.isNotEmpty ? onTap : null,
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: color,
          shape: BoxShape.circle,
          border: Border.all(
            color: color,
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Icon(
          icon,
          color: iconColor,
          size: size * 0.4,
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Column(
      children: [
        _buildTabSelector(),
        Expanded(
          child: Center(
            child: Container(
              margin: const EdgeInsets.all(40),
              padding: const EdgeInsets.all(40),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.favorite_outline,
                    size: 80,
                    color: const Color(0xFF0D76FF).withOpacity(0.3),
                  ),
                  const SizedBox(height: 24),
                  Text(
                    'No More Destinations',
                    style: GoogleFonts.instrumentSans(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF2D3748),
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'You\'ve seen all available destinations. Check back later for more travel inspiration!',
                    style: GoogleFonts.instrumentSans(
                      fontSize: 16,
                      color: const Color(0xFF718096),
                      height: 1.4,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
