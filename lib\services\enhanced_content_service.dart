import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/ai_itinerary_parser.dart';
import '../models/chat_history_models.dart';

/// Service to manage enhanced content storage and retrieval for the Plan tab
class EnhancedContentService {
  static const String _enhancedContentKey = 'enhanced_content_list';
  static const String _lastUpdateKey = 'enhanced_content_last_update';

  /// Store enhanced content parsed from AI responses
  static Future<void> storeEnhancedContent(
    ParsedContent content, {
    String? chatSessionId,
    String? messageId,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Get existing content list
      final existingContent = await getAllEnhancedContent();

      // Create enhanced content item
      final contentItem = EnhancedContentItem(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        content: content,
        createdAt: DateTime.now(),
        chatSessionId: chatSessionId,
        messageId: messageId,
      );

      // Add to list (newest first)
      existingContent.insert(0, contentItem);

      // Keep only the latest 50 items to prevent storage bloat
      if (existingContent.length > 50) {
        existingContent.removeRange(50, existingContent.length);
      }

      // Store updated list
      final jsonList = existingContent.map((item) => item.toJson()).toList();
      await prefs.setString(_enhancedContentKey, jsonEncode(jsonList));
      await prefs.setString(_lastUpdateKey, DateTime.now().toIso8601String());

      if (kDebugMode) {
        print(
            'Enhanced Content Service: Stored content "${content.title}" (${content.type})');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error storing enhanced content: $e');
      }
    }
  }

  /// Retrieve all enhanced content for the Plan tab
  static Future<List<EnhancedContentItem>> getAllEnhancedContent() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_enhancedContentKey);

      if (jsonString == null) {
        return [];
      }

      final jsonList = jsonDecode(jsonString) as List;
      return jsonList
          .map((json) => EnhancedContentItem.fromJson(json))
          .toList();
    } catch (e) {
      if (kDebugMode) {
        print('Error retrieving enhanced content: $e');
      }
      return [];
    }
  }

  /// Get enhanced content by type
  static Future<List<EnhancedContentItem>> getContentByType(
      ContentType type) async {
    final allContent = await getAllEnhancedContent();
    return allContent.where((item) => item.content.type == type).toList();
  }

  /// Get recent enhanced content (last 7 days)
  static Future<List<EnhancedContentItem>> getRecentContent() async {
    final allContent = await getAllEnhancedContent();
    final sevenDaysAgo = DateTime.now().subtract(const Duration(days: 7));

    return allContent
        .where((item) => item.createdAt.isAfter(sevenDaysAgo))
        .toList();
  }

  /// Delete enhanced content by ID
  static Future<void> deleteEnhancedContent(String id) async {
    try {
      final allContent = await getAllEnhancedContent();
      allContent.removeWhere((item) => item.id == id);

      final prefs = await SharedPreferences.getInstance();
      final jsonList = allContent.map((item) => item.toJson()).toList();
      await prefs.setString(_enhancedContentKey, jsonEncode(jsonList));

      if (kDebugMode) {
        print('Enhanced Content Service: Deleted content with ID $id');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting enhanced content: $e');
      }
    }
  }

  /// Clear all enhanced content
  static Future<void> clearAllContent() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_enhancedContentKey);
      await prefs.remove(_lastUpdateKey);

      if (kDebugMode) {
        print('Enhanced Content Service: Cleared all content');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error clearing enhanced content: $e');
      }
    }
  }

  /// Process AI message and extract enhanced content
  static Future<void> processAIMessage(
    String aiResponse, {
    String? chatSessionId,
    String? messageId,
  }) async {
    try {
      if (kDebugMode) {
        print(
            'Enhanced Content Service: Processing AI response of ${aiResponse.length} characters');
      }

      final parsedContent =
          AIItineraryParser.parseStructuredContent(aiResponse);

      if (kDebugMode) {
        print(
            'Enhanced Content Service: Parsed content type: ${parsedContent.type}');
      }

      // Only store if it's not plain content or if it's a substantial response
      if (parsedContent.type != ContentType.plain || aiResponse.length > 500) {
        await storeEnhancedContent(
          parsedContent,
          chatSessionId: chatSessionId,
          messageId: messageId,
        );

        if (kDebugMode) {
          print(
              'Enhanced Content Service: Stored content of type ${parsedContent.type} with title "${parsedContent.title}"');
        }
      } else {
        if (kDebugMode) {
          print(
              'Enhanced Content Service: Skipped storing plain content (${aiResponse.length} chars)');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error processing AI message for enhanced content: $e');
      }
    }
  }

  /// Get content statistics
  static Future<Map<String, int>> getContentStatistics() async {
    final allContent = await getAllEnhancedContent();
    final stats = <String, int>{};

    for (final type in ContentType.values) {
      stats[type.toString()] =
          allContent.where((item) => item.content.type == type).length;
    }

    return stats;
  }

  /// Search enhanced content by title or content
  static Future<List<EnhancedContentItem>> searchContent(String query) async {
    if (query.trim().isEmpty) {
      return getAllEnhancedContent();
    }

    final allContent = await getAllEnhancedContent();
    final lowerQuery = query.toLowerCase();

    return allContent.where((item) {
      return item.content.title.toLowerCase().contains(lowerQuery) ||
          item.content.rawContent.toLowerCase().contains(lowerQuery);
    }).toList();
  }
}

/// Represents an enhanced content item stored in the Plan tab
class EnhancedContentItem {
  final String id;
  final ParsedContent content;
  final DateTime createdAt;
  final String? chatSessionId;
  final String? messageId;

  EnhancedContentItem({
    required this.id,
    required this.content,
    required this.createdAt,
    this.chatSessionId,
    this.messageId,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': _contentToJson(content),
      'createdAt': createdAt.toIso8601String(),
      'chatSessionId': chatSessionId,
      'messageId': messageId,
    };
  }

  factory EnhancedContentItem.fromJson(Map<String, dynamic> json) {
    return EnhancedContentItem(
      id: json['id'],
      content: _contentFromJson(json['content']),
      createdAt: DateTime.parse(json['createdAt']),
      chatSessionId: json['chatSessionId'],
      messageId: json['messageId'],
    );
  }

  static Map<String, dynamic> _contentToJson(ParsedContent content) {
    return {
      'type': content.type.toString(),
      'title': content.title,
      'rawContent': content.rawContent,
      'itinerary': content.itinerary != null
          ? _itineraryToJson(content.itinerary!)
          : null,
      'tables': content.tables?.map((table) => _tableToJson(table)).toList(),
      'lists': content.lists,
      'metadata': content.metadata,
    };
  }

  static ParsedContent _contentFromJson(Map<String, dynamic> json) {
    return ParsedContent(
      type: ContentType.values.firstWhere((e) => e.toString() == json['type']),
      title: json['title'],
      rawContent: json['rawContent'],
      itinerary: json['itinerary'] != null
          ? _itineraryFromJson(json['itinerary'])
          : null,
      tables: json['tables']
          ?.map<ParsedTable>((table) => _tableFromJson(table))
          .toList(),
      lists: json['lists']?.cast<String>(),
      metadata: json['metadata']?.cast<String, dynamic>(),
    );
  }

  static Map<String, dynamic> _itineraryToJson(ParsedItinerary itinerary) {
    return {
      'title': itinerary.title,
      'destinations': itinerary.destinations,
      'activities': itinerary.activities,
      'daySpecificActivities': itinerary.daySpecificActivities?.map(
        (key, value) => MapEntry(key.toString(), value),
      ),
      'startDate': itinerary.startDate,
      'endDate': itinerary.endDate,
      'rawResponse': itinerary.rawResponse,
    };
  }

  static ParsedItinerary _itineraryFromJson(Map<String, dynamic> json) {
    return ParsedItinerary(
      title: json['title'],
      destinations: json['destinations']?.cast<String>() ?? [],
      activities: json['activities']?.cast<String, List<String>>() ?? {},
      daySpecificActivities:
          json['daySpecificActivities']?.map<int, Map<String, List<String>>>(
        (key, value) =>
            MapEntry(int.parse(key), value.cast<String, List<String>>()),
      ),
      startDate: json['startDate'],
      endDate: json['endDate'],
      rawResponse: json['rawResponse'],
    );
  }

  static Map<String, dynamic> _tableToJson(ParsedTable table) {
    return {
      'headers': table.headers,
      'rows': table.rows,
      'title': table.title,
      'rawContent': table.rawContent,
    };
  }

  static ParsedTable _tableFromJson(Map<String, dynamic> json) {
    return ParsedTable(
      headers: json['headers']?.cast<String>() ?? [],
      rows: json['rows']
              ?.map<List<String>>((row) => row.cast<String>())
              .toList() ??
          [],
      title: json['title'],
      rawContent: json['rawContent'],
    );
  }
}
