import 'dart:math';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class SimpleCaptcha extends StatefulWidget {
  final Function(bool) onCaptchaVerified;
  
  const SimpleCaptcha({
    super.key,
    required this.onCaptchaVerified,
  });

  @override
  State<SimpleCaptcha> createState() => _SimpleCaptchaState();
}

class _SimpleCaptchaState extends State<SimpleCaptcha> {
  late int _num1;
  late int _num2;
  late int _correctAnswer;
  final TextEditingController _answerController = TextEditingController();
  bool _isVerified = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _generateCaptcha();
  }

  @override
  void dispose() {
    _answerController.dispose();
    super.dispose();
  }

  void _generateCaptcha() {
    final random = Random();
    _num1 = random.nextInt(10) + 1; // 1-10
    _num2 = random.nextInt(10) + 1; // 1-10
    _correctAnswer = _num1 + _num2;
    _answerController.clear();
    _isVerified = false;
    _errorMessage = null;
    setState(() {});
  }

  void _verifyCaptcha() {
    final userAnswer = int.tryParse(_answerController.text);
    
    if (userAnswer == null) {
      setState(() {
        _errorMessage = 'Please enter a valid number';
        _isVerified = false;
      });
      widget.onCaptchaVerified(false);
      return;
    }

    if (userAnswer == _correctAnswer) {
      setState(() {
        _isVerified = true;
        _errorMessage = null;
      });
      widget.onCaptchaVerified(true);
    } else {
      setState(() {
        _errorMessage = 'Incorrect answer. Please try again.';
        _isVerified = false;
      });
      widget.onCaptchaVerified(false);
      _generateCaptcha(); // Generate new captcha on wrong answer
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFF7F9FC),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _isVerified 
              ? Colors.green 
              : _errorMessage != null 
                  ? Colors.red 
                  : const Color(0xFFE2E8F0),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Captcha title
          Row(
            children: [
              Icon(
                _isVerified ? Icons.check_circle : Icons.security,
                color: _isVerified ? Colors.green : const Color(0xFF0D76FF),
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Verify you\'re not a robot',
                style: GoogleFonts.instrumentSans(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF2D3748),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Math problem
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: const Color(0xFFE2E8F0)),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  '$_num1 + $_num2 = ?',
                  style: GoogleFonts.instrumentSans(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF2D3748),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),

          // Answer input
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _answerController,
                  keyboardType: TextInputType.number,
                  enabled: !_isVerified,
                  decoration: InputDecoration(
                    hintText: 'Enter answer',
                    hintStyle: GoogleFonts.instrumentSans(
                      color: const Color(0xFFA0AEC0),
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(color: Color(0xFFE2E8F0)),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(color: Color(0xFFE2E8F0)),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(color: Color(0xFF0D76FF)),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 12,
                    ),
                    suffixIcon: _isVerified
                        ? const Icon(Icons.check, color: Colors.green)
                        : null,
                  ),
                  onSubmitted: (_) => _verifyCaptcha(),
                ),
              ),
              const SizedBox(width: 8),
              ElevatedButton(
                onPressed: _isVerified ? null : _verifyCaptcha,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF0D76FF),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  'Verify',
                  style: GoogleFonts.instrumentSans(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),

          // Error message or success message
          if (_errorMessage != null || _isVerified) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  _isVerified ? Icons.check_circle : Icons.error,
                  color: _isVerified ? Colors.green : Colors.red,
                  size: 16,
                ),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    _isVerified ? 'Verification successful!' : _errorMessage!,
                    style: GoogleFonts.instrumentSans(
                      fontSize: 12,
                      color: _isVerified ? Colors.green : Colors.red,
                    ),
                  ),
                ),
              ],
            ),
          ],

          // Refresh button
          if (!_isVerified) ...[
            const SizedBox(height: 8),
            TextButton.icon(
              onPressed: _generateCaptcha,
              icon: const Icon(Icons.refresh, size: 16),
              label: Text(
                'Generate new captcha',
                style: GoogleFonts.instrumentSans(fontSize: 12),
              ),
              style: TextButton.styleFrom(
                foregroundColor: const Color(0xFF0D76FF),
                padding: EdgeInsets.zero,
              ),
            ),
          ],
        ],
      ),
    );
  }
}
