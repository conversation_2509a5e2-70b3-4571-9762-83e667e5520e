import 'dart:async';
import 'dart:convert';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/feedback_models.dart';
import '../models/learning_models.dart';
import '../services/feedback_service.dart';
import '../services/adaptive_prompt_service.dart';

/// Service for automated AI learning based on user feedback
class AILearningService {
  static const String _learningDataKey = 'ai_learning_data';
  static const String _lastAnalysisKey = 'last_feedback_analysis';
  static const String _learningConfigKey = 'ai_learning_config';
  static const String _promptVersionKey = 'current_prompt_version';

  // Learning configuration
  static const Duration _defaultAnalysisInterval = Duration(hours: 6);
  static const int _minFeedbackForLearning = 10;
  static const double _negativeThreshold =
      0.3; // 30% negative feedback triggers learning
  static const int _maxPromptVersions = 5;

  // Singleton pattern
  static AILearningService? _instance;
  static AILearningService get instance => _instance ??= AILearningService._();
  AILearningService._();

  // Internal state
  bool _isInitialized = false;
  Timer? _analysisTimer;
  LearningConfiguration _config = LearningConfiguration.defaultConfig();
  LearningData _learningData = LearningData.empty();

  /// Initialize the learning service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadConfiguration();
      await _loadLearningData();
      _startPeriodicAnalysis();
      _isInitialized = true;

      if (kDebugMode) {
        print('AI Learning Service: Initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('AI Learning Service: Initialization failed - $e');
      }
    }
  }

  /// Dispose resources
  void dispose() {
    _analysisTimer?.cancel();
    _isInitialized = false;
  }

  /// Start periodic feedback analysis
  void _startPeriodicAnalysis() {
    _analysisTimer?.cancel();
    _analysisTimer = Timer.periodic(_config.analysisInterval, (timer) {
      _performFeedbackAnalysis();
    });
  }

  /// Load learning configuration from storage
  Future<void> _loadConfiguration() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final configJson = prefs.getString(_learningConfigKey);

      if (configJson != null) {
        final configMap = json.decode(configJson);
        _config = LearningConfiguration.fromMap(configMap);
      }
    } catch (e) {
      if (kDebugMode) {
        print('AI Learning Service: Failed to load configuration - $e');
      }
      _config = LearningConfiguration.defaultConfig();
    }
  }

  /// Save learning configuration to storage
  Future<void> _saveConfiguration() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_learningConfigKey, json.encode(_config.toMap()));
    } catch (e) {
      if (kDebugMode) {
        print('AI Learning Service: Failed to save configuration - $e');
      }
    }
  }

  /// Load learning data from storage
  Future<void> _loadLearningData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final dataJson = prefs.getString(_learningDataKey);

      if (dataJson != null) {
        final dataMap = json.decode(dataJson);
        _learningData = LearningData.fromMap(dataMap);
      }
    } catch (e) {
      if (kDebugMode) {
        print('AI Learning Service: Failed to load learning data - $e');
      }
      _learningData = LearningData.empty();
    }
  }

  /// Save learning data to storage
  Future<void> _saveLearningData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
          _learningDataKey, json.encode(_learningData.toMap()));
    } catch (e) {
      if (kDebugMode) {
        print('AI Learning Service: Failed to save learning data - $e');
      }
    }
  }

  /// Perform comprehensive feedback analysis
  Future<void> _performFeedbackAnalysis() async {
    if (!_config.isLearningEnabled) return;

    try {
      if (kDebugMode) {
        print('AI Learning Service: Starting feedback analysis...');
      }

      // Fetch latest feedback from Google Sheets
      final feedbackList = await FeedbackService.readFeedbackFromSheets();

      if (feedbackList.length < _minFeedbackForLearning) {
        if (kDebugMode) {
          print(
              'AI Learning Service: Insufficient feedback data (${feedbackList.length} < $_minFeedbackForLearning)');
        }
        return;
      }

      // Analyze feedback patterns
      final analysis = await _analyzeFeedbackPatterns(feedbackList);

      // Update learning data
      _learningData.addAnalysis(analysis);
      await _saveLearningData();

      // Check if prompt optimization is needed
      if (_shouldOptimizePrompt(analysis)) {
        await _optimizeSystemPrompt(analysis);
      }

      // Update last analysis timestamp
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(
          _lastAnalysisKey, DateTime.now().millisecondsSinceEpoch);

      if (kDebugMode) {
        print('AI Learning Service: Analysis completed successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('AI Learning Service: Analysis failed - $e');
      }
    }
  }

  /// Analyze feedback patterns to identify improvement opportunities
  Future<FeedbackAnalysis> _analyzeFeedbackPatterns(
      List<AIResponseFeedback> feedbackList) async {
    final analysis = FeedbackAnalysis();

    // Separate positive and negative feedback
    final positiveFeedback =
        feedbackList.where((f) => f.feedbackType == FeedbackType.like).toList();
    final negativeFeedback = feedbackList
        .where((f) => f.feedbackType == FeedbackType.dislike)
        .toList();

    analysis.totalFeedback = feedbackList.length;
    analysis.positiveFeedback = positiveFeedback.length;
    analysis.negativeFeedback = negativeFeedback.length;
    analysis.positiveRatio = positiveFeedback.length / feedbackList.length;

    // Analyze negative feedback patterns
    analysis.commonFailures = _identifyCommonFailures(negativeFeedback);
    analysis.negativeReasons = _analyzeNegativeReasons(negativeFeedback);

    // Analyze positive feedback patterns
    analysis.successPatterns = _identifySuccessPatterns(positiveFeedback);

    // Analyze query types and contexts
    analysis.queryTypeAnalysis = _analyzeQueryTypes(feedbackList);
    analysis.contextualPatterns = _analyzeContextualPatterns(feedbackList);

    // Calculate quality scores
    analysis.overallQualityScore = _calculateQualityScore(feedbackList);
    analysis.travelSpecificScore = _calculateTravelSpecificScore(feedbackList);

    return analysis;
  }

  /// Get current learning statistics
  LearningStatistics getCurrentStatistics() {
    return LearningStatistics(
      totalAnalyses: _learningData.analyses.length,
      lastAnalysisTime: _learningData.lastAnalysisTime,
      currentPromptVersion: _learningData.currentPromptVersion,
      averageQualityScore: _learningData.getAverageQualityScore(),
      learningEnabled: _config.isLearningEnabled,
    );
  }

  /// Update learning configuration
  Future<void> updateConfiguration(LearningConfiguration newConfig) async {
    _config = newConfig;
    await _saveConfiguration();

    // Restart analysis timer with new interval
    if (_config.isLearningEnabled) {
      _startPeriodicAnalysis();
    } else {
      _analysisTimer?.cancel();
    }
  }

  /// Force immediate feedback analysis
  Future<void> forceAnalysis() async {
    await _performFeedbackAnalysis();
  }

  /// Get learning configuration
  LearningConfiguration getConfiguration() => _config;

  /// Get learning data
  LearningData getLearningData() => _learningData;

  /// Identify common failure patterns in negative feedback
  List<String> _identifyCommonFailures(
      List<AIResponseFeedback> negativeFeedback) {
    final failurePatterns = <String>[];
    final reasonCounts = <FeedbackReason, int>{};

    // Count feedback reasons
    for (final feedback in negativeFeedback) {
      if (feedback.feedbackReason != null) {
        reasonCounts[feedback.feedbackReason!] =
            (reasonCounts[feedback.feedbackReason!] ?? 0) + 1;
      }
    }

    // Identify patterns based on frequency
    final totalNegative = negativeFeedback.length;
    for (final entry in reasonCounts.entries) {
      final percentage = entry.value / totalNegative;
      if (percentage > 0.2) {
        // 20% threshold
        failurePatterns.add(
            '${entry.key.name}: ${(percentage * 100).toStringAsFixed(1)}%');
      }
    }

    // Analyze custom feedback text for patterns
    final customTexts = negativeFeedback
        .where((f) =>
            f.customFeedbackText != null && f.customFeedbackText!.isNotEmpty)
        .map((f) => f.customFeedbackText!.toLowerCase())
        .toList();

    final commonWords = _extractCommonWords(customTexts);
    failurePatterns
        .addAll(commonWords.take(5).map((word) => 'Common issue: $word'));

    return failurePatterns;
  }

  /// Analyze negative feedback reasons distribution
  Map<String, double> _analyzeNegativeReasons(
      List<AIResponseFeedback> negativeFeedback) {
    final reasonDistribution = <String, double>{};
    final reasonCounts = <FeedbackReason, int>{};

    for (final feedback in negativeFeedback) {
      if (feedback.feedbackReason != null) {
        reasonCounts[feedback.feedbackReason!] =
            (reasonCounts[feedback.feedbackReason!] ?? 0) + 1;
      }
    }

    final total = negativeFeedback.length;
    for (final entry in reasonCounts.entries) {
      reasonDistribution[entry.key.name] = entry.value / total;
    }

    return reasonDistribution;
  }

  /// Identify success patterns in positive feedback
  List<String> _identifySuccessPatterns(
      List<AIResponseFeedback> positiveFeedback) {
    final successPatterns = <String>[];

    // Analyze response characteristics of liked messages
    final responseLengths =
        positiveFeedback.map((f) => f.aiResponse.length).toList();
    final avgLength = responseLengths.fold(0, (sum, length) => sum + length) /
        responseLengths.length;

    successPatterns.add(
        'Optimal response length: ${avgLength.toStringAsFixed(0)} characters');

    // Analyze response content patterns
    final responseTexts =
        positiveFeedback.map((f) => f.aiResponse.toLowerCase()).toList();
    final commonSuccessWords = _extractCommonWords(responseTexts);

    successPatterns.addAll(
        commonSuccessWords.take(5).map((word) => 'Successful element: $word'));

    // Analyze query types that get positive feedback
    final queryTypes =
        _categorizeQueries(positiveFeedback.map((f) => f.userMessage).toList());
    for (final entry in queryTypes.entries) {
      if (entry.value > positiveFeedback.length * 0.15) {
        // 15% threshold
        successPatterns.add('Strong performance in: ${entry.key}');
      }
    }

    return successPatterns;
  }

  /// Analyze query types distribution
  Map<String, int> _analyzeQueryTypes(List<AIResponseFeedback> feedbackList) {
    final queries = feedbackList.map((f) => f.userMessage).toList();
    return _categorizeQueries(queries);
  }

  /// Categorize queries into types
  Map<String, int> _categorizeQueries(List<String> queries) {
    final categories = <String, int>{
      'destination_planning': 0,
      'itinerary_help': 0,
      'travel_logistics': 0,
      'accommodation': 0,
      'activities': 0,
      'budget_planning': 0,
      'general_travel': 0,
    };

    for (final query in queries) {
      final lowerQuery = query.toLowerCase();

      if (_containsAny(lowerQuery,
          ['where to go', 'destination', 'places to visit', 'recommend'])) {
        categories['destination_planning'] =
            categories['destination_planning']! + 1;
      } else if (_containsAny(
          lowerQuery, ['itinerary', 'schedule', 'plan', 'day by day'])) {
        categories['itinerary_help'] = categories['itinerary_help']! + 1;
      } else if (_containsAny(
          lowerQuery, ['flight', 'transport', 'visa', 'passport', 'booking'])) {
        categories['travel_logistics'] = categories['travel_logistics']! + 1;
      } else if (_containsAny(
          lowerQuery, ['hotel', 'accommodation', 'stay', 'lodging'])) {
        categories['accommodation'] = categories['accommodation']! + 1;
      } else if (_containsAny(lowerQuery,
          ['activities', 'things to do', 'attractions', 'sightseeing'])) {
        categories['activities'] = categories['activities']! + 1;
      } else if (_containsAny(
          lowerQuery, ['budget', 'cost', 'price', 'expensive', 'cheap'])) {
        categories['budget_planning'] = categories['budget_planning']! + 1;
      } else {
        categories['general_travel'] = categories['general_travel']! + 1;
      }
    }

    return categories;
  }

  /// Analyze contextual patterns in feedback
  Map<String, dynamic> _analyzeContextualPatterns(
      List<AIResponseFeedback> feedbackList) {
    final patterns = <String, dynamic>{};

    // Analyze conversation length patterns
    final conversationLengths =
        feedbackList.map((f) => f.conversationContext.length).toList();
    patterns['avg_conversation_length'] =
        conversationLengths.fold(0, (sum, length) => sum + length) /
            conversationLengths.length;

    // Analyze time-based patterns
    final hourCounts = <int, int>{};
    for (final feedback in feedbackList) {
      final hour = feedback.timestamp.hour;
      hourCounts[hour] = (hourCounts[hour] ?? 0) + 1;
    }
    patterns['peak_usage_hours'] = hourCounts.entries
        .where((entry) => entry.value > feedbackList.length * 0.05)
        .map((entry) => entry.key)
        .toList();

    return patterns;
  }

  /// Calculate overall quality score
  double _calculateQualityScore(List<AIResponseFeedback> feedbackList) {
    if (feedbackList.isEmpty) return 0.0;

    final positiveCount =
        feedbackList.where((f) => f.feedbackType == FeedbackType.like).length;
    return positiveCount / feedbackList.length;
  }

  /// Calculate travel-specific quality score
  double _calculateTravelSpecificScore(List<AIResponseFeedback> feedbackList) {
    final travelQueries =
        feedbackList.where((f) => _isTravelQuery(f.userMessage)).toList();
    if (travelQueries.isEmpty) return 0.0;

    final positiveCount =
        travelQueries.where((f) => f.feedbackType == FeedbackType.like).length;
    return positiveCount / travelQueries.length;
  }

  /// Check if a query is travel-related
  bool _isTravelQuery(String query) {
    final lowerQuery = query.toLowerCase();
    final travelKeywords = [
      'travel',
      'trip',
      'vacation',
      'holiday',
      'destination',
      'visit',
      'flight',
      'hotel',
      'itinerary',
      'places',
      'attractions',
      'tourism'
    ];

    return travelKeywords.any((keyword) => lowerQuery.contains(keyword));
  }

  /// Extract common words from text list
  List<String> _extractCommonWords(List<String> texts) {
    final wordCounts = <String, int>{};
    final stopWords = {
      'the',
      'a',
      'an',
      'and',
      'or',
      'but',
      'in',
      'on',
      'at',
      'to',
      'for',
      'of',
      'with',
      'by',
      'is',
      'was',
      'are',
      'were',
      'be',
      'been',
      'have',
      'has',
      'had',
      'do',
      'does',
      'did',
      'will',
      'would',
      'could',
      'should',
      'may',
      'might',
      'can',
      'this',
      'that',
      'these',
      'those',
      'i',
      'you',
      'he',
      'she',
      'it',
      'we',
      'they',
      'me',
      'him',
      'her',
      'us',
      'them'
    };

    for (final text in texts) {
      final words = text
          .split(RegExp(r'\W+'))
          .where((word) =>
              word.length > 2 && !stopWords.contains(word.toLowerCase()))
          .map((word) => word.toLowerCase());

      for (final word in words) {
        wordCounts[word] = (wordCounts[word] ?? 0) + 1;
      }
    }

    final sortedWords = wordCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return sortedWords.map((entry) => entry.key).toList();
  }

  /// Check if text contains any of the given keywords
  bool _containsAny(String text, List<String> keywords) {
    return keywords.any((keyword) => text.contains(keyword));
  }

  /// Check if prompt optimization is needed
  bool _shouldOptimizePrompt(FeedbackAnalysis analysis) {
    return analysis.positiveRatio < (1.0 - _negativeThreshold) &&
        analysis.totalFeedback >= _minFeedbackForLearning;
  }

  /// Optimize system prompt based on analysis
  Future<void> _optimizeSystemPrompt(FeedbackAnalysis analysis) async {
    try {
      if (kDebugMode) {
        print('AI Learning Service: Starting prompt optimization...');
        print('Quality score: ${analysis.overallQualityScore}');
        print('Common failures: ${analysis.commonFailures}');
      }

      // Initialize adaptive prompt service
      await AdaptivePromptService.instance.initialize();

      // Create optimized prompt based on analysis
      final optimizedPrompt =
          await AdaptivePromptService.instance.createOptimizedPrompt(analysis);

      // Get the new prompt version
      final allVersions = AdaptivePromptService.instance.getAllPromptVersions();
      final latestVersion = allVersions.isNotEmpty
          ? allVersions.reduce((a, b) => a.version > b.version ? a : b)
          : null;

      if (latestVersion != null) {
        // Activate the new optimized prompt
        await AdaptivePromptService.instance
            .activatePromptVersion(latestVersion.version);

        // Update learning data with optimization info
        _learningData.performanceMetrics['last_optimization'] = {
          'timestamp': DateTime.now().millisecondsSinceEpoch,
          'prompt_version': latestVersion.version,
          'trigger_quality_score': analysis.overallQualityScore,
          'optimization_reason': 'automatic_feedback_analysis',
        };

        await _saveLearningData();

        if (kDebugMode) {
          print(
              'AI Learning Service: Prompt optimized to version ${latestVersion.version}');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('AI Learning Service: Prompt optimization failed - $e');
      }
    }
  }
}
