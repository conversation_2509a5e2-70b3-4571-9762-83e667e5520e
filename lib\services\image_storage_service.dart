import 'dart:io';
import 'dart:typed_data';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

class ImageStorageService {
  static const String _imagesFolder = 'itinerary_images';

  /// Get the directory where itinerary images are stored
  static Future<Directory> _getImagesDirectory() async {
    final appDir = await getApplicationDocumentsDirectory();
    final imagesDir = Directory(path.join(appDir.path, _imagesFolder));
    
    // Create directory if it doesn't exist
    if (!await imagesDir.exists()) {
      await imagesDir.create(recursive: true);
    }
    
    return imagesDir;
  }

  /// Save an image file to persistent storage and return the new path
  static Future<String?> saveImage(String sourcePath, String itineraryId) async {
    try {
      final sourceFile = File(sourcePath);
      
      // Check if source file exists
      if (!await sourceFile.exists()) {
        print('Source image file does not exist: $sourcePath');
        return null;
      }

      // Get the images directory
      final imagesDir = await _getImagesDirectory();
      
      // Generate a unique filename using itinerary ID and timestamp
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final extension = path.extension(sourcePath);
      final fileName = '${itineraryId}_$timestamp$extension';
      final destinationPath = path.join(imagesDir.path, fileName);
      
      // Copy the file to persistent storage
      final destinationFile = await sourceFile.copy(destinationPath);
      
      print('Image saved successfully: ${destinationFile.path}');
      return destinationFile.path;
    } catch (e) {
      print('Error saving image: $e');
      return null;
    }
  }

  /// Save image from bytes (useful for camera captures)
  static Future<String?> saveImageFromBytes(Uint8List bytes, String itineraryId) async {
    try {
      // Get the images directory
      final imagesDir = await _getImagesDirectory();
      
      // Generate a unique filename
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = '${itineraryId}_$timestamp.jpg';
      final destinationPath = path.join(imagesDir.path, fileName);
      
      // Write bytes to file
      final file = File(destinationPath);
      await file.writeAsBytes(bytes);
      
      print('Image saved from bytes: $destinationPath');
      return destinationPath;
    } catch (e) {
      print('Error saving image from bytes: $e');
      return null;
    }
  }

  /// Delete an image file
  static Future<bool> deleteImage(String imagePath) async {
    try {
      final file = File(imagePath);
      if (await file.exists()) {
        await file.delete();
        print('Image deleted: $imagePath');
        return true;
      }
      return false;
    } catch (e) {
      print('Error deleting image: $e');
      return false;
    }
  }

  /// Check if an image file exists
  static Future<bool> imageExists(String imagePath) async {
    try {
      final file = File(imagePath);
      return await file.exists();
    } catch (e) {
      print('Error checking image existence: $e');
      return false;
    }
  }

  /// Clean up orphaned images (images that don't belong to any existing itinerary)
  static Future<void> cleanupOrphanedImages(List<String> validItineraryIds) async {
    try {
      final imagesDir = await _getImagesDirectory();
      
      if (!await imagesDir.exists()) return;
      
      final files = await imagesDir.list().toList();
      
      for (final file in files) {
        if (file is File) {
          final fileName = path.basename(file.path);
          
          // Extract itinerary ID from filename (format: itineraryId_timestamp.ext)
          final parts = fileName.split('_');
          if (parts.length >= 2) {
            final itineraryId = parts[0];
            
            // If this itinerary ID is not in the valid list, delete the image
            if (!validItineraryIds.contains(itineraryId)) {
              await file.delete();
              print('Cleaned up orphaned image: ${file.path}');
            }
          }
        }
      }
    } catch (e) {
      print('Error cleaning up orphaned images: $e');
    }
  }

  /// Get the size of all stored images in bytes
  static Future<int> getTotalStorageSize() async {
    try {
      final imagesDir = await _getImagesDirectory();
      
      if (!await imagesDir.exists()) return 0;
      
      int totalSize = 0;
      final files = await imagesDir.list().toList();
      
      for (final file in files) {
        if (file is File) {
          final stat = await file.stat();
          totalSize += stat.size;
        }
      }
      
      return totalSize;
    } catch (e) {
      print('Error calculating storage size: $e');
      return 0;
    }
  }

  /// Format storage size in human-readable format
  static String formatStorageSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}
