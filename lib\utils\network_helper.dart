import 'dart:io';
import 'package:flutter/foundation.dart';

class NetworkHelper {
  /// Check if device has internet connectivity
  static Future<bool> hasInternetConnection() async {
    try {
      if (kDebugMode) {
        print('Network Helper: Checking internet connectivity...');
      }
      
      // Try to lookup Google's DNS
      final result = await InternetAddress.lookup('google.com');
      
      if (result.isNotEmpty && result[0].rawAddress.isNotEmpty) {
        if (kDebugMode) {
          print('Network Helper: Internet connection available');
        }
        return true;
      }
      
      if (kDebugMode) {
        print('Network Helper: No internet connection');
      }
      return false;
    } on SocketException catch (e) {
      if (kDebugMode) {
        print('Network Helper: Socket exception - $e');
      }
      return false;
    } catch (e) {
      if (kDebugMode) {
        print('Network Helper: Unexpected error - $e');
      }
      return false;
    }
  }
  
  /// Check if specific host is reachable
  static Future<bool> canReachHost(String hostname) async {
    try {
      if (kDebugMode) {
        print('Network Helper: Checking connectivity to $hostname...');
      }
      
      final result = await InternetAddress.lookup(hostname);
      
      if (result.isNotEmpty && result[0].rawAddress.isNotEmpty) {
        if (kDebugMode) {
          print('Network Helper: Can reach $hostname');
        }
        return true;
      }
      
      if (kDebugMode) {
        print('Network Helper: Cannot reach $hostname');
      }
      return false;
    } on SocketException catch (e) {
      if (kDebugMode) {
        print('Network Helper: Socket exception for $hostname - $e');
      }
      return false;
    } catch (e) {
      if (kDebugMode) {
        print('Network Helper: Unexpected error for $hostname - $e');
      }
      return false;
    }
  }
  
  /// Get network error message for user
  static String getNetworkErrorMessage(dynamic error) {
    final errorString = error.toString().toLowerCase();
    
    if (errorString.contains('socketexception') || 
        errorString.contains('failed host lookup')) {
      return 'Network connection failed. Please check your internet connection and try again.';
    } else if (errorString.contains('timeout')) {
      return 'Connection timeout. Please check your internet connection and try again.';
    } else if (errorString.contains('certificate') || 
               errorString.contains('ssl') || 
               errorString.contains('tls')) {
      return 'Secure connection failed. Please check your network settings.';
    } else {
      return 'Network error occurred. Please try again later.';
    }
  }
  
  /// Retry mechanism for network operations
  static Future<T> retryNetworkOperation<T>(
    Future<T> Function() operation, {
    int maxRetries = 3,
    Duration delay = const Duration(seconds: 2),
  }) async {
    int attempts = 0;
    
    while (attempts < maxRetries) {
      try {
        if (kDebugMode && attempts > 0) {
          print('Network Helper: Retry attempt ${attempts + 1}/$maxRetries');
        }
        
        return await operation();
      } catch (error) {
        attempts++;
        
        if (kDebugMode) {
          print('Network Helper: Attempt $attempts failed - $error');
        }
        
        if (attempts >= maxRetries) {
          if (kDebugMode) {
            print('Network Helper: All retry attempts exhausted');
          }
          rethrow;
        }
        
        // Wait before retrying
        await Future.delayed(delay);
      }
    }
    
    throw Exception('Max retries exceeded');
  }
}
