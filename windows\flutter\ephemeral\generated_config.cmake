# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\src\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "C:\\Users\\<USER>\\OneDrive\\Documents\\Flutter Projects\\tripwisego\\tripwisego" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\src\\flutter"
  "PROJECT_DIR=C:\\Users\\<USER>\\OneDrive\\Documents\\Flutter Projects\\tripwisego\\tripwisego"
  "FLUTTER_ROOT=C:\\src\\flutter"
  "FLUTTER_EPHEMERAL_DIR=C:\\Users\\<USER>\\OneDrive\\Documents\\Flutter Projects\\tripwisego\\tripwisego\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=C:\\Users\\<USER>\\OneDrive\\Documents\\Flutter Projects\\tripwisego\\tripwisego"
  "FLUTTER_TARGET=C:\\Users\\<USER>\\OneDrive\\Documents\\Flutter Projects\\tripwisego\\tripwisego\\lib\\main.dart"
  "DART_DEFINES=RkxVVFRFUl9XRUJfQVVUT19ERVRFQ1Q9dHJ1ZQ==,RkxVVFRFUl9XRUJfQ0FOVkFTS0lUX1VSTD1odHRwczovL3d3dy5nc3RhdGljLmNvbS9mbHV0dGVyLWNhbnZhc2tpdC9jNGNkNDhlMTg2NDYwYjMyZDQ0NTg1Y2UzYzEwMzI3MWFiNjc2MzU1Lw=="
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=C:\\Users\\<USER>\\OneDrive\\Documents\\Flutter Projects\\tripwisego\\tripwisego\\.dart_tool\\package_config.json"
)
